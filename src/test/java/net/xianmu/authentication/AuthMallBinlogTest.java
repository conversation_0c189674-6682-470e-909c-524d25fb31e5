package net.xianmu.authentication;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.common.user.UserBase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class AuthMallBinlogTest {
    @Resource
    private AuthUserProvider authUserAuthProvider;

    @Test
    public void  testCheck(){
        UserBase userBase = new UserBase();
        BaseUserExtend baseUserExtend = new BaseUserExtend();
        authUserAuthProvider.createUser(SystemOriginEnum.COSFO_MALL,userBase,baseUserExtend);
    }
    /*
    请求入参:[["COSFO_MALL",{"phone":"15167199339",
    "status":0,"tenantId":2,"username":"15167199339"},
    {"auditStatus":1,"bizUserId":3555,"lastLoginTime":1682417889000,
    "mpOpenid":"ooWYY44NpdouBVUD6LrqkPp6cR6k","openid":"113123123123asd"}]]
     */
    @Test
    public void  testAdd(){
        UserBase userBase = new UserBase();
        userBase.setPhone("13588400001");
        userBase.setTenantId(2L);
        userBase.setStatus(0);
        userBase.setBizUserId(3781);

        /**
         * [["COSFO_MALL",{"phone":"13588400001","status":0,"tenantId":2,"username":"13588400001"},
         * {"auditStatus":2,"bizUserId":3781,"mpOpenid":"ooWYY4xZtSDXuQaTiZHYbKA4K9_A"}]]
         * rpcId:0.1
         */
        BaseUserExtend baseUserExtend = new BaseUserExtend();
        baseUserExtend.setAuditStatus(2);
        baseUserExtend.setBizUserId(3555L);
       // baseUserExtend.setLastLoginTime(new Date());
        baseUserExtend.setMpOpenid("ooWYY4xZtSDXuQaTiZHYbKA4K9_A");

        authUserAuthProvider.createUser(SystemOriginEnum.COSFO_MALL,userBase,baseUserExtend);
    }

}
