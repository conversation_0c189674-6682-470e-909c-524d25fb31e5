package net.xianmu.authentication;

import cn.hutool.json.JSONUtil;
import com.alibaba.schedulerx.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.user.AuthUserLastUpdatePwdTimeResp;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.common.enmu.LoginTypeEnum;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.model.VO.AuthLoginVO;
import net.xianmu.authentication.service.AuthLoginService;
import net.xianmu.authentication.service.AuthUserService;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.JUnit4;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class SaasSafeLoginTest {


    @Resource
    AuthUserService authUserService;
    @Resource
    AuthLoginService authLoginService;
    @Resource
    AuthBaseUserProvider authBaseUserProvider;
    @Test
    public void  perLogin(){

        //Boolean qazplm12345 = authUserService.checkPhonePassword(SystemOriginEnum.COSFO_MANAGE, "18334345453", "qazplm12345");
        for (int i = 0; i < 6; i++) {
            try {
                authUserService.checkPhonePassword(SystemOriginEnum.COSFO_MANAGE,"13588438640" , "24514");
            }catch (Exception e){
                System.out.println(e);
            }
        }
    }

    @Test
    public void  login(){
        AuthLoginVO authLoginVO = new AuthLoginVO();
        authLoginVO.setTenantId(0L);
        authLoginVO.setPassword("saas59321");
        authLoginVO.setPhone("18224535932");
        authLoginVO.setType(LoginTypeEnum.PHONE_PWD.name);
        authLoginVO.setOrigin(SystemOriginEnum.COSFO_OMS.getType());
        authLoginService.login(authLoginVO).getData();
    }

    @Test
    public void  updatePwd(){

        UserBase baseUser = new UserBase();
        baseUser.setPhone("13588438640");
        baseUser.setPassword("123456789");
        baseUser.setId(10411L);
        authUserService.updateUser(SystemOriginEnum.COSFO_OMS , baseUser);
        //  authUserService.checkPhonePassword(SystemOriginEnum.COSFO_MANAGE, , pwd)
    }

    @Test
    public void  queryLastUpdatePwdTime(){
        AuthUserQueryInput input = new AuthUserQueryInput();
        input.setPhone("18969052048");
        DubboResponse<AuthUserLastUpdatePwdTimeResp> authUserLastUpdatePwdTimeRespDubboResponse = authBaseUserProvider.queryLastUpdatePwdTime(net.xianmu.common.enums.base.auth.SystemOriginEnum.COSFO_OMS, input);
        System.out.println(JSONUtil.toJsonStr(authUserLastUpdatePwdTimeRespDubboResponse));
    }
}
