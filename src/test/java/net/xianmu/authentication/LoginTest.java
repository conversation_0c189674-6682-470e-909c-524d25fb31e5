package net.xianmu.authentication;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.login.AuthMockLoginInput;
import net.xianmu.authentication.model.VO.AuthLoginVO;
import net.xianmu.authentication.provider.impl.MockLoginProviderImpl;
import net.xianmu.authentication.service.AuthLoginService;
import net.xianmu.common.result.CommonResult;
import org.junit.Assert;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.autoconfigure.properties.PropertyMapping;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@ActiveProfiles("qa")
@PropertyMapping
@Slf4j
public class LoginTest {

    @Resource
    private MockLoginProviderImpl mockLoginProvider;
    @Resource
    private AuthLoginService authLoginService;

    @BeforeClass
    public static void setDubboGroup() {
        // 设置dubbo分组为local，本地的服务就不会被调用
        System.setProperty("dubbo.provider.group", "local");
    }

    @Test
    public void crossLoginTest() {
        AuthMockLoginInput var2 = new AuthMockLoginInput();
        var2.setToTenantId(2L);
        var2.setToSystemOriginEnum(6);
        var2.setAuthUserId(6002272L);
        mockLoginProvider.crossLogin(SystemOriginEnum.COSFO_OMS, var2);
    }

    @Test
    public void loginTest() {
        AuthLoginVO authLoginVO = new AuthLoginVO();
        authLoginService.login(authLoginVO);
    }

    @Test
    public void authLoginTest() {
        Integer type = 2;
        String code = "584q2db6da4246c2af12bf2480b6f478";
        Integer systemOrigin = 5;
        String channel = "boss_login";
        authLoginService.authLogin(type, code, systemOrigin, channel);
    }

    @Test
    public void loginWithFeishuUnionId() {
        String unionId = "584q2db6da4246c2af12bf2480b6f478";
        CommonResult<AuthLoginDto> loginDtoCommonResult = authLoginService.loginWithFeishuUnionId(unionId);
        log.info("loginDtoCommonResult: {}", loginDtoCommonResult.getData());
        Assert.assertTrue(null != loginDtoCommonResult && loginDtoCommonResult.getData() != null);
    }
}
