package net.xianmu.authentication;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.provider.AuthUserQueryProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class AuthUserQueryTest {

    @Resource
    AuthUserQueryProvider authUserQueryProvider;
    @Test
    public void test1() {

         AuthUserQueryInput authUserQueryInput = new AuthUserQueryInput();
        authUserQueryInput.setTenantId(1L);
        authUserQueryInput.setAuthUserIds(Arrays.asList(729L,730L,731L,732L));
        DubboResponse<List<AuthUserResp>> listDubboResponse = authUserQueryProvider.queryAuthUserList(SystemOriginEnum.ADMIN, authUserQueryInput);
        System.out.println(JSONUtil.toJsonStr(listDubboResponse));
    }
    @Test
    public void test2() {
        DubboResponse<List<AuthUserResp>> listDubboResponse = authUserQueryProvider.queryByPhones(Arrays.asList("15038278806"));
        System.out.println(JSONUtil.toJsonStr(listDubboResponse));
    }


    @Test
    public void queryUserByRoleIds() {
        DubboResponse<List<AuthUserResp>> listDubboResponse = authUserQueryProvider.queryUserByRoleIds(SystemOriginEnum.ADMIN,1L,Arrays.asList(1L,2L));
        System.out.println(JSONUtil.toJsonStr(listDubboResponse));
    }
}

