package net.xianmu.authentication;

import com.alibaba.schedulerx.common.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.input.wechat.WechatCareQrInput;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.provider.impl.WechatProviderImpl;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class AuthSassTest {
    @Resource
    WechatProviderImpl wechatProvider;
    @Resource
    WxService wxService;
    @Resource
    AuthUserProvider authUserProvider;

    @Test
    public void accessTokenTask() {
        wxService.accessTokenTask();
    }

    @Test
    public void getQR() { //ok
        WechatCareQrInput wechatCareQrInput = new WechatCareQrInput();
        wechatCareQrInput.setPhone("***********");
        wechatCareQrInput.setChannelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        wechatCareQrInput.setSystemOriginEnum(SystemOriginEnum.COSFO_MANAGE);
        wechatCareQrInput.setTenantId(0L);
        wechatCareQrInput.setAuthTypeEnum(AuthTypeEnum.OFFICIAL_WE_CHAT);
        DubboResponse<String> wxCareQr = wechatProvider.getWxCareQr(wechatCareQrInput);
        System.out.println(JsonUtil.toJson(wxCareQr));
    }


    @Test
    public void bindWeChatCare() {// ok
        WechatCareQrInput wechatCareQrInput = new WechatCareQrInput();
        wechatCareQrInput.setPhone("***********");
        wechatCareQrInput.setChannelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        wechatCareQrInput.setSystemOriginEnum(SystemOriginEnum.COSFO_MANAGE);
        wechatCareQrInput.setTenantId(0L);
        wechatCareQrInput.setAuthTypeEnum(AuthTypeEnum.OFFICIAL_WE_CHAT);
        wechatCareQrInput.setOpenId("abcdesfg");
        DubboResponse<Boolean> add = wechatProvider.bindWechatCare(wechatCareQrInput);
        System.out.println(JsonUtil.toJson(add));
    }

    @Test
    public void closeBindByOpenId() {//ok
        WechatCareQrInput wechatCareQrInput = new WechatCareQrInput();
        wechatCareQrInput.setChannelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        wechatCareQrInput.setSystemOriginEnum(SystemOriginEnum.COSFO_MANAGE);
        wechatCareQrInput.setTenantId(0L);
        wechatCareQrInput.setAuthTypeEnum(AuthTypeEnum.OFFICIAL_WE_CHAT);
        wechatCareQrInput.setOpenId("abcdesfg");
        DubboResponse<Boolean> add = wechatProvider.closeWechatCare(wechatCareQrInput);
        System.out.println(JsonUtil.toJson(add));
    }

    @Test
    public void closeBindByPhone() { //ok
        WechatCareQrInput wechatCareQrInput = new WechatCareQrInput();
        wechatCareQrInput.setPhone("***********");
        wechatCareQrInput.setChannelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        wechatCareQrInput.setSystemOriginEnum(SystemOriginEnum.COSFO_MANAGE);
        wechatCareQrInput.setTenantId(0L);
        wechatCareQrInput.setAuthTypeEnum(AuthTypeEnum.OFFICIAL_WE_CHAT);
        DubboResponse<Boolean> add = wechatProvider.closeWechatCare(wechatCareQrInput);
        System.out.println(JsonUtil.toJson(add));
    }

    @Test
    public void queryPhoneAuth() { //ok
        AuthUserAuthQueryInput wechatCareQrInput = new AuthUserAuthQueryInput();
        wechatCareQrInput.setPhones(Arrays.asList("***********","***********"));
        wechatCareQrInput.setChannelCode(WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        wechatCareQrInput.setSystemOriginEnum(SystemOriginEnum.COSFO_MANAGE);
        wechatCareQrInput.setTenantId(0L);
        DubboResponse<List<AuthUserAuthResp>> listDubboResponse = wechatProvider.queryUserRespByPhones(wechatCareQrInput);
        System.out.println(JsonUtil.toJson(listDubboResponse));
    }

    @Test
    public void rolesIds() { //ok
        System.out.println(JsonUtil.toJson(authUserProvider.getUserIdListByRoleIds(Arrays.asList(1L, 2L, 3L))));
    }


}
