package net.xianmu.authentication;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.service.feishu.impl.FeiShuServiceImpl;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class FeishuTest {
    @Resource
    private FeiShuServiceImpl feiShuService;
    @Test
    public void  test(){
        feiShuService.accessTokenTask();
    }
    @Test
    public void  getFeiShuSignature(){
        feiShuService.getFeiShuSignature(SystemOriginEnum.COSFO_OMS,"https://dev3boss.cosfo.cn/index.html");
    }
}
