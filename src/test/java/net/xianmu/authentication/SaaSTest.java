package net.xianmu.authentication;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.dto.wx.WXPhoneResultDTO;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.login.AuthClientLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserUpdateInput;
import net.xianmu.authentication.client.provider.AuthClientLoginProvider;
import net.xianmu.authentication.client.provider.AuthUserQueryProvider;
import net.xianmu.authentication.client.provider.AuthUserUpdateProvider;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class SaaSTest {
    @Resource
    AuthClientLoginProvider authClientLoginProvider;
    @Resource
    AuthUserQueryProvider authUserQueryProvider;
    @Resource
    AuthUserUpdateProvider authUserUpdateProvider;

    /**
     * {"accessToken":"",
     * "accessTokenExpiretime":"2023-09-01T15:20:44.812","appId":"wx85a9fed1e711916e"}
     */
    @Test
    public void  session2code(){
        AuthQueryWechatInfoInput authQueryWechatInfoInput = new AuthQueryWechatInfoInput();
        authQueryWechatInfoInput.setType(AuthTypeEnum.OFFICIAL_WE_CHAT);
        authQueryWechatInfoInput.setMyAppId("wx85a9fed1e711916e");
        authQueryWechatInfoInput.setAppId("wx5e7af9e479322fd9");
        authQueryWechatInfoInput.setCode("0f1XfhIa1bOrZF0TtcJa1Utpye2XfhIm");
        //{"accessToken":"72_Xv-RDlebm2z-cFEKvj2jG9xtFnz19q36lYk50H2mbnO4kuvSeu2WKM7DA4byN0UDCGpxj_4LEkUjGlCKXBF18q0dJ_sI1Hc31bZdRTvGgIBV29-NgQHlIXkZFE0BGHhABAUZA","accessTokenExpiretime":"2023-09-01T15:20:44.812","appId":"wx85a9fed1e711916e"}
        authQueryWechatInfoInput.setAccessToken("72_Xv-RDlebm2z-cFEKvj2jG9xtFnz19q36lYk50H2mbnO4kuvSeu2WKM7DA4byN0UDCGpxj_4LEkUjGlCKXBF18q0dJ_sI1Hc31bZdRTvGgIBV29-NgQHlIXkZFE0BGHhABAUZA");
        authClientLoginProvider.authQueryWechatInfo(authQueryWechatInfoInput);
    }
    //{"accessToken":"","accessTokenExpiretime":"2023-09-01T15:20:44.812","appId":"wx85a9fed1e711916e"}
//
//    @Test
//    public void  authQueryWechatPhone(){
//        AuthQueryWechatInfoInput var1 = new  AuthQueryWechatInfoInput();
//        var1.setAccessToken();
//        var1.setCode();
//        authClientLoginProvider.authQueryWechatPhone(var1);
//    }


    @Test
    public void  queryAuthUserList(){
        AuthUserQueryInput queryInput = new AuthUserQueryInput();
        queryInput.setTenantId(2L);
        queryInput.setBizIds(Arrays.asList(3741L,3742L,3746L,2672L));
        DubboResponse<List<AuthUserResp>> listDubboResponse = authUserQueryProvider.queryAuthUserList(SystemOriginEnum.COSFO_MALL, queryInput);
        System.out.println(JSONUtil.toJsonStr(listDubboResponse));


        AuthUserQueryInput queryInput2 = new AuthUserQueryInput();
        queryInput2.setTenantId(2L);
        queryInput2.setPhone("15856926666");

        DubboResponse<List<AuthUserResp>> listDubboResponse2 = authUserQueryProvider.queryAuthUserList(SystemOriginEnum.COSFO_MALL, queryInput2);
        System.out.println(JSONUtil.toJsonStr(listDubboResponse2));


        AuthUserQueryInput queryInput3 = new AuthUserQueryInput();
        queryInput3.setTenantId(2L);
        queryInput3.setPhone("15856926666");

        DubboResponse<List<AuthUserResp>> listDubboResponse3 = authUserQueryProvider.queryAuthUserList(SystemOriginEnum.COSFO_MALL, queryInput3);
        System.out.println(JSONUtil.toJsonStr(listDubboResponse3));
    }

    @Test
    public void  authClientLogin(){
        AuthClientLoginProviderInput authClientLoginProviderInput = new AuthClientLoginProviderInput();
        authClientLoginProviderInput.setBizId(3783L);
        authClientLoginProviderInput.setTenantId(2L);
        authClientLoginProviderInput.setSystemOriginEnum(net.xianmu.common.enums.base.auth.SystemOriginEnum.COSFO_MALL);
        //authClientLoginProviderInput.setLoginType(AuthTypeEnum.OFFICIAL_WE_CHAT);
        DubboResponse<AuthLoginDto> authLoginDtoDubboResponse = authClientLoginProvider.authClientLogin(authClientLoginProviderInput);
        System.out.println(JSONUtil.toJsonStr(authLoginDtoDubboResponse));

    }
    @Test
    public void  authClientLoginOpenId(){
        AuthClientLoginProviderInput authClientLoginProviderInput2 = new AuthClientLoginProviderInput();
        authClientLoginProviderInput2.setBizId(3783L);
        authClientLoginProviderInput2.setTenantId(2L);
        authClientLoginProviderInput2.setSystemOriginEnum(net.xianmu.common.enums.base.auth.SystemOriginEnum.COSFO_MALL);
        authClientLoginProviderInput2.setOpenid("test-ooWYY4_DEz_gIL0xGtIwqP_W-hIo");
        authClientLoginProviderInput2.setLoginType(AuthTypeEnum.WEI_CHAT);
        DubboResponse<AuthLoginDto> authLoginDtoDubboResponse1= authClientLoginProvider.authClientLogin(authClientLoginProviderInput2);
        System.out.println(JSONUtil.toJsonStr(authLoginDtoDubboResponse1));
    }


    @Test
    public void  loginOut(){
        AuthClientLoginProviderInput authClientLoginProviderInput2 = new AuthClientLoginProviderInput();
        authClientLoginProviderInput2.setToken("abcdes1111111111");
        DubboResponse<Boolean> authLoginDtoDubboResponse1= authClientLoginProvider.loginOut(authClientLoginProviderInput2);
        System.out.println(JSONUtil.toJsonStr(authLoginDtoDubboResponse1));
    }


    @Test
    public void  updateUserBase(){
        AuthUserUpdateInput authUserUpdateInput = new AuthUserUpdateInput();
        authUserUpdateInput.setBizId(3783L);
        authUserUpdateInput.setPhone("10086");
        DubboResponse<Boolean> authLoginDtoDubboResponse1 = authUserUpdateProvider.updateAuthUser(SystemOriginEnum.COSFO_MALL, authUserUpdateInput);
        System.out.println(JSONUtil.toJsonStr(authLoginDtoDubboResponse1));
    }



    @Test
    public void  updateUserBasePwd(){
        AuthUserUpdateInput authUserUpdateInput = new AuthUserUpdateInput();
        authUserUpdateInput.setBizId(3783L);
        authUserUpdateInput.setPhone("10086");
        DubboResponse<Boolean> authLoginDtoDubboResponse1 = authUserUpdateProvider.updateAuthUser(SystemOriginEnum.COSFO_MALL, authUserUpdateInput);
        System.out.println(JSONUtil.toJsonStr(authLoginDtoDubboResponse1));
    }



}
