package net.xianmu.authentication;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.binlog.AuthUserAuthBinlogInput;
import net.xianmu.authentication.client.input.binlog.AuthUserExtBinlogInput;
import net.xianmu.authentication.client.input.permission.PermissionQueryVO;
import net.xianmu.authentication.client.input.user.BaseUserUpdateInput;
import net.xianmu.authentication.client.provider.PermissionQueryProvider;
import net.xianmu.authentication.client.resp.AuthDatePermissionResp;
import net.xianmu.authentication.provider.impl.*;
import net.xianmu.authentication.service.AuthRoleService;
import net.xianmu.authentication.service.InitService;
import net.xianmu.authentication.service.impl.AuthMenuServiceImpl;
import net.xianmu.authentication.service.impl.AuthUserBaseServiceImpl;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.authentication.service.impl.WechatServiceImpl;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.DubboResponse;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;

import javax.annotation.Resource;
import java.util.List;

@SpringBootTest
@Slf4j
class AuthenticationApplicationApplicationTests {
    @Resource
    PermissionQueryProvider permissionQueryProvider;
    @Test
    void testAuthUserAuthProviderImpl() {
       /* PermissionQueryVO permissionQueryVO = new PermissionQueryVO();
        permissionQueryVO.setAuthUserId(925L);
        permissionQueryVO.setType(2);
        DubboResponse<List<AuthDatePermissionResp>> listDubboResponse = permissionQueryProvider.queryUserPermission(permissionQueryVO);
        System.out.println(JSONUtil.toJsonStr(listDubboResponse));
        */


    }
    @Test
    void  testbinLog(){
        String str = "{\"data\":[{\"id\":\"853\",\"store_no\":\"194\",\"store_name\":\"乌鲁木齐城配仓\",\"status\":\"1\",\"manage_admin_id\":\"4\",\"poi_note\":\"87.57593,43.830883\",\"address\":\"新疆维吾尔自治区乌鲁木齐市沙依巴克区红庙子街道阿勒泰路27号\",\"close_order_type\":\"1\",\"origin_store_no\":null,\"sot_finish_time\":\"2023-07-25 22:01:02\",\"creator\":\"4654\",\"updater\":\"1322\",\"update_time\":\"2023-04-28 16:56:07\",\"create_time\":\"2023-04-28 15:05:58\",\"close_time\":\"21:30:00\",\"update_close_time\":null,\"person_contact\":null,\"phone\":null,\"region\":\"其他\"}],\"database\":\"xianmudb\",\"es\":1690293661000,\"id\":34739,\"isDdl\":false,\"mysqlType\":{\"id\":\"int(11)\",\"store_no\":\"int(11)\",\"store_name\":\"varchar(255)\",\"status\":\"int(11)\",\"manage_admin_id\":\"int(11)\",\"poi_note\":\"varchar(255)\",\"address\":\"varchar(255)\",\"close_order_type\":\"int(11)\",\"origin_store_no\":\"int(11)\",\"sot_finish_time\":\"datetime\",\"creator\":\"int(11)\",\"updater\":\"int(11)\",\"update_time\":\"datetime\",\"create_time\":\"datetime\",\"close_time\":\"varchar(60)\",\"update_close_time\":\"varchar(60)\",\"person_contact\":\"varchar(50)\",\"phone\":\"varchar(50)\",\"region\":\"varchar(50)\"},\"old\":[{\"sot_finish_time\":\"2023-07-25 22:00:02\"}],\"pkNames\":[\"id\"],\"sql\":\"\",\"sqlType\":{\"id\":4,\"store_no\":4,\"store_name\":12,\"status\":4,\"manage_admin_id\":4,\"poi_note\":12,\"address\":12,\"close_order_type\":4,\"origin_store_no\":4,\"sot_finish_time\":93,\"creator\":4,\"updater\":4,\"update_time\":93,\"create_time\":93,\"close_time\":12,\"update_close_time\":12,\"person_contact\":12,\"phone\":12,\"region\":12},\"table\":\"warehouse_logistics_center\",\"ts\":1690293661991,\"type\":\"UPDATE\"}";
        //JSONUtil.toBean()
    }

}

