package net.xianmu.authentication;

import cn.hutool.json.JSONUtil;
import com.alibaba.schedulerx.common.util.JsonUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.EnterpriseWeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.client.resp.UserBaseThirdPartyResp;
import net.xianmu.authentication.provider.impl.AuthUserAuthProviderImpl;
import net.xianmu.authentication.provider.impl.EnterpriseWechatProviderImpl;
import net.xianmu.authentication.provider.impl.UserBaseProviderImpl;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.authentication.service.wechat.impl.WxServiceImpl;
import net.xianmu.authentication.service.wechat.qiye.EnterpriseWechatService;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class QiweiTest {
    @Resource
    EnterpriseWechatService enterpriseWechatService;
    @Resource
    EnterpriseWechatProviderImpl enterpriseWechatProvider;
    @Resource
    AuthUserAuthProviderImpl authUserAuthProvider;
    @Resource
    WxService wxService;
    @Resource
    UserBaseProviderImpl userBaseProvider;
    @Test
    public void test1() {
        //enterpriseWechatService.accessTokenTask();
        DubboResponse<List<String>> listDubboResponse = userBaseProvider.queryWechatOpenIdByBizUserId(SystemOriginEnum.SRM, 63L);
        System.out.println(JSONUtil.toJsonStr(listDubboResponse));
    }

    @Test
    public void test2(){
        enterpriseWechatService.updateUserId();
    }


    @Test
    public void test3(){
        wxService.accessTokenTask();
    }

    @Test
    public void  testInit(){
        enterpriseWechatService.updateUserId();
    }

    @Test
    public void  queryAuthUserAuthByInput(){
        AuthUserAuthQueryInput authUserAuthQueryInput = new AuthUserAuthQueryInput();
        authUserAuthQueryInput.setBizUserIds(Arrays.asList(11694L));
        authUserAuthQueryInput.setAuthType(AuthTypeEnum.ENTERPRISE_WE_CHAT);
        authUserAuthQueryInput.setTenantId(1L);
        authUserAuthQueryInput.setSystemOriginEnum(SystemOriginEnum.ADMIN);
        DubboResponse<List<AuthUserAuthResp>> listDubboResponse = authUserAuthProvider.queryAuthUserAuthByInput(authUserAuthQueryInput);
        System.out.println(JSONUtil.toJsonStr(listDubboResponse));
    }

    //
}

