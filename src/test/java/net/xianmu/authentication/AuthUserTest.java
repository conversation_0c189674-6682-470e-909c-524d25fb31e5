package net.xianmu.authentication;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.MD5Util;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.authentication.client.input.user.BaseUserUpdateInput;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.common.enmu.LoginTypeEnum;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.authentication.provider.impl.AuthBaseUserProviderImpl;
import net.xianmu.authentication.provider.impl.UserBaseProviderImpl;
import net.xianmu.authentication.service.feishu.impl.FeiShuServiceImpl;
import net.xianmu.authentication.service.impl.AuthLoginServiceImpl;
import net.xianmu.authentication.service.impl.AuthUserBaseServiceImpl;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/10  16:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class AuthUserTest {
    @Resource
    private AuthLoginServiceImpl authLoginServiceImpl;
    @Resource
    UserBaseProviderImpl userBaseProviderImpl;
    @Resource
    AuthUserServiceImpl authUserService;
    @Resource
    AuthBaseUserProviderImpl authBaseUserProvider;
    @Resource
    AuthUserBaseServiceImpl authUserBaseService;
    @Resource
    AuthUserAuthProvider authUserAuthProvider;

    @Test
    public void test1() {
        AuthUserAuthQueryInput var1 = new AuthUserAuthQueryInput();
        var1.setSystemOriginEnum(SystemOriginEnum.MALL);
        var1.setAuthType(AuthTypeEnum.WEI_CHAT);
        var1.setOpenId("oZY8K49QdInOHPjR8EYoRQFV9aaM");
        DubboResponse<List<AuthUserAuthResp>> listDubboResponse = authUserAuthProvider.queryAuthUserAuthByAuthId(var1);
        System.out.println(JSONUtil.toJsonStr(listDubboResponse));
    }

    @Test
    public void test2() {
        String s = "{\"password\":\"63m8eQ33\",\"phone\":\"13002021033\",\"username\":\"13002021033\"}";
        UserBase userBase = JSONObject.parseObject(s, UserBase.class);
        authUserService.createUser(SystemOriginEnum.SRM, userBase, false);
    }

    /**
     *     public UserBase updateUser(SystemOriginEnum systemOriginEnum, UserBase baseUser) {
     */




}
