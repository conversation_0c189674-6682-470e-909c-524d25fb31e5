package net.xianmu.authentication;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.AuthThirdPartyInput;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.service.AuthLoginService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/10  16:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class AuthUserAuthTest {
    @Resource
    private AuthUserAuthProvider authUserAuthProvider;

    @Test
    public void test1() {
        List<String> query = new ArrayList<>();
        query.add("XM00133");
//        query.add("XM00928");
//        query.add("XM00558");
//        query.add("XM00345");
        authUserAuthProvider.queryUserBaseByThirdBatch(SystemOriginEnum.ADMIN, AuthTypeEnum.FEI_SHU, 1L, query);
    }

    @Test
    public void test2() {
        AuthThirdPartyInput input = new AuthThirdPartyInput();
        input.setTenantId(1L);
        input.setAccountType(AuthThirdPartyInput.AccountTypeEnum.BIZ_USER_ID);
        List<String> aList = new ArrayList<>();
//        aList.add("***********");
//        aList.add("***********");
        aList.add("10447");
//        aList.add("10320");
//        aList.add("5149");
//        aList.add("5151");
        input.setAccountList(aList);
        authUserAuthProvider.queryThirdPartyBatch(SystemOriginEnum.ADMIN, AuthTypeEnum.FEI_SHU, input);
    }

    @Resource
    private AuthLoginService authLoginService;
    @Test
    public void test3(){
        authLoginService.authLogin(2, "d288da61");
    }
}
