package net.xianmu.authentication;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;
import net.xianmu.authentication.client.provider.AuthClientLoginProvider;
import net.xianmu.authentication.client.provider.WechatProvider;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.authentication.service.feishu.impl.FeiShuServiceImpl;
import net.xianmu.authentication.service.wechat.impl.WxServiceImpl;
import net.xianmu.authentication.task.SrmTicketUpdateJob;
import net.xianmu.common.enums.base.auth.WechatMiniProgramChannelEnum;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.result.DubboResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/10  16:27
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class TokenTest {
    @Resource
    private FeiShuServiceImpl feiShuService;
    @Resource
    WxServiceImpl wxService;
    @Resource
    WechatProvider wechatProvider;
    @Resource
    private SrmTicketUpdateJob srmTicketUpdateJob;

    @Test
    public void refreshFeishuToken() {
        feiShuService.accessTokenTask();
    }

    @Test
    public void test2() {
        List<AuthUserPhoneDTO> strings = new ArrayList<>();
        AuthUserPhoneDTO userBase = new AuthUserPhoneDTO();
        userBase.setId(6071L);
        userBase.setPhone("13071250438");
        strings.add(userBase);
        feiShuService.batchUpdateUserId(strings);
    }

    @Test
    public void test3() {
        wxService.accessTokenTask();
    }

    @Resource
    AuthClientLoginProvider authClientLoginProvider;

    @Test
    public void test4() {
        AuthQueryWechatInfoInput authQueryWechatInfoInput = new AuthQueryWechatInfoInput();
        authQueryWechatInfoInput.setType(AuthTypeEnum.WEI_CHAT);
        authQueryWechatInfoInput.setCode("acbd");
        DubboResponse<AuthQueryWechatInfoDTO> authQueryWechatInfoDTODubboResponse = authClientLoginProvider.authMallQueryWechatInfo(authQueryWechatInfoInput);
        System.out.println(authQueryWechatInfoDTODubboResponse);
    }

    @Test
    public void getToken() {
        DubboResponse<String> stringDubboResponse = wechatProvider.queryWeChatToken(WechatMiniProgramChannelEnum.XM_MALL_MP.channelCode);
        System.out.println(JSONUtil.toJsonStr(stringDubboResponse));

        DubboResponse<String> stringDubboResponse2 = wechatProvider.queryWeChatToken(WxOfficialAccountsChannelEnum.XM_MALL.channelCode);
        System.out.println(JSONUtil.toJsonStr(stringDubboResponse2));
    }

    @Test
    public void getTicket() {
        DubboResponse<String> stringDubboResponse = wechatProvider.queryWeChatTicket(WxOfficialAccountsChannelEnum.XM_MALL.channelCode);
        System.out.println(JSONUtil.toJsonStr(stringDubboResponse));

    }

    @Test
    public void accessTokenTest() throws Exception {
        srmTicketUpdateJob.processResult(null);
    }
}
