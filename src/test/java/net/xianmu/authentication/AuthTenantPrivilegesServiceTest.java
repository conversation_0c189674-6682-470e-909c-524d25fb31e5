package net.xianmu.authentication;

import lombok.extern.slf4j.Slf4j;

import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.client.input.purview.TenantPrivilegesInput;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AuthenticationApplication.class)
@Slf4j
public class AuthTenantPrivilegesServiceTest {
    @Resource
    private AuthTenantPrivilegesService authTenantPrivilegesService;

    @Test
    public void test(){
        AuthTenantPrivilegesInput authTenantPrivilegesInput = new AuthTenantPrivilegesInput();
        authTenantPrivilegesInput.setTenantId(1L);
        authTenantPrivilegesInput.setSystemOrigin(SystemOriginEnum.ADMIN.type);
        List<TenantPrivilegesInput> tenantPrivilegesInputs = new ArrayList<>();
        TenantPrivilegesInput input1 = new TenantPrivilegesInput();
        input1.setExpireTime(new Date());
        input1.setMenuId(1l);
        tenantPrivilegesInputs.add(input1);

        TenantPrivilegesInput input2 = new TenantPrivilegesInput();
        input2.setExpireTime(new Date());
        input2.setMenuId(2l);
        tenantPrivilegesInputs.add(input2);

        TenantPrivilegesInput input3 = new TenantPrivilegesInput();
        input3.setExpireTime(new Date());
        input3.setMenuId(3l);
        tenantPrivilegesInputs.add(input3);
        authTenantPrivilegesInput.setTenantPrivilegesInputs(tenantPrivilegesInputs);
        authTenantPrivilegesService.addTenantPrivileges(authTenantPrivilegesInput);
    }
}
