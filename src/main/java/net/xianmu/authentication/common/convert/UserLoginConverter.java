package net.xianmu.authentication.common.convert;

import net.xianmu.authentication.client.dto.login.AuthUserCommonLoginVO;
import net.xianmu.authentication.model.VO.AuthLoginVO;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/4/23 15:19
 */

public class UserLoginConverter {

    public static AuthLoginVO toAuthLoginVO (AuthUserCommonLoginVO vo) {

        if (vo == null) {
            return null;
        }
        AuthLoginVO authLoginVO = new AuthLoginVO();
        authLoginVO.setPhone(vo.getPhone());
        authLoginVO.setPassword(vo.getPassword());
        authLoginVO.setType(vo.getType());
        authLoginVO.setUsername(vo.getUsername());
        authLoginVO.setMessageCode(vo.getMessageCode());
        authLoginVO.setTenantId(vo.getTenantId());
        authLoginVO.setOrigin(vo.getOrigin());
        authLoginVO.setToken(vo.getToken());
        authLoginVO.setSuperMan(vo.isSuperMan());
        return authLoginVO;
    };

}
