package net.xianmu.authentication.common.convert;

import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.model.BO.AuthUserBO;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

public class AuthUserRespConvert {


    private AuthUserRespConvert() {
        // 无需实现
    }

    public static List<AuthUserResp> toAuthUserRespList(List<AuthUser> authUserList) {
        if (authUserList == null) {
            return Collections.emptyList();
        }
        List<AuthUserResp> authUserRespList = new ArrayList<>();
        for (AuthUser authUser : authUserList) {
            authUserRespList.add(toAuthUserResp(authUser));
        }
        return authUserRespList;
    }

    public static AuthUserResp toAuthUserResp(AuthUser authUser) {
        if (authUser == null) {
            return null;
        }
        AuthUserResp authUserResp = new AuthUserResp();
        authUserResp.setId(authUser.getId());
        authUserResp.setUserBaseId(authUser.getUserBaseId());
        authUserResp.setTenantId(authUser.getTenantId());
        authUserResp.setCreateTime(authUser.getCreateTime());
        authUserResp.setStatus(authUser.getStatus());
        authUserResp.setSystemOrigin(authUser.getSystemOrigin());
        authUserResp.setUpdateTime(authUser.getUpdateTime());
        authUserResp.setBizUserId(authUser.getBizUserId());
        authUserResp.setPassword(authUser.getPassword());
        authUserResp.setLastLoginTime(authUser.getLastLoginTime());
        authUserResp.setAuditStatus(authUser.getAuditStatus());
// Not mapped TO fields:
// openid
// mpOpenid
// unionid
        return authUserResp;
    }

    public static List<AuthUser> toAuthUserList(List<AuthUserResp> authUserRespList) {
        if (authUserRespList == null) {
            return Collections.emptyList();
        }
        List<AuthUser> authUserList = new ArrayList<>();
        for (AuthUserResp authUserResp : authUserRespList) {
            authUserList.add(toAuthUser(authUserResp));
        }
        return authUserList;
    }

    public static AuthUser toAuthUser(AuthUserResp authUserResp) {
        if (authUserResp == null) {
            return null;
        }
        AuthUser authUser = new AuthUser();
        authUser.setId(authUserResp.getId());
        authUser.setUserBaseId(authUserResp.getUserBaseId());
        authUser.setTenantId(authUserResp.getTenantId());
        authUser.setCreateTime(authUserResp.getCreateTime());
        authUser.setStatus(authUserResp.getStatus());
        authUser.setSystemOrigin(authUserResp.getSystemOrigin());
        authUser.setUpdateTime(authUserResp.getUpdateTime());
        authUser.setBizUserId(authUserResp.getBizUserId());
        authUser.setPassword(authUserResp.getPassword());
        authUser.setLastLoginTime(authUserResp.getLastLoginTime());
        authUser.setAuditStatus(authUserResp.getAuditStatus());
// Not mapped FROM fields:
// openid
// mpOpenid
// unionid
        return authUser;
    }


    public static AuthUserResp toAuthUserResp(AuthUserBO authUser) {
        if (authUser == null) {
            return null;
        }
        AuthUserResp authUserResp = new AuthUserResp();
        authUserResp.setId(authUser.getId());
        authUserResp.setUserBaseId(authUser.getUserBaseId());
        authUserResp.setTenantId(authUser.getTenantId());
        authUserResp.setCreateTime(authUser.getCreateTime());
        authUserResp.setStatus(authUser.getStatus());
        authUserResp.setSystemOrigin(authUser.getSystemOrigin());
        authUserResp.setUpdateTime(authUser.getUpdateTime());
        authUserResp.setBizUserId(authUser.getBizUserId());
        authUserResp.setPassword(authUser.getPassword());
        authUserResp.setLastLoginTime(authUser.getLastLoginTime());
        authUserResp.setAuditStatus(authUser.getAuditStatus());
        authUserResp.setPhone(authUser.getPhone());
        String roleIdsStr = authUser.getRoleIds();
        if (!StringUtils.isEmpty(roleIdsStr)){
            List<Long> roleIds = Arrays.stream(roleIdsStr.split(",")).map(Long::valueOf).collect(Collectors.toList());
            authUserResp.setRoleIds(roleIds);
        }
// Not mapped TO fields:
// openid
// mpOpenid
// unionid
        return authUserResp;
    }
}
