package net.xianmu.authentication.common.enmu;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;


@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum LoginTypeEnum {

    /**
     * 手机号密码
     */
    PHONE_PWD("PHONE_PWD", "手机号密码"),
    /**
     * 二次token登陆
     */
    SECOND_TOKEN("SECOND_TOKEN", "二次token登陆"),

    /**
     * 账号密码
     */
    NAME_PWD("NAME_PWD", "账号密码"),


    CROSS_LOGIN("CROSS_LOGIN", "跨域登陆"),


    /**
     * 手机验证码
     */
    PHONE_MESSAGE("PHONE_MESSAGE", "手机验证码");



    public final String name;
    public final String desc;


    public static LoginTypeEnum getLoginType(String name) {
        LoginTypeEnum[] values = values();
        for (LoginTypeEnum value : values) {
            if (value.name.equals(name)) {
                return value;
            }
        }
        return null;
    }
}
