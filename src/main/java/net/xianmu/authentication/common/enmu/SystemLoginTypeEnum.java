package net.xianmu.authentication.common.enmu;

import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.common.config.XianMuWxConfig;
import net.xianmu.authentication.common.util.SpringContextUtil;
import net.xianmu.authentication.enums.AuthLoginTypeEnum;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;

import java.util.Arrays;
import java.util.Objects;

public enum  SystemLoginTypeEnum {
    SRM_WX(SystemOriginEnum.SRM, AuthLoginTypeEnum.WX_MINI_APP, "wx2622a2d99057d825", "8c3b945da6b425d1158962f323c4e20a"),
    TMS_WX(SystemOriginEnum.TMS, AuthLoginTypeEnum.WX_MINI_APP, "wxacf770005a3c8389", "12fd447426bee1cbb061ebc4b799b081"),
    CRM_WX(SystemOriginEnum.CRM, AuthLoginTypeEnum.WX_MINI_APP, "wxef11bcb5e136e2d2", "e5f7e466135806e5a6401557b731d5b6");

    private final SystemOriginEnum systemOriginEnum;
    private final AuthLoginTypeEnum authLoginTypeEnum;
    private final String appId;
    private final String appSecret;

    public static SystemLoginTypeEnum getSupportedSystemLoginType(SystemOriginEnum systemOriginEnum, AuthLoginTypeEnum authLoginTypeEnum) {
        if (systemOriginEnum == null) {
            throw new DefaultServiceException("不支持的系统来源");
        } else if (authLoginTypeEnum == null) {
            throw new DefaultServiceException("不支持的授权登录类型");
        } else {
            return Arrays.stream(values()).filter((e) -> systemOriginEnum.equals(e.getSystemOriginEnum()) && authLoginTypeEnum.equals(e.getAuthLoginTypeEnum())).findFirst().orElseThrow(() -> new DefaultServiceException("不支持的系统登录类型"));
        }
    }

    public SystemOriginEnum getSystemOriginEnum() {
        return this.systemOriginEnum;
    }

    public AuthLoginTypeEnum getAuthLoginTypeEnum() {
        return this.authLoginTypeEnum;
    }

    public String getAppId() {
        if(Objects.equals(this.systemOriginEnum, SystemOriginEnum.SRM)){
            XianMuWxConfig config =   (XianMuWxConfig)SpringContextUtil.getBean("xianMuWxConfig");
            return config.getSrmWxMiniAppId();
        }
        return this.appId;
    }

    public String getAppSecret() {
        if(Objects.equals(this.systemOriginEnum, SystemOriginEnum.SRM)){
            XianMuWxConfig config =   (XianMuWxConfig)SpringContextUtil.getBean("xianMuWxConfig");
            return config.getSrmWxMiniSecret();
        }
        return this.appSecret;
    }

    SystemLoginTypeEnum(final SystemOriginEnum systemOriginEnum, final AuthLoginTypeEnum authLoginTypeEnum, final String appId, final String appSecret) {
        this.systemOriginEnum = systemOriginEnum;
        this.authLoginTypeEnum = authLoginTypeEnum;
        this.appId = appId;
        this.appSecret = appSecret;
    }
}
