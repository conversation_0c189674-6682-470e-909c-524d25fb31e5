package net.xianmu.authentication.common.enmu;


import lombok.AccessLevel;
import lombok.AllArgsConstructor;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.common.exception.BizException;


@AllArgsConstructor(access = AccessLevel.PRIVATE)
public enum PermissionTypeEnum {

    /**
     * 城市
     */
    CITY(0, "城市"),
    /**
     * 仓库
     */
    WAREHOUSE(1, "仓库"),

    /**
     * 城配仓
     */
    CITY_WAREHOUSE(2, "城配仓库");



    public final Integer code;
    public final String desc;


    public static PermissionTypeEnum getByType(Integer type) {
        PermissionTypeEnum[] values = values();
        for (PermissionTypeEnum value : values) {
            if (value.code.equals(type)) {
                return value;
            }
        }
        throw new DefaultServiceException("错误的参数类型");
    }
}
