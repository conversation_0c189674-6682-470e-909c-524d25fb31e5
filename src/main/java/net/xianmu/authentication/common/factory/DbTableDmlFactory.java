package net.xianmu.authentication.common.factory;


import net.xianmu.authentication.common.DbTableDml;

import java.util.Map;

/**
 * <AUTHOR>
 * @Description dts订阅表工厂类,增加新表是需要在ManageApplication.dbTableDmlFactory中注册
 * @date 2022/4/22 10:03
 */
public class DbTableDmlFactory {
    /**
     * 注册器:存储表名与实现类关系
     */
    private Map<String, DbTableDml> dbTableDmlMap;

    public void setDbTableDmlMap(Map<String, DbTableDml> dbTableDmlMap) {
        this.dbTableDmlMap = dbTableDmlMap;
    }

    /**
     * 通过注册好的表名,获取实现类对象
     * @param dbTableNameEnum 表名
     * @return 实现类对象
     */
    public DbTableDml creator(String dbTableNameEnum){
        return dbTableDmlMap.get(dbTableNameEnum);
    }
}
