package net.xianmu.authentication.common;

/**
 * sg
 */
public class AuthGlobal {
    // 没有租户的默认租户id
    public static final Long TENANT_ID = 1L;


    /**
     * 明文密码
     */
    public static final String PLAINTEXT_PASSWORD = "PLAINTEXT_PASSWORD";
    /**
     * 上次修改密码时间
     */
    public static final String LAST_UPDATE_PWD_TIME = "LAST_UPDATE_PWD_TIME";




    /**
     * 企业微信token前缀
     */
    public static final String ENTERPRISE_WECHAT_REDIS = "enterprise:wechat:";
    /**
     * 微信token前缀
     */
    public static final String WECHAT_REDIS = "wechat:%s:%s";
    /**
     * 微信token ticket前缀
     */
    public static final String WECHAT_TICKET_REDIS = "wechat:ticket:%s";

    public static final String VERIFY_PHONE = "^[1][0-9]{10}$";

    public static final String ALL = "-1";

    /**
     * 表名称
     */
    public static final String WAREHOUSE_LOGISTICS_CENTER_TABLE = "warehouse_logistics_center";



    public static final String PWD_PREFIX = "init_";


    public static final String QW_BD_QR = "QW_BD_QR";

    public static final String TRUE = "true";



    public static final String AUTH_BIND_REDIS_KEY = "auth:user:lock:%s:%s";
}
