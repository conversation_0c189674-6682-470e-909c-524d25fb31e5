/*
package net.xianmu.authentication.common.exception;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

*/
/**
 * Description: 全局异常处理类
 * date: 2022/6/14 13:31
 *
 * <AUTHOR>
 *//*

@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandlerAdvice {

    */
/**
     * Form表单参数验证拦截
     * @param e
     * @return the AjaxResult
     *//*

    @ExceptionHandler(BindException.class)
    public AjaxResult bindExceptionHandler(BindException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String msg = fieldErrors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(","));
        return AjaxResult.getErrorWithMsg(msg);
    }

    */
/**
     * RequestParam等单参数验证拦截
     * @param e
     * @return the AjaxResult
     *//*

    @ExceptionHandler(ConstraintViolationException.class)
    public AjaxResult constraintViolationException(ConstraintViolationException e) {
        Set<ConstraintViolation<?>> constraintViolations = e.getConstraintViolations();
        String msg = constraintViolations.stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(","));
        return AjaxResult.getErrorWithMsg(msg);
    }

    */
/**
     * RequestBody请求体验证拦截
     * @param e
     * @return the AjaxResult
     *//*

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public AjaxResult methodArgumentNotValidException(MethodArgumentNotValidException e) {
        List<FieldError> fieldErrors = e.getBindingResult().getFieldErrors();
        String msg = fieldErrors.stream()
                .map(DefaultMessageSourceResolvable::getDefaultMessage)
                .collect(Collectors.joining(","));
        // 然后提取错误提示信息进行返回
        return AjaxResult.getErrorWithMsg(msg);
    }

    */
/**
     * 服务异常拦截
     * @param e
     * @return the AjaxResult
     *//*

    @ExceptionHandler(DefaultServiceException.class)
    public AjaxResult defaultServiceException(DefaultServiceException e) {
        if (StrUtil.isNotBlank(e.getCode()) && ResultConstant.UNBIND.equals(e.getCode())) {
            return AjaxResult.getError(e.getCode(),(Object) e.getMessage());
        }
        log.error("DefaultServiceException异常",e);
        if (StrUtil.isBlank(e.getCode())){
            return AjaxResult.getErrorWithMsg(e.getMessage());
        }
        return AjaxResult.getError(e.getCode());

    }

    @ExceptionHandler(Exception.class)
    public AjaxResult systemException(Exception e) {
        log.error("系统异常",e);
        return AjaxResult.getErrorWithMsg("系统异常");

    }
}
*/
