package net.xianmu.authentication.common.exception;

import com.alibaba.fastjson.JSON;
import io.micrometer.core.instrument.util.StringUtils;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.common.exception.*;
import net.xianmu.common.exception.error.code.ProviderErrorCode;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.dubbo.support.constant.DubboCommonConstant;
import net.xianmu.i18n.result.util.I18nDubboResponseUtil;
import net.xianmu.i18n.util.XianmuI18nUtil;
import net.xianmu.log.helper.ParamExceptionHelper;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.shiro.authz.AuthorizationException;
import org.apache.shiro.session.UnknownSessionException;
import org.aspectj.lang.ProceedingJoinPoint;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Component;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.io.IOException;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static net.xianmu.common.result.ResultStatusEnum.BAD_REQUEST;

/**
 * @Package: net.summerfarm.common.exceptions
 * @Description: 自定义异常处理类
 * @author: <EMAIL>
 * @Date: 2016/7/27
 */
@RestControllerAdvice
@Component
public class CustomExceptionHandler implements net.xianmu.dubbo.support.handle.ExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(CustomExceptionHandler.class);


    @ExceptionHandler(Exception.class)
    public ModelAndView resolveException(HttpServletRequest httpServletRequest, HttpServletResponse httpServletResponse,
                                         Object o, Exception e) {
        ModelAndView modelAndView = new ModelAndView();

        AjaxResult result;

        httpServletResponse.setCharacterEncoding("UTF-8");
        httpServletResponse.setHeader("Content-Type", "application/json;charset=UTF-8");
        if (e instanceof ClientAbortException) {
            logger.info("【警告】message=[{}]", e.getMessage(), e);
            result = AjaxResult.getErrorWithMsg("网络不给力");
        }else if (ParamExceptionHelper.checkSupport(e)) {
            CommonResult commonResult = ParamExceptionHelper.dealException(e);
            logger.warn("【警告】message=[{}]", commonResult.getMsg(), e);
            result = AjaxResult.getErrorWithMsg(commonResult.getMsg());
        } else if (e instanceof BindException) {
            logger.info("【警告】message=[{}]", e.getMessage(), e);
            BindException exception = (BindException) e;
            List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
            String msg = fieldErrors.stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.joining(","));
            result = AjaxResult.getErrorWithMsg(msg);
        } else if (e instanceof ConstraintViolationException) {
            logger.info("【警告】message=[{}]", e.getMessage(), e);
            ConstraintViolationException exception = (ConstraintViolationException) e;
            Set<ConstraintViolation<?>> constraintViolations = exception.getConstraintViolations();
            String msg = constraintViolations.stream()
                    .map(ConstraintViolation::getMessage)
                    .collect(Collectors.joining(","));
            result = AjaxResult.getErrorWithMsg(msg);
        } else if (e instanceof MethodArgumentNotValidException) {
            logger.info("【警告】message=[{}]", e.getMessage(), e);
            MethodArgumentNotValidException exception = (MethodArgumentNotValidException) e;
            List<FieldError> fieldErrors = exception.getBindingResult().getFieldErrors();
            String msg = fieldErrors.stream()
                    .map(DefaultMessageSourceResolvable::getDefaultMessage)
                    .collect(Collectors.joining(","));
            // 然后提取错误提示信息进行返回
            result = AjaxResult.getErrorWithMsg(msg);
        } else if (e instanceof ParamsException) {
            ParamsException exception = (ParamsException) e;
            logger.warn("调用方参数异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof BizException) {
            BizException exception = (BizException) e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof CallerException) {
            CallerException exception = (CallerException) e;
            logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof ProviderException) {
            ProviderException exception = (ProviderException) e;
            logger.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            result = AjaxResult.getError(exception.getErrorCode().getCode(), exception.getMessage());
        } else if (e instanceof AuthorizationException) {
            //跨域设置
            httpServletResponse.addHeader("Access-Control-Allow-Origin", "*");
            httpServletResponse.addHeader("Access-Control-Allow-Methods", "POST, GET");
            httpServletResponse.addHeader("Access-Control-Allow-Headers", "*");
            httpServletResponse.addHeader("Access-Control-Allow-Credentials", "true");
            //无访问权限设置http请求status为403
            httpServletResponse.setStatus(403);
            result = AjaxResult.getError(ResultConstant.UNAUTHORIZED, "暂无该功能权限,请联系主管操作");
        } else if (e instanceof DefaultServiceException) {
            Object[] params = ((DefaultServiceException) e).getParams();
            if (((DefaultServiceException) e).getLevel() == 0) {
                logger.warn("调用方异常, 异常信息:{}", e.getMessage(), e);
            } else {
                logger.error("提供方异常, 异常信息:{}", e.getMessage(), e);
            }
            if (Objects.isNull(params)) {
                //兼容模式如果e.getMessage()包含中文判断使用哪种构造函数
                //若e.getMessage()字节码长度等于本身长度则不包含中文
                if (StringUtils.isNotBlank(e.getMessage()) && e.getMessage().getBytes().length != e.getMessage().length()) {
                    result = AjaxResult.getErrorWithMsg(e.getMessage());
                } else {
                    result = AjaxResult.getError(e.getMessage());
                }
            } else {
                result = AjaxResult.getErrorWithParam(e.getMessage(), params);
            }
        } else if (e instanceof DuplicateKeyException) {
            logger.info("异常:{}", e.getMessage());
            result = AjaxResult.getErrorWithMsg("该信息已存在,请确认!");
        } else if (e instanceof UnknownSessionException) {
            logger.info("登录超时,msg:{}", e.getMessage(), e);
            result = AjaxResult.getError(ResultConstant.UNAUTHORIZED, "登录超时，请重新登录");
        } else {
            logger.error("系统异常:{}", e);
            result = AjaxResult.getError(ResultConstant.DEFAULT_FAILED, "系统异常");
        }
        try {
            result.setMsg(XianmuI18nUtil.getI18nValue(result.getMsg()));
            httpServletResponse.getWriter().write(JSON.toJSONString(result));
        } catch (IOException ex) {
            logger.info("与客户端通信异常，{}", ex.getMessage());
        }

        return modelAndView;
    }

    @Override
    public DubboResponse processError(Throwable throwable, ProceedingJoinPoint joinPoint) {
        if (throwable instanceof ConsumerException) {
            ConsumerException exception = (ConsumerException) throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else if (throwable instanceof ParamsException) {
            ParamsException exception = (ParamsException) throwable;
            logger.warn("调用方参数异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else if (throwable instanceof BizException) {
            BizException exception = (BizException) throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else if (throwable instanceof CallerException) {
            CallerException exception = (CallerException) throwable;
            logger.warn("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else if (throwable instanceof DefaultServiceException) {
            DefaultServiceException exception = (DefaultServiceException) throwable;
            if (exception.getLevel() == 0) {
                logger.info("调用方异常, 异常信息:{}", throwable.getMessage(), throwable);
            } else {
                logger.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            }
            return I18nDubboResponseUtil.getError(exception.getCode(), exception.getMessage(), null);
        } else if (throwable instanceof ProviderException) {
            ProviderException exception = (ProviderException) throwable;
            logger.error("提供方异常, 异常信息:{}", throwable.getMessage(), throwable);
            return I18nDubboResponseUtil.getError(exception.getErrorCode().getCode(), exception.getMessage(), null);
        } else {
            logger.error("提供方未知异常, 异常信息:{}", throwable.getMessage(), throwable);
            ProviderErrorCode providerErrorCode = new ProviderErrorCode(DubboCommonConstant.UNDEFINED_EXCEPTION_CODE);
            return I18nDubboResponseUtil.getError(providerErrorCode.getCode(), throwable.getMessage(), null);
        }
    }
}
