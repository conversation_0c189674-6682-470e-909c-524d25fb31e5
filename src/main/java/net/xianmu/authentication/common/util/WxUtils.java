package net.xianmu.authentication.common.util;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.model.BO.WxOpenIdBO;
import net.xianmu.common.exception.BizException;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

@Slf4j
@Component
public class WxUtils {

    @Resource
    RestTemplate restTemplate;

    //公众号
    public static final String WX_OA_USER_BASE_INFO= "https://api.weixin.qq.com/sns/oauth2/access_token?appid=%s&secret=%s&code=%s&grant_type=authorization_code";

    //小程序
    public static final String WX_USER_BASE_INFO= "https://api.weixin.qq.com/sns/jscode2session?appid=%s&secret=%s&js_code=%s&grant_type=authorization_code";

    // 微信小程序接口基础 URL
    private static final String BASE_URL = "https://api.weixin.qq.com";
    // 获取商品的最近平均使用情况接口
    private static final String ENDPOINT = "/wxa/charge/usage/get_recent_average";

    /**
     * 获取微信公众号信息
     *
     * @param code
     * @return
     */
    public  WxOpenIdBO getOaOpenId(AuthTypeEnum authTypeEnum, String code, String appId, String appSecret) {
        if (!Arrays.asList(AuthTypeEnum.WEI_CHAT.getType(), AuthTypeEnum.OFFICIAL_WE_CHAT.getType()).contains(authTypeEnum.getType())) {
            throw new BizException("错误的微信来源");
        }


        String baseUrl = Objects.equals(authTypeEnum.getType(), AuthTypeEnum.WEI_CHAT.getType()) ? WX_USER_BASE_INFO : WX_OA_USER_BASE_INFO;
        String url = String.format(baseUrl, appId, appSecret, code);
        log.info("获取微信小程序的ou信息, authTypeEnum{} code{}, appId{}, appSecret{}", authTypeEnum.getType(), code, appId, appSecret);
        String result = restTemplate.getForObject(url, String.class);
        if (StringUtils.isBlank(result)) {
            log.info("获取微信小程序的ou信息, authTypeEnum{} code{}, appId{}, appSecret{}, result {}", authTypeEnum.getType(), code, appId, appSecret, result);
            return null;
        }
        log.info(result);
        JSONObject userInfo = JSONObject.parseObject(result);
        if (StringUtils.isNotBlank(userInfo.getString("errcode"))) {
            log.error("获取openid错误");
            return null;
        }
        WxOpenIdBO wxOpenIdBO = new WxOpenIdBO();
        wxOpenIdBO.setOpenid(userInfo.getString("openid"));
        wxOpenIdBO.setUnionid(userInfo.getString("unionid"));
        return wxOpenIdBO;
    }


    /**
     * 调用微信接口，获取商品的最近平均使用情况
     *
     * @param accessToken 接口调用凭证
     * @param spuId       商品SPU ID
     * @return 接口返回的 JSON 字符串
     */
    public String getRecentAverageUsage(String accessToken, String spuId) {
        String url = String.format("%s%s?access_token=%s&spuId=%s", BASE_URL, ENDPOINT, accessToken, spuId);

        // 创建 RestTemplate 实例
        RestTemplate restTemplate = new RestTemplate();

        // 发送 GET 请求
        ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

        // 返回结果
        return response.getBody();
    }
}
