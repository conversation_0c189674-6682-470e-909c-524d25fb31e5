package net.xianmu.authentication.common.util;

import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNodeConfig;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.json.JSONUtil;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.dto.AuthMenuPurviewDto;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

public class MenuTreeUtils {

    public static List<AuthMenuPurviewDto> convertTree(List<AuthMenuPurview> menuPreviews) {
        if (CollectionUtils.isEmpty(menuPreviews)) {
            return new ArrayList<>();
        }
        TreeNodeConfig config = new TreeNodeConfig();
        config.setWeightKey("weight");
        config.setParentIdKey("parentId");
        config.setDeep(8);
        config.setChildrenKey("children");
        config.setIdKey("id");
        List<Tree<String>> treeList = TreeUtil.build(menuPreviews, "0", config, (treeNode, tree) -> {
            tree.setId(treeNode.getId().toString());
            tree.setParentId(treeNode.getParentId().toString());
            tree.setName(treeNode.getMenuName());
            tree.setWeight(treeNode.getWeight());
            tree.putExtra("description", treeNode.getDescription());
            tree.putExtra("type", treeNode.getType());
            tree.putExtra("createTime", treeNode.getCreateTime());
            tree.putExtra("updateTime", treeNode.getUpdateTime());
            tree.putExtra("url", treeNode.getUrl());
            tree.putExtra("sonPurview", treeNode.getSonPurview());
            tree.putExtra("purviewName", treeNode.getPurviewName());
            tree.putExtra("menuName", treeNode.getMenuName());
            tree.putExtra("expireTime", treeNode.getExpireTime());

        });
        return JSONUtil.toList(JSONUtil.toJsonStr(treeList), AuthMenuPurviewDto.class);
    }
}
