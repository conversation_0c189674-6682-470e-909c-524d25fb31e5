package net.xianmu.authentication.common.util;

import net.xianmu.common.input.PageSortInput;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

public class SqlUtils {

    public static String getOrderBySql(List<PageSortInput> sortList) {
        if (CollectionUtils.isEmpty(sortList)) {
            return null;
        }
        return sortList.stream().filter(it -> !StringUtils.isEmpty(it.getSortBy()) || !StringUtils.isEmpty(it.getOrderBy())).map(
                it ->  it.getSortBy() +" "+ it.getOrderBy()
        ).collect(Collectors.joining(","));
    }
}
