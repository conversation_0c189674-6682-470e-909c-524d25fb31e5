package net.xianmu.authentication.common.util;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiGettokenRequest;
import com.dingtalk.api.request.OapiUserGetRequest;
import com.dingtalk.api.request.OapiUserGetuserinfoRequest;
import com.dingtalk.api.response.OapiGettokenResponse;
import com.dingtalk.api.response.OapiUserGetResponse;
import com.dingtalk.api.response.OapiUserGetuserinfoResponse;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-08-12
 * @description
 */
@Slf4j
@Service
public class DingTalkUtils {
    @Resource(name = "redisTemplate")
    private  RedisTemplate redisTemplate;

    private static final String URL_TOKEN = "https://oapi.dingtalk.com/gettoken";
    private static final String URL_USERINFO = "https://oapi.dingtalk.com/user/getuserinfo";
    private static final String URL_USER = "https://oapi.dingtalk.com/user/get";
    public static final String APP_KEY = "dingotmyqkkux5rnj2u7";
    public static final String APP_SECRET = "N_uOnSK9L5SOumPkNN53IRl_oquP3UmpzDdNSHa1koyH6d225YHnPXkqnP55lNtc";

    /**
     * 获取token
     *
     * @return token
     */
    public String getToken() {
        try {
            String accessToken = (String) redisTemplate.opsForValue().get("accessToken");
            if (accessToken != null) {
                return accessToken;
            }
            DefaultDingTalkClient client = new DefaultDingTalkClient(URL_TOKEN);
            OapiGettokenRequest request = new OapiGettokenRequest();
            request.setAppkey(APP_KEY);
            request.setAppsecret(APP_SECRET);
            request.setHttpMethod("GET");
            OapiGettokenResponse response = client.execute(request);
            if (response.isSuccess()) {
                redisTemplate.opsForValue().set("accessToken", response.getAccessToken(), 7200, TimeUnit.SECONDS);
                return response.getAccessToken();
            }
            log.error("获取token失败，errmsg：{}", response.getErrmsg());
            return null;
        } catch (ApiException e) {
            log.error("钉钉token获取失败：", e);
            throw new DefaultServiceException("钉钉token获取失败");
        }
    }

    /**
     * 根据token 和 code 获取用户信息
     *
     * @param code
     * @return OapiUserGetResponse
     */
    public OapiUserGetResponse getOapiUser(String code) {

        try {
            String accessToken = (String) redisTemplate.opsForValue().get("accessToken");
            if (StringUtils.isEmpty(accessToken)) {
                accessToken = getToken();
            }
            DingTalkClient client = new DefaultDingTalkClient(URL_USERINFO);
            OapiUserGetuserinfoRequest request = new OapiUserGetuserinfoRequest();
            request.setCode(code);
            request.setHttpMethod("GET");
            OapiUserGetuserinfoResponse response = client.execute(request, accessToken);

            if (!response.isSuccess()) {
                OapiUserGetResponse userGetResponse = new OapiUserGetResponse();
                userGetResponse.setErrcode(response.getErrcode());
                userGetResponse.setErrmsg(response.getErrmsg());
                return userGetResponse;
            }

            DingTalkClient userClient = new DefaultDingTalkClient(URL_USER);
            OapiUserGetRequest userRequest = new OapiUserGetRequest();
            userRequest.setUserid(response.getUserid());
            userRequest.setHttpMethod("GET");
            return userClient.execute(userRequest, accessToken);
        } catch (Exception e) {
            log.error("钉钉授权信息获取失败：", e);
            throw new DefaultServiceException("钉钉授权信息获取失败");
        }
    }

}
