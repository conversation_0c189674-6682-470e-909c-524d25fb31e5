package net.xianmu.authentication.common.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;

import java.util.regex.Pattern;

/**
 * 密码格式校验工具类
 * 
 * <AUTHOR>
 * @date 2025-01-21
 */
@Slf4j
public class PasswordValidationUtil {

    /**
     * 来源6(COSFO_MANAGE)密码格式：8-20字符，需包含大小写字母和数字
     */
    private static final Pattern COSFO_MANAGE_PASSWORD_PATTERN = Pattern.compile("^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,20}$");
    
    /**
     * 来源7(MALL)密码格式：6-20字符
     */
    private static final Pattern MALL_PASSWORD_PATTERN = Pattern.compile("^.{6,20}$");

    /**
     * 根据系统来源校验密码格式
     * 
     * @param systemOriginEnum 系统来源枚举
     * @param password 密码
     * @throws BizException 校验不通过时抛出业务异常
     */
    public static void validatePasswordFormat(Integer originType, String password) {
        if (originType == null) {
            throw new BizException("系统来源不能为空");
        }
        
        if (StrUtil.isBlank(password)) {
            throw new BizException("密码不能为空");
        }
        
        // 来源6：COSFO_MANAGE - 8-20字符，需包含大小写字母和数字
        if (SystemOriginEnum.COSFO_MANAGE.getType().equals(originType)) {
            validateCosfoManagePassword(password);
        }
        // 来源7：COSFO_MALL - 6-20字符
        else if (SystemOriginEnum.COSFO_MALL.getType().equals(originType)) {
            validateMallPassword(password);
        }
        // 其他来源暂不校验
    }

    /**
     * 校验COSFO_MANAGE来源的密码格式
     * 
     * @param password 密码
     * @throws BizException 校验不通过时抛出业务异常
     */
    private static void validateCosfoManagePassword(String password) {
        if (!COSFO_MANAGE_PASSWORD_PATTERN.matcher(password).matches()) {
            log.error("密码格式不正确，需要8-20字符，且包含大小写字母和数字");
            throw new BizException("密码格式不正确，需要8-20字符，且包含大小写字母和数字");
        }
    }

    /**
     * 校验MALL来源的密码格式
     * 
     * @param password 密码
     * @throws BizException 校验不通过时抛出业务异常
     */
    private static void validateMallPassword(String password) {
        if (!MALL_PASSWORD_PATTERN.matcher(password).matches()) {
            log.error("密码格式不正确，需要6-20字符");
            throw new BizException("密码格式不正确，需要6-20字符");
        }
    }

    /**
     * 检查密码是否包含大写字母
     * 
     * @param password 密码
     * @return 是否包含大写字母
     */
    public static boolean containsUpperCase(String password) {
        if (StrUtil.isBlank(password)) {
            return false;
        }
        return password.chars().anyMatch(Character::isUpperCase);
    }

    /**
     * 检查密码是否包含小写字母
     * 
     * @param password 密码
     * @return 是否包含小写字母
     */
    public static boolean containsLowerCase(String password) {
        if (StrUtil.isBlank(password)) {
            return false;
        }
        return password.chars().anyMatch(Character::isLowerCase);
    }

    /**
     * 检查密码是否包含数字
     * 
     * @param password 密码
     * @return 是否包含数字
     */
    public static boolean containsDigit(String password) {
        if (StrUtil.isBlank(password)) {
            return false;
        }
        return password.chars().anyMatch(Character::isDigit);
    }

    /**
     * 获取密码格式要求描述
     * 
     * @param systemOriginEnum 系统来源枚举
     * @return 密码格式要求描述
     */
    public static String getPasswordFormatDescription(SystemOriginEnum systemOriginEnum) {
        if (systemOriginEnum == null) {
            return "未知来源";
        }
        
        Integer originType = systemOriginEnum.getType();
        
        if (SystemOriginEnum.COSFO_MANAGE.getType().equals(originType)) {
            return "8-20字符，需包含大小写字母和数字";
        } else if (SystemOriginEnum.MALL.getType().equals(originType)) {
            return "6-20字符";
        } else {
            return "无特殊要求";
        }
    }
}
