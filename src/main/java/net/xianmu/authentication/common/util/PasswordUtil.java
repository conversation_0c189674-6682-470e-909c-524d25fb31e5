package net.xianmu.authentication.common.util;

import cn.hutool.core.util.StrUtil;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/5/8 18:22
 */
public class PasswordUtil {



    /**
     * 根据账户信息生成密码
     * 规则：
     * - 手机号格式：租户ID_手机号后6位
     * - 邮箱格式：租户ID_@符号前的内容
     * - 默认情况：租户ID_用户名后6位
     */
    public static String generatePassword(String username, String phone, Long tenantId) {
        if (StrUtil.isBlank(username)) {
            username = phone;
        }

        // 判断是否为手机号格式
        if (isPhoneNumber(username)) {
            // 手机号格式：租户ID_手机号后6位
            String phoneSuffix = username.length() >= 6 ? username.substring(username.length() - 6) : username;
            return tenantId + "_" + phoneSuffix;
        }

        // 判断是否为邮箱格式
        if (isEmail(username)) {
            // 邮箱格式：租户ID_@符号前的内容
            String emailPrefix = username.substring(0, username.indexOf('@'));
            return tenantId + "_" + emailPrefix;
        }

        // 默认情况：按照手机号逻辑处理（取用户名后6位）
        String usernameSuffix = username.length() >= 6 ? username.substring(username.length() - 6) : username;
        return tenantId + "_" + usernameSuffix;
    }


    /**
     * 判断是否为手机号格式
     */
    public static boolean isPhoneNumber(String str) {
        if (StrUtil.isBlank(str)) {
            return false;
        }
        return str.matches("^1[0-9]\\d{9}$");
    }

    /**
     * 判断是否为邮箱格式
     */
    public static boolean isEmail(String str) {
        if (StrUtil.isBlank(str)) {
            return false;
        }
        return str.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }
}
