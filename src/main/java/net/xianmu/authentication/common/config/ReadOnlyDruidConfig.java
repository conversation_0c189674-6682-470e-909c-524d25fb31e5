package net.xianmu.authentication.common.config;

import com.alibaba.druid.pool.DruidDataSource;
import com.github.pagehelper.PageHelper;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * <AUTHOR> ct
 * create at:  2022/2/8  11:53
 * offline 数据源配置
 */
@Configuration
@MapperScan(basePackages = "net.xianmu.authentication.mapper.offline", sqlSessionFactoryRef = "readOnlySqlSessionFactory")
public class ReadOnlyDruidConfig {

    @Value(value = "${mysql.driverClassName:}")
    private String driverClassName;
    @Value(value = "${mysql.offline.url:}")
    private String url;
    @Value(value = "${mysql.offline.username:}")
    private String username;
    @Value(value = "${mysql.offline.password:}")
    private String password;
    @Value(value = "${mysql.minIdle:}")
    private int minIdle;
    @Value(value = "${mysql.initialSize:}")
    private int initialSize;
    @Value(value = "${mysql.maxActive:}")
    private int maxActive;
    @Value(value = "${mysql.maxWait:}")
    private int maxWait;
    @Value(value = "${mysql.asyncInit:}")
    private boolean asyncInit;
    @Value(value = "${mysql.testWhileIdle:}")
    private boolean testWhileIdle;


   // @ConfigurationProperties(prefix = "spring.datasource.read.only")
    @Bean("readOnlyDruidDataSource")
    public DruidDataSource druidDataSource() {
        DruidDataSource druidDataSource = new DruidDataSource();
        druidDataSource.setDbType("mysql");
        druidDataSource.setDriverClassName(driverClassName);
        druidDataSource.setUrl(url);
        druidDataSource.setUsername(username);
        druidDataSource.setPassword(password);
        druidDataSource.setInitialSize(initialSize);
        druidDataSource.setMinIdle(minIdle);
        druidDataSource.setMaxActive(maxActive);
        druidDataSource.setMaxWait(maxWait);
        druidDataSource.setAsyncInit(asyncInit);
        druidDataSource.setTestWhileIdle(testWhileIdle);
        return druidDataSource;
    }


    @Bean("readOnlySqlSessionFactory")
    public SqlSessionFactoryBean sqlSessionFactory(@Qualifier("readOnlyDruidDataSource") DruidDataSource druidDataSource,
                                                   PageHelper pageHelper) {
        SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();
        sqlSessionFactoryBean.setDataSource(druidDataSource);
        sqlSessionFactoryBean.setPlugins(pageHelper);
        return sqlSessionFactoryBean;
    }



}
