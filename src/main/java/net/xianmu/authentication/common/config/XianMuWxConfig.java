package net.xianmu.authentication.common.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
@Data
public class XianMuWxConfig {

    @Value(value = "${xm.mall-wechat.mp-app.id}")
    private String xmMallWxAppId;
    @Value(value = "${xm.mall-wechat.mp-app.secret}")
    private String xmMallWxSecret;
    @Value(value = "${xm.mall-wechat.app.id}")
    private String xmMallWxOaAppId;
    @Value(value = "${xm.mall-wechat.app.secret}")
    private String xmMallWxOaSecret;




    @Value(value = "${xm.mall-wechat.pop-mp-app.id}")
    private String popMallWxAppId;
    @Value(value = "${xm.mall-wechat.pop-mp-app.secret}")
    private String popMallWxSecret;
    @Value(value = "${xm.mall-wechat.pop-app.id}")
    private String popMallWxOaAppId;
    @Value(value = "${xm.mall-wechat.pop-app.secret}")
    private String popMallWxOaSecret;

    @Value(value = "${xm.srm-wechat.app.id}")
    private String srmWxOaAppId;
    @Value(value = "${xm.srm-wechat.app.secret}")
    private String srmWxOaSecret;

    @Value(value = "${xm.srm-wechat.miniapp.id}")
    private String srmWxMiniAppId;
    @Value(value = "${xm.srm-wechat.miniapp.secret}")
    private String srmWxMiniSecret;


}
