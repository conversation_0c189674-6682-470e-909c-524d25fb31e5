package net.xianmu.authentication.common.config;

import net.xianmu.authentication.shiro.filter.PermissionFilter;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.servlet.Filter;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022年05月09日
 */
@Configuration
public class ShiroConfigFilter {

    /**
     * ShiroFilter是整个Shiro的入口点，用于拦截需要安全控制的请求进行处理
     */
    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilter = new ShiroFilterFactoryBean();
        shiroFilter.setSecurityManager(securityManager);
        shiroFilter.setLoginUrl("/summerfarm/home.html#/login");
        shiroFilter.setUnauthorizedUrl("/auth/unauthorized");
        Map<String, String> filterMap = new LinkedHashMap<>();
        filterMap.put("/ok", "anon");
        filterMap.put("/devtool/init", "anon");
        filterMap.put("/devtool/initTenantPrivileges", "anon");

        filterMap.put("/auth/username/logout", "roles");
        filterMap.put("/auth/authorized/logout", "roles");
        filterMap.put("/auth/loginfirst", "anon");
        filterMap.put("/auth/authorized/login", "anon");
        filterMap.put("/auth/authorized/feishuUnionIdLogin", "anon");
        filterMap.put("/auth/authorized/bind", "anon");
        filterMap.put("/auth/username/login", "anon");
        filterMap.put("/auth/phone/login", "anon");
        filterMap.put("/auth/login*", "anon");

        filterMap.put("/auth/unified/authorized/logout", "roles");
        filterMap.put("/auth/unified/logout", "roles");
        filterMap.put("/auth/unified/authorized/login", "anon");
        filterMap.put("/auth/unified/username/login", "anon");
        filterMap.put("/auth/unified/authorized/bind", "anon");
        filterMap.put("/feishu/query/signature", "anon");
        filterMap.put("/feishu/query/config-signature", "anon");
        filterMap.put("/feishu/query", "anon");

        filterMap.put("/auth/wechat/query/care", "anon");
        filterMap.put("/auth/wechat/query/access-token", "anon");

        filterMap.put("/auth/wx/config/**", "anon");
        filterMap.put("/auth/wx/**", "anon");
        filterMap.put("/**", "authc");
        shiroFilter.setFilterChainDefinitionMap(filterMap);

        Map<String, Filter> filterWonMap = new LinkedHashMap<>();
        filterWonMap.put("authc",new PermissionFilter());
        shiroFilter.setFilters(filterWonMap);

        return shiroFilter;
    }

}
