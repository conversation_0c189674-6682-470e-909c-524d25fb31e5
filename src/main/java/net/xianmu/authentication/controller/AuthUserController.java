package net.xianmu.authentication.controller;

import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.user.AuthUserPasswordUpdateInput;
import net.xianmu.authentication.model.VO.AuthUserVo;
import net.xianmu.authentication.model.input.AuthUserPasswordCommandInput;
import net.xianmu.authentication.model.input.AuthUserQueryInput;
import net.xianmu.authentication.service.AuthUserService;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.common.result.CommonResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RestController
@RequestMapping("/auth-user")
@Slf4j
public class AuthUserController {

    @Resource
    private AuthUserService authUserService;


    /**
     * 查询各业务线用户信息
     * @param input
     * @return
     */
    @RequestMapping(value = "/queryAuthUser", method = RequestMethod.POST)
    public CommonResult<PageInfo<AuthUserVo>> queryAuthUser(@Validated @RequestBody AuthUserQueryInput input) {
        return CommonResult.ok(authUserService.selectAuthUserBySourceRoleIds(input));
    }


    /**
     * 重置业务线的用户的密码
     * @param input
     * @return
     */
    @RequestMapping(value = "/resetUserPwd", method = RequestMethod.POST)
    public CommonResult<String> resetUserPwd(@Validated @RequestBody AuthUserPasswordCommandInput input) {
        return CommonResult.ok(authUserService.resetUserPwd(input));
    }


    /**
     * 重置业务线的用户的密码
     * @param input
     * @return
     */
    @RequestMapping(value = "/updateAuthUserPassword", method = RequestMethod.POST)
    public CommonResult<Void> updateAuthUserPassword(@Validated @RequestBody AuthUserPasswordCommandInput input) {
        authUserService.updateAuthUserPasswordByOrigin(input);
        return CommonResult.ok();
    }


}
