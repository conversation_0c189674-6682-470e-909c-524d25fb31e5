package net.xianmu.authentication.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.util.MD5Util;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.client.input.purview.TenantPrivilegesInput;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.model.VO.InitVO;
import net.xianmu.authentication.model.input.AuthUserBindBatchUpdateInput;
import net.xianmu.authentication.model.input.MenuCommandInput;
import net.xianmu.authentication.model.input.RoleCommandInput;
import net.xianmu.authentication.model.input.UserBaseBatchUpdateInput;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.authentication.service.InitService;
import net.xianmu.authentication.service.impl.AuthMenuServiceImpl;
import net.xianmu.authentication.service.impl.AuthRoleServiceImpl;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.common.user.UserBase;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/devtool")
@Slf4j
public class InitController {
    @Resource
    InitService  initService;
    @Resource
    AuthTenantPrivilegesService authTenantPrivilegesService;
    @Resource
    AuthUserBaseDao userBaseDao;
    @Resource
    AuthUserDao authUserDao;

    @Resource
    AuthUserServiceImpl authUserService;

    @Resource
    AuthRoleServiceImpl authRoleService;
    @Resource
    AuthMenuServiceImpl authMenuService;

    private final String token = "XIANMU_naksdkjashkjdhakjmnamnds,jljl1a..";

    @RequestMapping(value = "/init", method = RequestMethod.POST)
    public AjaxResult initRedisData(@RequestBody InitVO initVo) {
        log.info("初始化redis数据 {}", JSONUtil.toJsonStr(initVo));
        if (StringUtils.isEmpty(initVo.getToken()) || !Objects.equals(token, initVo.getToken())) {
            return AjaxResult.getErrorWithMsg("非法的来源");
        }
        List<Integer> systemOrigins = initVo.getSystemOrigins();
        for (Integer systemOrigin : systemOrigins) {
            SystemOriginEnum originEnum = SystemOriginEnum.getSystemOriginByType(systemOrigin);
            if (originEnum == null) {
                log.info("初始化redis数据 非法来源  {}", systemOrigin);
                return AjaxResult.getErrorWithMsg("非法的来源,参数错误");
            }
            initService.initRedisKey(systemOrigin);
        }
        log.info("初始化redis数据OK  {}", JSONUtil.toJsonStr(initVo));
        return AjaxResult.getOK();
    }

    @RequestMapping(value = "/initTenantPrivileges", method = RequestMethod.POST)
    public CommonResult initTenantPrivileges(@RequestBody AuthTenantPrivilegesInput authTenantPrivilegesInput) {
        log.info("初始化租户id数据 {}", JSONUtil.toJsonStr(authTenantPrivilegesInput));
        return CommonResult.ok(authTenantPrivilegesService.addTenantPrivileges(authTenantPrivilegesInput));
    }


    /**
     * 后门接口，用户更新业务线对应的用户基本信息、角色信息
     * @param userBaseBatchUpdateInput
     * @return
     */
    @RequestMapping(value = "/updateUserBase", method = RequestMethod.POST)
    public CommonResult updateUserBase(@Validated @RequestBody UserBaseBatchUpdateInput userBaseBatchUpdateInput) {
        log.info("通过后门接口更新userbase信息 {}", JSONUtil.toJsonStr(userBaseBatchUpdateInput));
        List<UserBase> userBaseList = userBaseBatchUpdateInput.getUserBaseList();
        Integer systemOrigin = userBaseBatchUpdateInput.getSystemOrigin();
        userBaseList.forEach(userBase ->authUserService.updateUser(SystemOriginEnum.getSystemOriginByType(systemOrigin), userBase));
        return CommonResult.ok();
    }


    /**
     * 后门接口，用户更新指定的用户基本信息
     * @param authUserBase
     * @return
     */
    @RequestMapping(value = "/updateAuthUserBase", method = RequestMethod.POST)
    public CommonResult updateUserBase(@RequestBody InitVO initVo) {
        log.info("通过后门接口更新AuthUserBase信息 {}", JSONUtil.toJsonStr(initVo));
        if (StringUtils.isEmpty(initVo.getToken()) || !Objects.equals(token, initVo.getToken())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "非法的来源");
        }
        AuthUserBase authUserBase = initVo.getAuthUserBase();
        userBaseDao.updateByPrimaryKeySelective(authUserBase);
        return CommonResult.ok();
    }


    /**
     * 后门接口，修改用户绑定的base信息
     * @param authUserBase
     * @return
     */
    @RequestMapping(value = "/updateAuthUserBindBaseInfo", method = RequestMethod.POST)
    public CommonResult updateAuthUserBindBaseInfo(@RequestBody InitVO initVo) {
        log.info("通过后门接口更新AuthUser所绑定的base信息 {}", JSONUtil.toJsonStr(initVo));
        if (StringUtils.isEmpty(initVo.getToken()) || !Objects.equals(token, initVo.getToken())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "非法的来源");
        }
        AuthUserBindBatchUpdateInput authUserBindBatchUpdateInput = initVo.getAuthUserBindBatchUpdateInput();
        List<Long> authUserIdList = authUserBindBatchUpdateInput.getAuthUserIdList();
        Long authUserBaseId = authUserBindBatchUpdateInput.getAuthUserBaseId();
        authUserIdList.forEach(id -> authUserDao.updateBaseUserIdById(id, authUserBaseId));
        return CommonResult.ok();
    }



    /**
     * @param authUserBase
     * @return
     */
    @RequestMapping(value = "/addRole", method = RequestMethod.POST)
    public CommonResult addRole(@RequestBody InitVO initVo) {
        log.info("通过后门接口新增角色信息 {}", JSONUtil.toJsonStr(initVo));
        if (StringUtils.isEmpty(initVo.getToken()) || !Objects.equals(token, initVo.getToken())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "非法的来源");
        }
        RoleCommandInput roleCommandInput = initVo.getRoleCommandInput();
        authRoleService.addRole(roleCommandInput.getSystemOriginEnum(), roleCommandInput.getUserId(),roleCommandInput.getTenantId(),roleCommandInput.getUpdateVO());
        return CommonResult.ok();
    }

    /**
     * @param authUserBase
     * @return
     */
    @RequestMapping(value = "/updateRole", method = RequestMethod.POST)
    public CommonResult updateRole(@RequestBody InitVO initVo) {
        log.info("通过后门接口更新角色信息 {}", JSONUtil.toJsonStr(initVo));
        if (StringUtils.isEmpty(initVo.getToken()) || !Objects.equals(token, initVo.getToken())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "非法的来源");
        }
        RoleCommandInput roleCommandInput = initVo.getRoleCommandInput();
        authRoleService.updateRole(roleCommandInput.getSystemOriginEnum(), roleCommandInput.getUserId(),roleCommandInput.getTenantId(),roleCommandInput.getUpdateVO());
        return CommonResult.ok();
    }



    /**
     * @param authUserBase
     * @return
     */
    @RequestMapping(value = "/addPurviews", method = RequestMethod.POST)
    public CommonResult addPurviews(@RequestBody InitVO initVo) {
        log.info("通过后门接口新增权限信息 {}", JSONUtil.toJsonStr(initVo));
        if (StringUtils.isEmpty(initVo.getToken()) || !Objects.equals(token, initVo.getToken())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "非法的来源");
        }
        MenuCommandInput menuCommandInput = initVo.getMenuCommandInput();
        authMenuService.addPurviews(menuCommandInput.getSystemOriginEnum(), menuCommandInput.getUserId(),menuCommandInput.getTenantId(),menuCommandInput.getAuthMenuPurview());
        return CommonResult.ok();
    }

    /**
     * @param authUserBase
     * @return
     */
    @RequestMapping(value = "/updatePurviews", method = RequestMethod.POST)
    public CommonResult updatePurviews(@RequestBody InitVO initVo) {
        log.info("通过后门接口更新权限信息 {}", JSONUtil.toJsonStr(initVo));
        if (StringUtils.isEmpty(initVo.getToken()) || !Objects.equals(token, initVo.getToken())) {
            return CommonResult.fail(ResultStatusEnum.BAD_REQUEST, "非法的来源");
        }
        MenuCommandInput menuCommandInput = initVo.getMenuCommandInput();
        authMenuService.updatePurviews(menuCommandInput.getSystemOriginEnum(), menuCommandInput.getUserId(),menuCommandInput.getTenantId(),menuCommandInput.getAuthMenuPurview());
        return CommonResult.ok();
    }


}
