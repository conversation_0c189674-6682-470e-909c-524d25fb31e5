package net.xianmu.authentication.controller;

import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.ShiroUser;
import net.xianmu.authentication.model.VO.AuthLoginVO;
import net.xianmu.authentication.service.AuthLoginService;
import net.xianmu.authentication.service.feishu.FeiShuService;
import net.xianmu.common.enums.base.auth.LoginTypeEnum;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 用户登录
 * @createTime 2022年04月19日
 */
@RestController
@Slf4j
@RequestMapping("/auth")
public class LoginController {


    public final static String LOCK_PREFIX = "lock:auth:%s";
    public final static String UNDERLINE = "_";
    @Resource
    private AuthLoginService loginService;
    @Resource
    private RedissonClient redissonClient;
    @Resource
    private FeiShuService feiShuService;

    /**
     * 登陆页
     *
     * @return
     */
    @ApiOperation(value = "登陆页", httpMethod = "GET", tags = "登录管理")
    @RequestMapping(value = "/login", method = RequestMethod.GET)
    public String login(HttpServletRequest request) {
        if (SecurityUtils.getSubject().getPrincipal() != null) {
            return "redirect:/pages/managelogin/managelogin.html";
        }
        if (!"/login".equals(request.getServletPath())) {
            return "redirect:/loginfirst";
        }
        return "login";
    }

    /**
     * 提示登陆
     *
     * @return
     */
    @ApiOperation(value = "提示登陆", httpMethod = "GET", tags = "登录管理")
    @ResponseBody
    @RequestMapping(value = "/loginfirst", method = RequestMethod.GET)
    public AjaxResult loginfirst() {
        return AjaxResult.getError(ResultConstant.LOGIN_FIRST);
    }

    @ApiOperation(value = "无权限提示语", httpMethod = "GET", tags = "登录管理")
    @RequestMapping(value = "/unauthorized")
    @ResponseBody
    public AjaxResult unauthorized() {
        return AjaxResult.getError(ResultConstant.UNAUTHORIZED);
    }

    /**
     * 用户名密码登录
     *
     * @param username
     * @param password
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/username/login", method = RequestMethod.POST)
    public AjaxResult login(String username, String password) {
        AuthLoginVO authLoginVO = new AuthLoginVO();
        if (StringUtils.isEmpty(username)) {
            throw new DefaultServiceException("用户名不能为空");
        }
        if (StringUtils.isEmpty(password)) {
            throw new DefaultServiceException("密码不能为空");
        }
        authLoginVO.setUsername(username);
        authLoginVO.setPassword(password);
        authLoginVO.setType(LoginTypeEnum.NAME_PWD.name);
        authLoginVO.setOrigin(SystemOriginEnum.ADMIN.type);
        return loginService.login(authLoginVO);
    }

    /**
     * 其他来源pc登陆
     *
     * @param authLoginVO
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/phone/login", method = RequestMethod.POST)
    public AjaxResult<AuthLoginDto> login(@RequestBody AuthLoginVO authLoginVO) {
        if (StringUtils.isEmpty(authLoginVO.getPassword())) {
            throw new BizException("密码不能为空");
        }
        if (StringUtils.isEmpty(authLoginVO.getPhone())) {
            throw new BizException("账户不能为空");
        }
        if (StringUtils.isEmpty(authLoginVO.getType())) {
            throw new BizException("登陆来源不能为空");
        }
        return loginService.login(authLoginVO);
    }

    /**
     * 获取当前用户
     *
     * @return
     */
    @ApiOperation(value = "获取当前用户", httpMethod = "GET", tags = "登录管理")
    @ResponseBody
    @RequestMapping(value = "/adminOnline", method = RequestMethod.GET)
    public AjaxResult adminOnline() {
        Subject subject = SecurityUtils.getSubject();
        //判断是否登陆过
        if (subject.isAuthenticated()) {
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
            Map<String, Object> resData = new HashMap<>();
            resData.put("username", user.getUsername());
            resData.put("token", subject.getSession().getId());
            return AjaxResult.getOK(resData);
        }
        return AjaxResult.getError(ResultConstant.LOGIN_FIRST);
    }

    /**
     * 退出
     *
     * @return
     */
    @ApiOperation(value = "退出登录", httpMethod = "POST", tags = "登录管理")
    @RequestMapping(value = "/username/logout", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult logout() {
        try {
            Subject subject = SecurityUtils.getSubject();
            subject.logout();
        } catch (Exception e) {
            log.warn("用户登出异常", e);
        }
        return AjaxResult.getOK();
    }

    @ApiOperation(value = "登录", httpMethod = "POST", tags = "管理员管理")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "type", value = "认证登录类型：0、钉钉 1、CRM 2 飞书", paramType = "query"),
        @ApiImplicitParam(name = "code", value = "wechat Code值", paramType = "query")
    })
    @ResponseBody
    @RequestMapping(value = "/authorized/login", method = RequestMethod.POST)
    public AjaxResult<AuthLoginDto> authLogin(@RequestParam(defaultValue = "0", required = false) Integer type, String code, @RequestParam(required = false) Integer systemOrigin
        , @RequestParam(required = false) String channel) {
        return loginService.authLogin(type, code, systemOrigin, channel);
    }

    @ApiOperation(value = "根据UnionId登录", httpMethod = "POST", tags = "管理员管理")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "unionId", value = "飞书unionId, 每个用户在同一个飞书组织下的唯一标识（不同的app获取到的union ID 相同），用于获取用户信息", paramType = "query")
    })
    @ResponseBody
    @PostMapping(value = "/authorized/feishuUnionIdLogin")
    public CommonResult<AuthLoginDto> loginWithFeishuUnionId(@RequestParam String unionId) {
        return loginService.loginWithFeishuUnionId(unionId);
    }

    @ApiOperation(value = "授权绑定", httpMethod = "POST", tags = "管理员管理")
    @ApiImplicitParams({
        @ApiImplicitParam(name = "type", value = "认证类型：0、钉钉应用 1、CRM小程序", required = false, defaultValue = "0"),
        @ApiImplicitParam(name = "username", value = "账号", required = true),
        @ApiImplicitParam(name = "password", value = "密码", required = true)
    })
    @RequestMapping(value = "/authorized/bind", method = RequestMethod.POST)
    public AjaxResult authBinding(@RequestParam(required = false, defaultValue = "0") Integer type, String username, String password, String uuid) {
        String loginKey = username + UNDERLINE + type + UNDERLINE + uuid;
        RLock redissonLock = redissonClient.getLock(String.format(LOCK_PREFIX, loginKey));
        try {
            boolean flag = redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS);
            if (!flag) {
                return AjaxResult.getErrorWithMsg("登录中,请稍候");
            }
            return loginService.authBind(type, username, password, uuid);
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            return AjaxResult.getErrorWithMsg("请重新登录");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()) {
                redissonLock.unlock();
            }
        }
    }

    @ApiOperation(value = "退出登录", httpMethod = "POST", tags = "管理员管理")
    @ApiImplicitParam(name = "type", value = "认证登录类型：0、钉钉 1、CRM", paramType = "query")
    @ResponseBody
    @RequestMapping(value = "/authorized/logout", method = RequestMethod.POST)
    public AjaxResult authLogout(Integer type) {
        return loginService.authLogout(type);
    }

}
