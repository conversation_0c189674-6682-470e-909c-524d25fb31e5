package net.xianmu.authentication.controller;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.model.VO.FeiShuQueryVO;
import net.xianmu.authentication.model.resp.FeiShuTicketResp;
import net.xianmu.authentication.service.feishu.FeiShuService;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.CommonResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 飞书相关接口
 * <AUTHOR>
 * @date 2023/7/18  15:18
 */
@RestController
@RequestMapping("/feishu")
@Slf4j
public class FeiShuController {
    @Resource
    private FeiShuService feiShuService;
    /**
     * 获取飞书jsapi ticket
     * @param url url
     * @return FeiShuTicketResp
     */
    @PostMapping("/query/signature")
    public CommonResult<FeiShuTicketResp> getFeiShuSignature(String url) {
        return feiShuService.getFeiShuSignature(url);
    }

    /**
     * 查询飞书的系统的配置和signature
     * @param feiShuQueryVO
     * @return
     */
    @PostMapping("/query/config-signature")
    public CommonResult<FeiShuTicketResp> getFeiShuConfigSignature(@RequestBody FeiShuQueryVO feiShuQueryVO) {
        SystemOriginEnum systemOriginByType = SystemOriginEnum.getSystemOriginByType(feiShuQueryVO.getSystemOrigin());
        CommonResult<FeiShuTicketResp> feiShuSignature = feiShuService.getFeiShuSignature(systemOriginByType, feiShuQueryVO.getUrl());
        return feiShuSignature;
    }

    /**
     * 查询飞书的系统的配置和signature
     * @param feiShuQueryVO
     * @return
     */
    @PostMapping("/query")
    public CommonResult<FeiShuTicketResp> getFeiShuConfig(@RequestBody FeiShuQueryVO feiShuQueryVO) {
        SystemOriginEnum systemOriginByType = SystemOriginEnum.getSystemOriginByType(feiShuQueryVO.getSystemOrigin());
        CommonResult<FeiShuTicketResp> feiShuSignature = feiShuService.getFeiShuConfig(systemOriginByType, feiShuQueryVO.getChannel());
        return feiShuSignature;
    }

}
