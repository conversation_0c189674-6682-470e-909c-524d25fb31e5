package net.xianmu.authentication.controller;

import io.swagger.annotations.ApiOperation;
import net.xianmu.authentication.model.VO.QueryCareWxVO;
import net.xianmu.authentication.service.WechatService;
import net.summerfarm.common.AjaxResult;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.common.result.CommonResult;
import org.springframework.data.repository.query.Param;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Description:微信相关接入层
 * date: 2022/7/22 16:20
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/auth/wechat")
public class WechatController {

    @Resource
    private WechatService wechatService;
    @Resource
    private WxService wxService;

    @ApiOperation(value = "引导关注",httpMethod = "GET",tags = "微信相关")
    @RequestMapping(value = "/toPayAttention", method = RequestMethod.GET)
    public AjaxResult toPayAttention(@Param(value = "unionId") String unionId){
        boolean payAttentionResult = wechatService.toPayAttention(unionId);
        return AjaxResult.getOK(payAttentionResult);
    }

    /**
     * 判断是否关注微信公众号
      * @param queryCareWxVO
     * @return
     */
    @RequestMapping(value = "/query/care", method = RequestMethod.POST)
    public CommonResult<String> queryCare(@Validated  @RequestBody QueryCareWxVO queryCareWxVO ){
        return CommonResult.ok(wechatService.queryCare(queryCareWxVO));
    }

    /**
     * 查询accessToken
     * @param channelCode
     * @return
     */
    @RequestMapping(value = "/query/access-token", method = RequestMethod.GET)
    public CommonResult<String> queryAccessToken(@Param(value = "channelCode") String channelCode){
        return CommonResult.ok(wxService.getAccessToken(channelCode));
    }
}
