package net.xianmu.authentication.controller;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.MD5Util;
import net.xianmu.authentication.common.util.SpringContextUtil;
import net.xianmu.authentication.model.VO.CreateDatePermissionVO;
import net.xianmu.authentication.model.VO.QueryDatePermissionVO;
import net.xianmu.authentication.model.resp.DatePermissionResp;
import net.xianmu.authentication.service.permission.AuthUserPermissionService;
import net.xianmu.authentication.service.permission.PermissionService;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 数据权限
 */
@RestController
@Slf4j
@RequestMapping("/permission")
public class PermissionController extends AuthBaseController {
    @Resource
    AuthUserPermissionService authUserPermissionService;
    /**
     * 添加权限
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public CommonResult<Boolean> addPermission(@RequestBody @Validated CreateDatePermissionVO vo) {
        return CommonResult.ok(authUserPermissionService.addPermission(getUserBase(), vo));
    }


    /**
     * 查询权限
     *
     * @return
     */
    @ResponseBody
    @RequestMapping(value = "/query", method = RequestMethod.POST)
    public CommonResult<List<DatePermissionResp>> query(@RequestBody @Validated QueryDatePermissionVO vo) {
        UserBase userBase = getUserBase();
        return CommonResult.ok(authUserPermissionService.queryDatePermissionResp(userBase, vo));
    }

    public static void main(String[] args) {
        System.out.println(MD5Util.string2MD5("19JW9m"));
    }
}
