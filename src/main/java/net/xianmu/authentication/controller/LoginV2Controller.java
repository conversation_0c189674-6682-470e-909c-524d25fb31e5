package net.xianmu.authentication.controller;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.model.DTO.AuthPeronUserInfoDTO;
import net.xianmu.authentication.model.VO.AuthLoginVO;
import net.xianmu.authentication.service.AuthLoginService;
import net.summerfarm.common.AjaxResult;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.Logical;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.apache.shiro.subject.Subject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description 用户登录
 * @createTime 2022年04月19日
 */
@RestController
@Slf4j
@RequestMapping("/auth")
public class LoginV2Controller {

    @Resource
    private AuthLoginService loginService;

    @ResponseBody
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public AjaxResult login(@RequestBody @Validated AuthLoginVO authLoginVO) {
        return loginService.login(authLoginVO);
    }

    @ResponseBody
    @RequestMapping(value = "/loginByToken", method = RequestMethod.POST)
    public AjaxResult loginByToken(@RequestBody @Validated AuthLoginVO authLoginVO) {
        return loginService.loginByToken(authLoginVO);
    }



    @ResponseBody
    @RequestMapping(value = "/personInfo", method = RequestMethod.POST)
    public AjaxResult<AuthPeronUserInfoDTO> authInfo() {
        return loginService.personalInfo();
    }

    /**
     * 退出
     *
     * @return
     */
    @ApiOperation(value = "退出登录", httpMethod = "POST", tags = "登录管理")
    @RequestMapping(value = "/loginOut", method = RequestMethod.POST)
    @ResponseBody
    public AjaxResult logout() throws IOException {
        Subject subject = SecurityUtils.getSubject();
        subject.logout();
        return AjaxResult.getOK();
    }

}
