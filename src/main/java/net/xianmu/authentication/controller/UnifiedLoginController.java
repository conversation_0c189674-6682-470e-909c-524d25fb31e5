package net.xianmu.authentication.controller;

import com.aliyun.odps.simpleframework.xml.core.Validate;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.model.DTO.AuthLoginDTO;
import net.xianmu.authentication.model.DTO.AuthLoginRespDTO;
import net.xianmu.authentication.model.DTO.LoginDTO;
import net.xianmu.authentication.model.DTO.LogoutDTO;
import net.xianmu.authentication.model.VO.LoginVO;
import net.xianmu.authentication.service.UnifiedLoginService;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;



@Slf4j
@Validate
@RestController
@RequestMapping("/auth/unified")
public class UnifiedLoginController {

    @Resource
    private UnifiedLoginService loginService;
    @Resource
    private RedissonClient redissonClient;

    public final static String LOCK_PREFIX = "lock:auth:%s";
    public final static String UNDERLINE = "_";



    @ApiOperation(value = "授权登录",httpMethod = "POST",tags = "用户管理")
    @RequestMapping(value = "/authorized/login", method = RequestMethod.POST)
    public AjaxResult<AuthLoginDto> authLogin(@RequestBody @Validate AuthLoginDTO authLoginDTO){
        AuthLoginDto loginVO = loginService.authLogin(authLoginDTO.transferToBO());
        if (StringUtils.isEmpty(loginVO.getUnionId())){
            return AjaxResult.getError(ResultConstant.UNBIND,(Object) loginVO.getUsername());
        }
        return AjaxResult.getOK(loginVO);
    }

    @ApiOperation(value = "授权绑定/账密登录", httpMethod = "POST", tags = "用户管理")
    @RequestMapping(value = {"/authorized/bind","/username/login"}, method = RequestMethod.POST)
    public AjaxResult<AuthLoginDto> login(@RequestBody @Validate LoginDTO loginDTO) {
        String loginKey = loginDTO.getLoginKey();
        RLock redissonLock = redissonClient.getLock(String.format(LOCK_PREFIX, loginKey));
        try {
            boolean flag = redissonLock.tryLock(0L, 10L, TimeUnit.SECONDS);
            if (!flag) {
                return AjaxResult.getErrorWithMsg("登录中,请稍候");
            }
            AuthLoginDto loginVO = loginService.login(loginDTO.transferToBO());
            return AjaxResult.getOK(loginVO);
        } catch (InterruptedException e) {
            log.error("锁获取异常", e);
            return AjaxResult.getErrorWithMsg("请重新登录");
        } finally {
            if (redissonLock.isLocked() && redissonLock.isHeldByCurrentThread()){
                redissonLock.unlock();
            }
        }
    }

    @ApiOperation(value = "退出登录",httpMethod = "POST",tags = "用户管理")
    @RequestMapping(value = {"/authorized/logout","/logout"}, method = RequestMethod.POST)
    public AjaxResult logout(@RequestBody @Validate LogoutDTO logoutDTO){
        loginService.logout(logoutDTO.transferToBO());
        return AjaxResult.getOK();
    }
}

