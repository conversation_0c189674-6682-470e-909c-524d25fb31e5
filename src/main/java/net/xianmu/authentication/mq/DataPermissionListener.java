package net.xianmu.authentication.mq;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.common.DbTableDml;
import net.xianmu.authentication.common.dto.DtsModel;
import net.xianmu.authentication.common.factory.DbTableDmlFactory;
import net.xianmu.rocketmq.support.annotation.MqOrderlyListener;
import net.xianmu.rocketmq.support.consumer.AbstractMqListener;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

@Component
@Slf4j
@Lazy
@MqOrderlyListener(topic = "mysql-binlog",tag = "warehouse_logistics_center", consumerGroup = "GID_binlog_auth_permission")
public class DataPermissionListener extends AbstractMqListener<DtsModel> {

    @Resource
    private DbTableDmlFactory dbTableDmlFactory;

    @Override
    public void process(DtsModel dtsModel) {
        log.info("rocketmq 收到消息，事件类型：{}，recordId/msg-key：{}， 表：{}.{}",
                dtsModel.getType(), dtsModel.getMsgKey(), dtsModel.getDatabase(), dtsModel.getTable());

        DbTableDml creator = dbTableDmlFactory.creator(dtsModel.getTable());
        if (Objects.nonNull(creator)) {
            creator.handel(dtsModel);
        } else {
            log.info("未在DbTableDmlFactory注册的table:{},请先注册后再做处理!", dtsModel.getTable());
        }
    }
}


