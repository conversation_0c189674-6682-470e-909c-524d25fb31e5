package net.xianmu.authentication.tripartite;



import net.xianmu.authentication.common.enmu.SystemLoginTypeEnum;

/**
 * Description:授权主体相关接口
 * date: 2022/7/14 14:12
 *
 * <AUTHOR>
 */
public interface AuthSubject {

    /**
     * 查询授权信息
     * @param systemLoginTypeEnum 系统登录类型枚举类
     * @param code 授权码
     * @return 授权信息
     */
    AuthInfo getAuthInfo(SystemLoginTypeEnum systemLoginTypeEnum, String code);

    /**
     * 处理用户绑定关系
     * @param subjectId subjectId
     * @param userId 用户ID
     */
    void handleUserBind(String subjectId,Long userId);
}
