package net.xianmu.authentication.tripartite;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import net.xianmu.authentication.common.contexts.Global;

/**
 * Description:授权信息
 * date: 2022/7/14 14:46
 *
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public class AuthInfo {

    /**
     * 授权ID
     */
    private String openId;

    /**
     * 主体ID
     */
    private String subjectId;

    public String getAuthId(){
        return openId + Global.COMMA + subjectId;
    }

}
