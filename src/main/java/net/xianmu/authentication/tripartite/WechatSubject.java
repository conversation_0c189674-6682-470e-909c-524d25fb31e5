package net.xianmu.authentication.tripartite;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import net.xianmu.authentication.common.contexts.Global;
import net.xianmu.authentication.common.enmu.SystemLoginTypeEnum;
import net.xianmu.authentication.enums.AuthLoginTypeEnum;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.authentication.service.impl.BaseService;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.weixin.WeChatUtils;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.i18n.exception.I18nBizException;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * Description:微信主体
 * date: 2022/7/14 14:21
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class WechatSubject extends BaseService implements AuthSubject {

    private static final String OPENID = "openid";
    private static final String UNION_ID = "unionid";

    @Resource
    private AuthUserPropertiesExtDao authUserPropertiesExtDao;
    @Resource
    private AuthUserServiceImpl authUserService;

    @Override
    public AuthInfo getAuthInfo(SystemLoginTypeEnum systemLoginTypeEnum, String code) {
        SystemOriginEnum systemOriginEnum = systemLoginTypeEnum.getSystemOriginEnum();
        AuthLoginTypeEnum authLoginTypeEnum = systemLoginTypeEnum.getAuthLoginTypeEnum();
        JSONObject userInfo = WeChatUtils.getMpOpenId(code, systemLoginTypeEnum.getAppId(), systemLoginTypeEnum.getAppSecret());
        log.info("获取对应平台授权ID,origin:【{}】,authType:【{}】,code:【{}】,result:【{}】", systemOriginEnum.getName(), authLoginTypeEnum.getName(), code, JSON.toJSONString(userInfo));

        if (StringUtils.isBlank(userInfo) || StringUtils.isBlank(userInfo.getString(OPENID))) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "未获取到openId");
        }
        //获取用户授权ID(微信openId)
        String openId = userInfo.getString(OPENID);
        //获取平台唯一标识(微信unionId) 用于微信公众号推送消息的时候绑定微信小程序登录的系统用户
        String unionId = userInfo.getString(UNION_ID);
        return new AuthInfo(openId, unionId);
    }

    @Override
    public void handleUserBind(String subjectId, Long userId) {
        if (StrUtil.isBlank(subjectId) || userId == null) {
            throw new I18nBizException("数据异常,subjectId:{0},userId:{1}", subjectId, userId);
        }
        List<AuthUserPropertiesExt> userPropertiesExt = authUserPropertiesExtDao.selectALLByValue(Global.WECHAT_OFFICIAL_PUSH_INFO, subjectId);
        if (CollectionUtils.isEmpty(userPropertiesExt)) {
            //先登录后关注的情况
            authUserService.createUserExtProperties(userId, Global.WECHAT_OFFICIAL_PUSH_INFO, subjectId);
        } else {
            for (AuthUserPropertiesExt it : userPropertiesExt) {
                Long bindUserId = it.getUserId();
                if (bindUserId == null) {
                    continue;
                }
                if (bindUserId.equals(userId)){
                    it.setUpdateTime(new Date());
                    authUserPropertiesExtDao.updateByPrimaryKey(it);
                    continue;
                }
                //先关注后登录 未绑定的情况
                //微信已绑定关系 登录其他账号情况下进行换绑
                it.setUserId(userId);
                authUserPropertiesExtDao.updateByPrimaryKey(it);
            }
        }
    }

}
