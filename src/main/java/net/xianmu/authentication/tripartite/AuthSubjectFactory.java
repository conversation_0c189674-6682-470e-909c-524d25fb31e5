package net.xianmu.authentication.tripartite;


import net.xianmu.authentication.enums.AuthLoginTypeEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.EnumMap;
import java.util.Map;

/**
 * Description:授权主体工厂类
 * date: 2022/7/14 14:11
 *
 * <AUTHOR>
 */
@Component
public class AuthSubjectFactory {

    /**
     * <登录类型枚举，授权主体> Map
     */
    private final static Map<AuthLoginTypeEnum, AuthSubject> STRATEGY_MAP = new EnumMap<>(AuthLoginTypeEnum.class);

    @Autowired
    public AuthSubjectFactory(DingTalkSubject dingTalkSubject, WechatSubject wechatSubject){
        STRATEGY_MAP.put(AuthLoginTypeEnum.DING_TALK, dingTalkSubject);
        STRATEGY_MAP.put(AuthLoginTypeEnum.WX_MINI_APP, wechatSubject);
    }

    /**
     * 获取授权主体
     * @param authLoginTypeEnum 授权登录类型枚举
     * @return 授权主体
     */
    public AuthSubject getAuthSubject(AuthLoginTypeEnum authLoginTypeEnum){
        return STRATEGY_MAP.get(authLoginTypeEnum);
    }

}
