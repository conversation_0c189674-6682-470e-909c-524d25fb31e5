package net.xianmu.authentication.tripartite;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.dingtalk.api.response.OapiUserGetResponse;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import net.xianmu.authentication.common.contexts.Global;
import net.xianmu.authentication.common.enmu.SystemLoginTypeEnum;
import net.xianmu.authentication.enums.AuthLoginTypeEnum;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.authentication.service.impl.BaseService;
import net.xianmu.authentication.common.util.DingTalkUtils;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;

import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.i18n.exception.I18nBizException;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * Description:
 * date: 2022/7/14 14:22
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class DingTalkSubject extends BaseService implements AuthSubject {


    @Resource
    private AuthUserPropertiesExtDao authUserPropertiesExtDao;
    @Resource
    private AuthUserServiceImpl authUserService;
    @Resource
    DingTalkUtils dingTalkUtils;

    @Override
    public AuthInfo getAuthInfo(SystemLoginTypeEnum systemLoginTypeEnum, String code) {
        SystemOriginEnum systemOriginEnum = systemLoginTypeEnum.getSystemOriginEnum();
        AuthLoginTypeEnum authLoginTypeEnum = systemLoginTypeEnum.getAuthLoginTypeEnum();
        OapiUserGetResponse apiUser = dingTalkUtils.getOapiUser(code);
        log.info("获取对应平台授权ID,origin:【{}】,authType:【{}】,code:【{}】,result:【{}】", systemOriginEnum.getName(), authLoginTypeEnum.getName(), code, JSONObject.toJSONString(apiUser));
        if (apiUser == null) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "未获取到openId");
        }
        if (!apiUser.isSuccess()) {
            log.error(apiUser.getErrmsg());
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "未获取到openId");
        }

        String openId = apiUser.getOpenId();
        String userid = apiUser.getUserid();
        return new AuthInfo(openId, userid);
    }

    @Override
    public void handleUserBind(String subjectId, Long userId) {
        if (StrUtil.isBlank(subjectId) || userId == null) {
            throw new I18nBizException("数据异常,subjectId:{0},userId:{1}", subjectId, userId);
        }
        List<AuthUserPropertiesExt> userPropertiesExtList = authUserPropertiesExtDao.selectValue(userId, Global.DING_TALK_USERID);
        if (userPropertiesExtList.isEmpty()) {
            authUserService.createUserExtProperties(userId, Global.DING_TALK_USERID, subjectId);
        }
    }
}
