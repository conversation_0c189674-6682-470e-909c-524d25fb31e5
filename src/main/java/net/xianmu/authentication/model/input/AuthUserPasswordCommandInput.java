package net.xianmu.authentication.model.input;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 描述:
 *
 * @author: <EMAIL>
 * @创建时间: 2023/2/27
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthUserPasswordCommandInput implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     *  业务表ID admin-admin_id saas租户账户id  auth auth_id 门店-门店账户id
     *
     */
    @NotNull
    private Long bizUserId;

    /**
     * 租户id
     */
    @NotNull
    private Long tenantId;


    /**
     * 系统来源 详情见SystemOriginEnum
     * 0:SRM 1:TMS 2:ADMIN 3:CRM 5:COSF0_OMS 6:COSF0_MANAGE 7：COSF0_MALL 8:MALL
     */
    @NotNull
    private Integer origin;


    /**
     * 密码
     */
    private String password;


}
