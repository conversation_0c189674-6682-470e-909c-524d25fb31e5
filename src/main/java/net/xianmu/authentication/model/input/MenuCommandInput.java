package net.xianmu.authentication.model.input;

import lombok.Data;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/3/12 14:01
 */

@Data
public class MenuCommandInput {

    private SystemOriginEnum systemOriginEnum;
    /**
     * 操作人的auth user id
     */
    private Long userId;
    private Long tenantId;
    private AuthMenuPurview authMenuPurview;

}
