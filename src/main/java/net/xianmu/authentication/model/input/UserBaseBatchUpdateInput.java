package net.xianmu.authentication.model.input;

import lombok.Data;
import net.xianmu.common.user.UserBase;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/3/12 14:01
 */

@Data
public class UserBaseBatchUpdateInput {

    @NotEmpty
    @Size(max = 50)
    private List<UserBase> userBaseList;

    @NotNull
    private Integer systemOrigin;
}
