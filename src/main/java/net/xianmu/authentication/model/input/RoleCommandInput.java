package net.xianmu.authentication.model.input;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.common.input.BasePageInput;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/3/12 14:01
 */

@Data
public class RoleCommandInput {

    private SystemOriginEnum systemOriginEnum;
    /**
     * 操作人的auth user id
     */
    private Long userId;
    private Long tenantId;
    private AuthRoleUpdateVO updateVO;

}
