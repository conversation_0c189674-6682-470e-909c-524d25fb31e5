package net.xianmu.authentication.model.input;

import lombok.Data;
import net.xianmu.common.user.UserBase;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/3/12 14:01
 */

@Data
public class AuthUserBindBatchUpdateInput {

    private List<Long> authUserIdList;

    private Long authUserBaseId;
}
