package net.xianmu.authentication.model.entity;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * business_data_permission
 * <AUTHOR>
@Data
public class BusinessDataPermission implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 数据权限值
     */
    private String permissionValue;

    /**
     * 数据权限名称
     */
    private String permissionName;

    /**
     * 数据类型 数据类型 0城市 1仓库 2城配仓
     */
    private Byte permissionType;

    private static final long serialVersionUID = 1L;
}