package net.xianmu.authentication.model.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * auth_wechat_relation
 * <AUTHOR>
@Data
public class AuthWechatRelation implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 系统来源
     */
    private Byte systemOrigin;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * auth_type1：微信小程序，4微信公众号
     */
    private Byte authType;

    /**
     * open_id
     */
    private String openid;

    /**
     * untion_id
     */
    private String unionid;

    private static final long serialVersionUID = 1L;
}