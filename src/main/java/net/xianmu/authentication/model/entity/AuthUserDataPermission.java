package net.xianmu.authentication.model.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;
import net.xianmu.authentication.client.dto.AuthUser;

/**
 * auth_user_data_permission
 * <AUTHOR>
@Data
public class AuthUserDataPermission implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 权限值
     */
    private String permissionValue;

    /**
     * 数据类型 0城市 1仓库 2城配仓
     */
    private Byte permissionType;

    private static final long serialVersionUID = 1L;
}