package net.xianmu.authentication.model.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * auth_tenant_privileges
 * <AUTHOR>
@Data
public class AuthTenantPrivileges implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 权益id
     */
    private Long menuId;

    /**
     * 过期时间
     */
    private Date expireTime;


    private static final long serialVersionUID = 1L;
}