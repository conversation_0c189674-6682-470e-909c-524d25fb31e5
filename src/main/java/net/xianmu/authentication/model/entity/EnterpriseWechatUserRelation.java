package net.xianmu.authentication.model.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * enterprise_wechat_user_relation
 * <AUTHOR>
@Data
public class EnterpriseWechatUserRelation implements Serializable {
    /**
     * primary key
     */
    private Long id;

    /**
     * create time
     */
    private Date createTime;

    /**
     * update time
     */
    private Date updateTime;

    /**
     * auth 用户id
     */
    private Long authId;

    /**
     * admin  id
     */
    private Long bizUserId;

    /**
     * 微信 user_id
     */
    private String thirdPartyId;

    /**
     * 邀请码
     */
    private String qrCodeUrl;

    private static final long serialVersionUID = 1L;
}