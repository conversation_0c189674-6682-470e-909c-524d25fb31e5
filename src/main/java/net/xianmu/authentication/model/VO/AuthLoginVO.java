package net.xianmu.authentication.model.VO;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 登陆vo
 */
@Data
public class AuthLoginVO {
    /**
     * 手机号
     */
    private String phone;
    /**
     * 密码
     */
    private String password;

    /**
     * 登陆方式
     */
    private String type;
    /**
     * 用户名称
     */
    private String username;

    /**
     * 短信验证码
     */
    private String messageCode;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 系统来源 详情见SystemOriginEnum
     * 0:SRM 1:TMS 2:ADMIN 3:CRM 5:COSF0_OMS 6:COSF0_MANAGE
     */
    @NotNull
    private Integer origin;

    private String token;


    private Boolean superMan;

    public Boolean getSuperMan() {
        return superMan == null ? false: superMan;
    }
}
