package net.xianmu.authentication.model.VO;

import lombok.Data;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.model.input.AuthUserBindBatchUpdateInput;
import net.xianmu.authentication.model.input.MenuCommandInput;
import net.xianmu.authentication.model.input.RoleCommandInput;

import java.util.List;

@Data
public class InitVO {
    private String token;
    private List<Integer> systemOrigins;
    private AuthUserBase authUserBase;
    private AuthUserBindBatchUpdateInput authUserBindBatchUpdateInput;
    private RoleCommandInput roleCommandInput;
    private MenuCommandInput menuCommandInput;

}
