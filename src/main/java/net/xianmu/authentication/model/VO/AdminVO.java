package net.xianmu.authentication.model.VO;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import net.xianmu.authentication.model.BO.AdminDataPermissionBO;
import net.xianmu.authentication.model.BO.RoleBO;
import net.summerfarm.pojo.DO.Admin;
import net.summerfarm.pojo.DO.MenuPurview;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022年05月03日
 */
@Data
public class AdminVO extends Admin {

    private List<RoleBO> roleVOs;

    /**
     * 普通客户为1 大客户为2
     */
    @ApiModelProperty(value = "客户类型：普通客户为1 大客户为2")
    private Integer type = 1;

    @ApiModelProperty(value = "管理员邀请码")
    private String invitecode;

    @ApiModelProperty(value = "是否在公海池中")
    private Boolean openSea = false;

    private List<AdminDataPermissionBO> dataPermissions;

    private List<MenuPurview> menuPurviews;

    @ApiModelProperty(value = "是否有主管权限,true-是、false-不是，用于数据报表销售维度")
    private Boolean onlySaleManager;

    @ApiModelProperty(value = "数据报表销售维度仓数据权限")
    private List<String> partSaleArae;

}
