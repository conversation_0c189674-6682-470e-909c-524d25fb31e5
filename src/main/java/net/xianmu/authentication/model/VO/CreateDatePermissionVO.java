package net.xianmu.authentication.model.VO;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class CreateDatePermissionVO {
    /**
     * 数据类型 2城配仓 0城市 1仓库
     */
    @NotNull
    private Integer type;
    /**
     * 权限集合
     */
    private List<DatePermissionVO> list;
    /**
     * admin 的 adminId
     */
    private Long bizUserId;
    /**
     * 租户id admin添加的话写死为 1
     */
    private Long tenantId;

    /**
     * admin 为 2
     * to see SystemOriginEnum
     */
    private Integer systemOrigin;
}

