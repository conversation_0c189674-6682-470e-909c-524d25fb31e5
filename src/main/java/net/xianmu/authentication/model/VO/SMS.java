package net.xianmu.authentication.model.VO;

import com.alibaba.fastjson.JSONObject;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import net.summerfarm.enums.SMSType;

import java.util.List;

/**
 * @Package: com.manageSystem.model
 * @Description:
 * @author: <EMAIL>
 * @Date: 2018/5/4
 */
@ToString
public class SMS {

    private String phone;

    private String templateCode;

    private JSONObject jsonObject;

    @Setter
    @Getter
    private SMSType type;

    @Setter
    @Getter
    private List<String> args;

    @Setter
    @Getter
    private Long sceneId;

    /**
     * 短信内容.
     */
    private String content;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getTemplateCode() {
        return templateCode;
    }

    public void setTemplateCode(String templateCode) {
        this.templateCode = templateCode;
    }

    public JSONObject getJsonObject() {
        return jsonObject;
    }

    public void setJsonObject(JSONObject jsonObject) {
        this.jsonObject = jsonObject;
    }
}
