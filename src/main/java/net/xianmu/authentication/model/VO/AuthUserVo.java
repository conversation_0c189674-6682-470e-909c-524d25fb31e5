package net.xianmu.authentication.model.VO;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/3/12 14:40
 */
@Data
public class AuthUserVo {

    /**
     * id 自增
     */
    private Long id;

    /**
     * 基础表的id
     */
    private Long userBaseId;

    /**
     * 用户名（登录使用）
     */
    private String userName;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 状态，0：有效，1：失效
     */
    private Byte status;

    /**
     * 系统来源
     */
    private Integer systemOrigin;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;

    /**
     * 用户业务id(例如：来源是admin时，这里就是adminId)
     */
    private Long bizUserId;
}
