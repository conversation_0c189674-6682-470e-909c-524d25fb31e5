package net.xianmu.authentication.model.VO;

import lombok.Builder;
import lombok.Data;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.io.Serializable;

//回复消息的实体类,可以修改以下toString方法格式为xml的格式即可
@XmlRootElement( name = "xml")
@Data
@Builder
public class WXSendMessageVO implements Serializable {
        /**
         * <xml>
         *   <ToUserName><![CDATA[toUser]]></ToUserName>
         *   <FromUserName><![CDATA[FromUser]]></FromUserName>
         *   <CreateTime>123456789</CreateTime>
         *   <MsgType><![CDATA[event]]></MsgType>
         *   <Event><![CDATA[subscribe]]></Event>
         *   <EventKey><![CDATA[qrscene_123123]]></EventKey>
         * </xml>
         */
        private static final long serialVersionUID = 1L;
        @XmlElement(name = "ToUserName")
        private String toUserName;
        @XmlElement(name ="FromUserName")
        private String fromUserName;
        @XmlElement(name ="CreateTime")
        private Long createTime;
        @XmlElement(name ="MsgType")
        private String msgType;
        @XmlElement(name ="Event")
        private String event;
        @XmlElement(name ="EventKey")
        private String eventKey;

}