package net.xianmu.authentication.model.VO;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * Description: 登录响应视图对象
 * date: 2022/7/13 11:51
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class LoginVO {
    private String username;
    private Serializable token;
    private String unionId;

    public LoginVO(String username, Serializable token) {
        this.username = username;
        this.token = token;
    }


    public boolean ifUnBind() {
        return this.token == null;
    }
}
