package net.xianmu.authentication.model.resp;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/7/18  21:54
 */
@Data
public class FeiShuTicketResp implements Serializable {
    private static final long serialVersionUID=1L;

    /**
     * url
     */
    private String url;

    /**
     * 随机字符串
     */
    private String noncestr;

    /**
     * 飞书ticker
     */
    private String jsapiTicket;

    /**
     * 毫秒时间戳
     */
    private Long timestamp;

    /**
     * 签名
     */
    private String signature;

    /**
     * appId
     */
    private String appId;
    /**
     * appSecret
     */
    private String appSecret;
}
