package net.xianmu.authentication.model.BO;

import lombok.Data;
import net.summerfarm.pojo.DO.Admin;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022年04月20日
 */
@Data
public class AdminBO extends Admin {

    private List<RoleBO> roleBOs;

    /**
     * 用户角色id
     */
    private Set<Integer> roleIds;

    /**
     * 用户菜单权限
     */
    private Set<String> purviewUrls;

    /**
     * 用户数据权限
     */
    private Set<Integer> dataPermissions;

    public AdminBO(){

    }

}
