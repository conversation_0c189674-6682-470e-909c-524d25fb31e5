package net.xianmu.authentication.model.BO;

import lombok.AllArgsConstructor;
import lombok.Data;
import net.xianmu.authentication.common.enmu.SystemLoginTypeEnum;


/**
 * Description: 授权绑定业务实体
 * date: 2022/6/10 17:47
 *
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
public class LoginBO {

    /**
     * 用户名
     */
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 授权ID
     */
    private String authId;

    /**
     * unionId
     */
    private String unionId;

    private SystemLoginTypeEnum systemLoginType;

}
