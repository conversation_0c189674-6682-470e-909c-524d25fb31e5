package net.xianmu.authentication.model.BO;

import lombok.Data;
import net.summerfarm.pojo.DO.AdminDataPermission;
import net.summerfarm.warehouse.model.domain.WarehouseStorageCenter;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022年04月23日
 */
@Data
public class AdminDataPermissionBO extends AdminDataPermission {
    /**
     * 仓库编号
     */
    private Integer warehouseNo;

    public AdminDataPermissionBO(){

    }

    public AdminDataPermissionBO(WarehouseStorageCenter center) {
        this.setPermissionValue(String.valueOf(center.getWarehouseNo()));
        this.setPermissionName(center.getWarehouseName());
        this.setType("0");
    }
}
