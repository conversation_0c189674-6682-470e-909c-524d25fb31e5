package net.xianmu.authentication.model.DTO;

import lombok.Data;
import net.xianmu.authentication.client.dto.AuthLoginDto;


@Data
public class AuthLoginRespDTO extends AuthLoginDto {
    private static final long serialVersionUID = 8432861128865445205L;
    private String username;
    private String unionId;
    private String phone;
    private String realname;


    public AuthLoginRespDTO() {
    }

    public AuthLoginRespDTO(String authId) {

    }

    public boolean ifUnBind(){
        return this.getToken() == null;
    }
}
