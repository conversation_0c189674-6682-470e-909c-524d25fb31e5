package net.xianmu.authentication.model.DTO;

import lombok.Data;
import net.xianmu.authentication.common.enmu.SystemLoginTypeEnum;
import net.xianmu.authentication.enums.AuthLoginTypeEnum;
import net.xianmu.authentication.model.BO.LogoutBO;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;


import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * Description:授权退出请求实体
 * date: 2022/6/10 17:43
 *
 * <AUTHOR>
 */
@Data
public class LogoutDTO {

    /**
     * 系统来源：0、srm，1、tms
     */
    @NotNull(message = "系统来源不能为空")
    @Min(value = 0,message = "非法系统来源")
    private Integer origin;

    /**
     * 认证登录类型：0、钉钉应用，1、微信小程序
     */
    @NotNull(message = "认证登录类型不能为空")
    @Min(value = 0,message = "非法认证登录类型")
    private Integer authType;


    public LogoutBO transferToBO(){
        //校验是否是可支持的授权登录类型
        AuthLoginTypeEnum authLoginTypeEnum = AuthLoginTypeEnum.getAuthLoginType(authType);
        //校验是否是可支持的系统来源
        SystemOriginEnum originEnum = SystemOriginEnum.getSystemOriginByType(origin);
        //校验是否是可支持的系统登录类型
        SystemLoginTypeEnum systemLoginType = SystemLoginTypeEnum.getSupportedSystemLoginType(originEnum, authLoginTypeEnum);
        return new LogoutBO(systemLoginType);
    }
}
