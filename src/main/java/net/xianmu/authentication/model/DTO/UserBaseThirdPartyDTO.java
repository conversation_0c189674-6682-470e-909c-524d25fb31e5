package net.xianmu.authentication.model.DTO;

import lombok.Data;

import java.io.Serializable;

@Data
public class UserBaseThirdPartyDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    private Long baseUserId;
    private Long bizUserId;
    private String phone;
    private String nickName;
    private Byte authType;
    private String authId;
    private String thirdPartyId;
}
