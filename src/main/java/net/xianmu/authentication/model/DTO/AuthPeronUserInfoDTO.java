package net.xianmu.authentication.model.DTO;

import lombok.Data;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.dto.AuthMenuPurviewDto;
import net.xianmu.authentication.client.dto.AuthPeronUserInfo;
import net.xianmu.authentication.model.resp.DatePermissionResp;

import java.util.List;

@Data
public class AuthPeronUserInfoDTO extends AuthPeronUserInfo {
    /**
     * 菜单树
     */
    private List<AuthMenuPurviewDto> treeMenus;
    /**
     * 菜单树
     */
    private List<AuthMenuPurviewDTO> menus;
    private Long bizUserId;
    /**
     * 真实名称
     */
    private String realName;
}
