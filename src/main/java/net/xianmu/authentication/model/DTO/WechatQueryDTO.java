package net.xianmu.authentication.model.DTO;

import lombok.Data;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;

import java.io.Serializable;

@Data
public class WechatQueryDTO implements Serializable {
    private static final long serialVersionUID = 1L;


    /**
     * 是否pop客户
     */
    private boolean isPopMerchant;

    /**
     * 三方登陆类型(公众号/小程序)
     */
    private AuthTypeEnum loginType;

    /**
     * 微信公众号小程序拉起来授权
     */
    private String code;
    /**
     * 系统来源
     */
    private SystemOriginEnum systemOriginEnum;

}
