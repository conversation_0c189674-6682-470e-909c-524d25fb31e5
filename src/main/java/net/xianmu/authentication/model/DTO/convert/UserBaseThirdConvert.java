package net.xianmu.authentication.model.DTO.convert;

import net.xianmu.authentication.client.resp.UserBaseThirdPartyResp;
import net.xianmu.authentication.model.DTO.UserBaseThirdPartyDTO;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

public class UserBaseThirdConvert {


    private UserBaseThirdConvert() {
        // 无需实现
    }

    public static List<UserBaseThirdPartyResp> toUserBaseThirdPartyRespList(List<UserBaseThirdPartyDTO> userBaseThirdPartyDTOList) {
        if (userBaseThirdPartyDTOList == null) {
            return Collections.emptyList();
        }
        List<UserBaseThirdPartyResp> userBaseThirdPartyRespList = new ArrayList<>();
        for (UserBaseThirdPartyDTO userBaseThirdPartyDTO : userBaseThirdPartyDTOList) {
            userBaseThirdPartyRespList.add(toUserBaseThirdPartyResp(userBaseThirdPartyDTO));
        }
        return userBaseThirdPartyRespList;
    }

    public static UserBaseThirdPartyResp toUserBaseThirdPartyResp(UserBaseThirdPartyDTO userBaseThirdPartyDTO) {
        if (userBaseThirdPartyDTO == null) {
            return null;
        }
        UserBaseThirdPartyResp userBaseThirdPartyResp = new UserBaseThirdPartyResp();
        userBaseThirdPartyResp.setBaseUserId(userBaseThirdPartyDTO.getBaseUserId());
        userBaseThirdPartyResp.setBizUserId(userBaseThirdPartyDTO.getBizUserId());
        userBaseThirdPartyResp.setPhone(userBaseThirdPartyDTO.getPhone());
        userBaseThirdPartyResp.setNickName(userBaseThirdPartyDTO.getNickName());
        userBaseThirdPartyResp.setAuthType(userBaseThirdPartyDTO.getAuthType());
        userBaseThirdPartyResp.setAuthId(userBaseThirdPartyDTO.getAuthId());
        userBaseThirdPartyResp.setThirdPartyId(userBaseThirdPartyDTO.getThirdPartyId());
        return userBaseThirdPartyResp;
    }

    public static List<UserBaseThirdPartyDTO> toUserBaseThirdPartyDTOList(List<UserBaseThirdPartyResp> userBaseThirdPartyRespList) {
        if (userBaseThirdPartyRespList == null) {
            return Collections.emptyList();
        }
        List<UserBaseThirdPartyDTO> userBaseThirdPartyDTOList = new ArrayList<>();
        for (UserBaseThirdPartyResp userBaseThirdPartyResp : userBaseThirdPartyRespList) {
            userBaseThirdPartyDTOList.add(toUserBaseThirdPartyDTO(userBaseThirdPartyResp));
        }
        return userBaseThirdPartyDTOList;
    }

    public static UserBaseThirdPartyDTO toUserBaseThirdPartyDTO(UserBaseThirdPartyResp userBaseThirdPartyResp) {
        if (userBaseThirdPartyResp == null) {
            return null;
        }
        UserBaseThirdPartyDTO userBaseThirdPartyDTO = new UserBaseThirdPartyDTO();
        userBaseThirdPartyDTO.setBaseUserId(userBaseThirdPartyResp.getBaseUserId());
        userBaseThirdPartyDTO.setBizUserId(userBaseThirdPartyResp.getBizUserId());
        userBaseThirdPartyDTO.setPhone(userBaseThirdPartyResp.getPhone());
        userBaseThirdPartyDTO.setNickName(userBaseThirdPartyResp.getNickName());
        userBaseThirdPartyDTO.setAuthType(userBaseThirdPartyResp.getAuthType());
        userBaseThirdPartyDTO.setAuthId(userBaseThirdPartyResp.getAuthId());
        userBaseThirdPartyDTO.setThirdPartyId(userBaseThirdPartyResp.getThirdPartyId());
        return userBaseThirdPartyDTO;
    }
}
