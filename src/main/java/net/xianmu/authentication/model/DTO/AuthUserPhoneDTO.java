package net.xianmu.authentication.model.DTO;

import lombok.Data;
import net.xianmu.authentication.client.dto.AuthUser;

/**
 * <AUTHOR>
 * @date 2023/7/19  15:35
 */
@Data
public class AuthUserPhoneDTO extends AuthUser {
    /**
     * 手机号
     */
    private String phone;



    public boolean filterSetPhone(){
        String phone = getPhone();
        if (phone.startsWith("_")) {
            return false;
        }

        //处理手机号
        if (phone.contains("_")) {
            String[] spl = phone.split("_");
            this.setPhone(spl[0]);
        }
        return true;
    }
}
