package net.xianmu.authentication.model.DTO;

import lombok.Data;
import net.xianmu.authentication.common.contexts.Global;
import net.xianmu.authentication.common.enmu.SystemLoginTypeEnum;
import net.xianmu.authentication.controller.UnifiedLoginController;
import net.xianmu.authentication.enums.AuthLoginTypeEnum;
import net.xianmu.authentication.model.BO.LoginBO;

import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * Description: 授权绑定请求实体
 * date: 2022/6/10 17:47
 *
 * <AUTHOR>
 */
@Data
public class LoginDTO {

    /**
     * 系统来源：0、srm，1、tms
     */
    @NotNull(message = "系统来源不能为空")
    @Min(value = 0, message = "非法系统来源")
    private Integer origin;

    /**
     * 认证登录类型：0、钉钉应用，1、微信小程序
     */
    @NotNull(message = "认证登录类型不能为空")
    @Min(value = 0, message = "非法认证登录类型")
    private Integer authType;

    /**
     * 用户名
     */
    @NotBlank(message = "用户名不能为空")
    private String username;

    /**
     * 密码
     */
    @NotBlank(message = "密码不能为空")
    private String password;

    /**
     * 授权ID：openId,unionId
     */
    @NotBlank(message = "授权ID不能为空")
    private String authId;

    public LoginBO transferToBO() {
        //校验是否是可支持的授权登录类型
        AuthLoginTypeEnum authLoginTypeEnum = AuthLoginTypeEnum.getAuthLoginType(authType);
        //校验是否是可支持的系统来源
        SystemOriginEnum originEnum = SystemOriginEnum.getSystemOriginByType(origin);
        //校验是否是可支持的系统登录类型
        SystemLoginTypeEnum systemLoginType = SystemLoginTypeEnum.getSupportedSystemLoginType(originEnum, authLoginTypeEnum);
        if (!authId.contains(Global.COMMA) || authId.split(Global.COMMA).length != Global.TWO){
            throw new DefaultServiceException("参数异常");
        }
        String[] split = authId.split(Global.COMMA);
        return new LoginBO(username, password, split[0], split[1], systemLoginType);
    }

    public String getLoginKey() {
        return username + UnifiedLoginController.UNDERLINE + origin + UnifiedLoginController.UNDERLINE + authType;
    }
}
