package net.xianmu.authentication.model.PO;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2022-06-07
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = false)
public class SrmSupplierUser implements Serializable {

    private static final long serialVersionUID=1L;

    /**
     * 主键id
     */
    private Long id;

    /**
     * 手机号
     */
    private String phone;

    /**
     * openId
     */
    private String openId;

    /**
     * unionId
     */
    private String unionId;

    /**
     * 供应商id
     */
    private Integer supplierId;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;

    /**
     * 更新人
     */
    private String updater;

    public SrmSupplierUser(String phone, Integer supplierId){
        this.phone = phone;
        this.supplierId = supplierId;
    }

}
