package net.xianmu.authentication.facade;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.usercenter.client.tenant.provider.TenantAccountQueryProvider;
import net.xianmu.usercenter.client.tenant.resp.TenantAccountResultResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import static net.summerfarm.contexts.ResultConstant.LOGIN_FIRST;

/**
 * sg
 * 用户中心dubbo调用
 */
@Component
@Slf4j
public class UserCenterFacade {
    @DubboReference
    private TenantAccountQueryProvider tenantAccountQueryProvider;

    /**
     * 根据authid获取 saas租户账号名称
     * @param authId
     * @return
     */
    public String getTenantUserName(Long authId){
        DubboResponse<TenantAccountResultResp> tenantAccountVO = tenantAccountQueryProvider.getTenantAccountVO(authId);
        TenantAccountResultResp data = tenantAccountVO.getData();
        if (data != null && tenantAccountVO.getCode().equals(DubboResponse.COMMON_SUCCESS_CODE)) {
           return data.getNickname();
        }
        log.warn("调用user center rpc接口异常 result {}", JSONUtil.toJsonStr(tenantAccountVO));
        return   null;
    }

}
