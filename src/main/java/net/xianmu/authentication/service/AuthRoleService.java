package net.xianmu.authentication.service;

import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthRoleDTO;
import net.xianmu.authentication.client.dto.AuthRoleDetailsDTO;
import net.xianmu.authentication.client.dto.AuthUserRoleDto;
import net.xianmu.authentication.client.input.AuthRoleQueryVO;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.UserRoleInput;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

public interface AuthRoleService {

    /**
     * @param systemOriginEnum 系统来源
     * @param roleQueryVO      角色名称查询 分页
     * @return
     */
    PageInfo<AuthRoleDTO> roleList(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleQueryVO roleQueryVO);

    /**
     * @param systemOriginEnum 系统来源
     * @param roleId           角色id
     * @param userId           用户id
     * @return
     */
    AuthRoleDetailsDTO roleDetail(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, Long roleId);

    /**
     * @param systemOriginEnum 系统来源
     * @param updateVO         更新角色信息
     * @param userId           用户id
     * @return
     */
    int updateRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleUpdateVO updateVO);

    /**
     * @param systemOriginEnum 系统来源
     * @param updateVO         更新角色信息
     * @param userId           用户id
     * @return
     */
    AuthRole addRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleUpdateVO updateVO);


    /**
     * 删除角色
     */
    /**
     * @param systemOriginEnum 系统来源
     * @param userId           用户id
     * @param roleId           角色id
     * @return
     */
    int deleteRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, Long roleId);

    List<Long> getUserIdListByRoleId(List<Long> roleIds);

    List<AuthUserRoleDto> getUserRoleByUserList(List<Long> userIdList);

    void createUserRole(SystemOriginEnum systemOriginEnum, List<UserRoleInput> userRoleInputs);
}
