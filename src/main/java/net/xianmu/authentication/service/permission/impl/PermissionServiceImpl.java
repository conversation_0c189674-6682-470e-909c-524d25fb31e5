package net.xianmu.authentication.service.permission.impl;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.mapper.auth.BusinessDataPermissionDao;
import net.xianmu.authentication.model.entity.BusinessDataPermission;
import net.xianmu.authentication.service.permission.PermissionService;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;

@Slf4j
@Service
public class PermissionServiceImpl  implements PermissionService {
    @Resource
    BusinessDataPermissionDao businessDataPermissionDao;
    @Override
    public void addPermission(BusinessDataPermission businessDataPermission) {
         businessDataPermission.setUpdateTime(new Date());
         businessDataPermission.setCreateTime(new Date());
         businessDataPermissionDao.insertSelective(businessDataPermission);
    }

    @Override
    public void updatePermission(String value,Integer type,String name) {
        BusinessDataPermission old = businessDataPermissionDao.selectByValueType(value,type);
        if (old  == null){
            throw new BizException("数据权限老数据异常");
        }
        old.setPermissionName(name);
        old.setUpdateTime(new Date());
        businessDataPermissionDao.updateByPrimaryKeySelective(old);
    }
}
