package net.xianmu.authentication.service.permission;

import net.xianmu.authentication.model.VO.CreateDatePermissionVO;
import net.xianmu.authentication.model.VO.QueryDatePermissionVO;
import net.xianmu.authentication.model.resp.DatePermissionResp;
import net.xianmu.common.user.UserBase;

import java.util.List;


public interface AuthUserPermissionService {
    Boolean addPermission(UserBase userBase, CreateDatePermissionVO vo);

    List<DatePermissionResp> queryDatePermissionResp(UserBase userBase, QueryDatePermissionVO queryDatePermissionVO);
}
