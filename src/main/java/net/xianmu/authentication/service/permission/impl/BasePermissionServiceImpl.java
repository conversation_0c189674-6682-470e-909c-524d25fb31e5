package net.xianmu.authentication.service.permission.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.authentication.mapper.auth.AuthUserDataPermissionDao;
import net.xianmu.authentication.mapper.auth.BusinessDataPermissionDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.model.VO.CreateDatePermissionVO;
import net.xianmu.authentication.model.VO.QueryDatePermissionVO;
import net.xianmu.authentication.model.entity.AuthUserDataPermission;
import net.xianmu.authentication.model.entity.BusinessDataPermission;
import net.xianmu.authentication.model.resp.DatePermissionResp;
import net.xianmu.authentication.service.permission.AuthUserPermissionService;
import net.xianmu.common.user.UserBase;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class BasePermissionServiceImpl implements AuthUserPermissionService {

    private static final String ALL_DATE_PERMISSION = "-1";
    @Resource
    AuthUserDataPermissionDao authUserDataPermissionDao;
    @Resource
    BusinessDataPermissionDao businessDataPermissionDao;
    @Resource
    AuthUserDao authUserDao;

    @Override
    @Transactional
    public Boolean addPermission(UserBase userBase, CreateDatePermissionVO vo) {
        //删所有
        Long bizUserId = vo.getBizUserId();
        Long tenantId = vo.getTenantId();
        Integer systemOrigin = vo.getSystemOrigin();
        AuthUser authUser = authUserDao.selectByBizUserIdTenantId(systemOrigin, bizUserId, tenantId);
        if (authUser == null) {
            throw new DefaultServiceException("用户数据错误");
        }
        authUserDataPermissionDao.deleteByUserIdType(authUser.getId(), vo.getType());
        //批量添加
        batchAdd(authUser.getId(), authUser.getTenantId(), vo);
        return false;
    }

    @Override
    public List<DatePermissionResp> queryDatePermissionResp(UserBase userBase, QueryDatePermissionVO queryDatePermissionVO) {
        Integer type = queryDatePermissionVO.getType();
        Long userId = getUserId(userBase, queryDatePermissionVO);
        log.info("权限查询获取到当前对象信息 {} auth:id{}", JSONUtil.toJsonStr(userBase),userId);
        List<AuthUserDataPermission> authUserDataPermissions = authUserDataPermissionDao.selectByUserIdType(userId, type);
        List<BusinessDataPermission> businessDataPermissionByType = businessDataPermissionDao.getBusinessDataPermissionByType(type);
        List<AuthUserDataPermission> dataPermissions = filterAll(type, authUserDataPermissions, businessDataPermissionByType);
        Map<String, BusinessDataPermission> authUserBaseMap = businessDataPermissionByType.stream().collect(Collectors.toMap(BusinessDataPermission::getPermissionValue, Function.identity()));
        return convert(dataPermissions, authUserBaseMap);
    }

    protected Long getUserId(UserBase userBase, QueryDatePermissionVO queryDatePermissionVO) {
        if (queryDatePermissionVO.getBizUserId() == null) {
            return userBase.getId();
        }
        Long bizUserId = queryDatePermissionVO.getBizUserId();
        Long tenantId = queryDatePermissionVO.getTenantId();
        Integer systemOrigin = queryDatePermissionVO.getSystemOrigin();
        AuthUser authUser = authUserDao.selectByBizUserIdTenantId(systemOrigin, bizUserId, tenantId);
        if (authUser == null) {
            throw new DefaultServiceException("用户数据错误");
        }
        return authUser.getId();
    }


    private List<AuthUserDataPermission> filterAll(Integer type, List<AuthUserDataPermission> authUserDataPermissions,
                                                   List<BusinessDataPermission> businessDataPermissionByType) {
        if (CollectionUtils.isEmpty(authUserDataPermissions)) {
            return authUserDataPermissions;
        }
        List<AuthUserDataPermission> all = authUserDataPermissions.stream().filter(it -> it.getPermissionValue().equals(ALL_DATE_PERMISSION)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(all)) {
            return authUserDataPermissions;
        }
        List<AuthUserDataPermission> dataPermissions = businessDataPermissionByType.stream().map(
                it -> {
                    AuthUserDataPermission authUserDataPermission = new AuthUserDataPermission();
                    authUserDataPermission.setPermissionValue(it.getPermissionValue());
                    authUserDataPermission.setPermissionType(type.byteValue());
                    authUserDataPermission.setUpdateTime(it.getUpdateTime());
                    authUserDataPermission.setCreateTime(it.getCreateTime());
                    return authUserDataPermission;
                }
        ).collect(Collectors.toList());
        all.addAll(dataPermissions);
        return all;
    }

    protected List<DatePermissionResp> convert(List<AuthUserDataPermission> authUserDataPermission, Map<String, BusinessDataPermission> map) {
        if (CollectionUtils.isEmpty(authUserDataPermission)) {
            return new ArrayList<>();
        }
        return authUserDataPermission.stream().map(
                it -> {
                    DatePermissionResp datePermissionResp = new DatePermissionResp();
                    datePermissionResp.setType(it.getPermissionType().intValue());
                    if (it.getPermissionValue().equals(ALL_DATE_PERMISSION)) {
                        datePermissionResp.setPermissionName("全部");
                    } else {
                        BusinessDataPermission businessDataPermission = map.get(it.getPermissionValue());
                        if (businessDataPermission != null) {
                            datePermissionResp.setPermissionName(businessDataPermission.getPermissionName());
                        }
                    }
                    datePermissionResp.setPermissionValue(it.getPermissionValue());
                    return datePermissionResp;
                }
        ).collect(Collectors.toList());
    }

    private void batchAdd(Long userId, Long tenantId, CreateDatePermissionVO vo) {
        Integer type = vo.getType();
        Set<String> valueSet = new HashSet<>(vo.getList().size());
        List<AuthUserDataPermission> collect = vo.getList().stream().map(
                it -> {
                    if (!valueSet.contains(it.getPermissionValue())){
                        AuthUserDataPermission authUserDataPermission = new AuthUserDataPermission();
                        authUserDataPermission.setUserId(userId);
                        authUserDataPermission.setPermissionType(type.byteValue());
                        authUserDataPermission.setPermissionValue(it.getPermissionValue());
                        authUserDataPermission.setTenantId(tenantId);
                        authUserDataPermission.setCreateTime(new Date());
                        authUserDataPermission.setUpdateTime(new Date());
                        valueSet.add(it.getPermissionValue());
                        return authUserDataPermission;
                    }
                    return null;
                }
        ).filter(Objects::nonNull).collect(Collectors.toList());
        long count = collect.stream().filter(it -> it.getPermissionValue().equals(AuthGlobal.ALL)).count();
        if (count > 0) {
            collect = collect.stream().filter(it -> it.getPermissionValue().equals(AuthGlobal.ALL)).collect(Collectors.toList());
        }
        authUserDataPermissionDao.batchAdd(collect);
    }

}
