package net.xianmu.authentication.service;

import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.UserPropertiesExtInput;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.model.VO.QueryCareWxVO;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

/**
 * Description:微信相关接口层
 * date: 2022/7/22 16:30
 *
 * <AUTHOR>
 */
public interface WechatService {

    /**
     * 查询微信账号是否关注公众号
     * @param unionId 微信unionId
     * @return 是否关注公众号，true：已关注，false：未关注
     */
    boolean toPayAttention(String unionId);

    DubboResponse<Boolean> createExtends(SystemOriginEnum systemOriginEnum, List<UserPropertiesExtInput> userPropertiesExtInputs);

    AuthUserAuthResp queryUserAuth(SystemOriginEnum systemOriginEnum, Long userBaseId, Integer type) ;

    List<AuthUserAuthResp> queryUsersAuth(SystemOriginEnum systemOriginEnum, List<Long> userBaseIds, Integer type);

    AuthUserAuthResp queryUserAuthByUserId(SystemOriginEnum systemOriginEnum, String userId, Integer type);

    String queryCare(QueryCareWxVO queryCareWxVO);
}
