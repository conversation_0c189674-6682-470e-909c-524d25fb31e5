package net.xianmu.authentication.service.impl;

import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class BaseService {
    @Resource
    private AuthUserDao authUserDao;
    @Resource
    private AuthUserBaseDao authUserBaseDao;


    public UserBase getUserBase(Long userId) {
        if (userId == null || userId <= 0) {
            throw new BizException("账号不存在");
        }
        AuthUser authUser = authUserDao.selectByPrimaryKey(userId);
        if (authUser == null) {
            throw new BizException("账号不存在或已经被删除");
        }
        UserBase userBase = new UserBase();
        BeanUtils.copyProperties(authUser, userBase);
        Long userBaseId = authUser.getUserBaseId();
        AuthUserBase authUserBase = authUserBaseDao.selectByPrimaryKey(userBaseId);
        if (authUserBase != null) {
            BeanUtils.copyProperties(authUserBase, userBase);
        }
        userBase.setId(authUser.getId());
        return userBase;
    }


    public ShiroUser getShiroUser() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        return user;
    }




}
