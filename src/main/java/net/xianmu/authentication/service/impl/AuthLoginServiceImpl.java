package net.xianmu.authentication.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.dingtalk.api.response.OapiUserGetResponse;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.MD5Util;
import net.summerfarm.common.util.StringUtils;
import net.summerfarm.common.util.weixin.WeChatUtils;
import net.summerfarm.contexts.ResultConstant;
import net.summerfarm.enums.AdminAuthExtendEnum;
import net.summerfarm.pojo.DO.AdminAuthExtend;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.client.dto.user.AuthUseLockResp;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.LoginLockEnum;
import net.xianmu.authentication.client.input.UpdateAuthUserVO;
import net.xianmu.authentication.client.input.login.AuthMockLoginInput;
import net.xianmu.authentication.client.utils.RedisKeyUtils;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.authentication.common.contexts.Global;
import net.xianmu.authentication.common.enmu.LoginTypeEnum;
import net.xianmu.authentication.common.enmu.RoleTypeEnum;
import net.xianmu.authentication.common.util.DingTalkUtils;
import net.xianmu.authentication.common.util.MenuTreeUtils;
import net.xianmu.authentication.enums.AuthLoginTypeEnum;
import net.xianmu.authentication.enums.SuperRoleTypeEnum;
import net.xianmu.authentication.enums.UserStatusEnum;
import net.xianmu.authentication.facade.UserCenterFacade;
import net.xianmu.authentication.mapper.auth.*;
import net.xianmu.authentication.model.DTO.AuthMenuPurviewDTO;
import net.xianmu.authentication.model.DTO.AuthPeronUserInfoDTO;
import net.xianmu.authentication.model.DTO.ShiroUserExtendDto;
import net.xianmu.authentication.model.VO.AuthLoginVO;
import net.xianmu.authentication.service.AuthLoginService;
import net.xianmu.authentication.service.AuthUserService;
import net.xianmu.authentication.service.feishu.FeiShuService;
import net.xianmu.authentication.service.feishu.impl.FeiShuServiceImpl;
import net.xianmu.authentication.tripartite.AuthSubjectFactory;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.result.ResultStatusEnum;
import net.xianmu.common.user.UserBase;
import net.xianmu.i18n.exception.I18nBizException;
import net.xianmu.i18n.util.XianmuI18nUtil;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authc.UsernamePasswordToken;
import org.apache.shiro.session.Session;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.ThreadContext;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.contexts.Global.AUTH_USER_EXTEND_KEY;

@Service
@Slf4j
public class AuthLoginServiceImpl extends BaseService implements AuthLoginService {
    @Resource
    AuthUserBaseDao userBaseDao;
    @Resource
    AuthUserDao authUserDao;
    @Resource(name = "authRedisTemplate")
    RedisTemplate authRedisTemplate;
    @Resource(name = "redisTemplate")
    RedisTemplate redisTemplate;
    @Resource
    AuthUserRoleDao userRoleDao;
    @Resource
    AuthRoleDao authRoleDao;
    @Resource
    AuthMenuPurviewDao authMenuPurviewDao;
    @Resource
    AuthUserService authUserBase;
    @Resource
    private AuthUserAuthDao authUserAuthDao;
    @Resource
    AuthSubjectFactory authSubjectFactory;
    @Resource
    UserCenterFacade userCenterFacade;
    @Resource
    FeiShuService feiShuService;

    @Resource
    DingTalkUtils dingTalkUtils;

    private static final Integer COSFO_MANAGE_PERSSION_ID = 119;

    @NacosValue(value = "${authLockCount:5}")
    private Integer authLockCount;

    @NacosValue(value = "${authLockTime:30}")
    private Long authLockTime;


    @NacosValue(value = "${cosfologin.feishu.expireHour:72}", autoRefreshed = true)
    private Long bossLoginExpireHour;


    /**
     * 通用登录
     *
     * @param authLoginVO
     * @return
     */
    @Override
    public AjaxResult<AuthLoginDto> login(AuthLoginVO authLoginVO) {
        // 1.必要参数校验
        SystemOriginEnum originEnum = SystemOriginEnum.getSystemOriginByType(authLoginVO.getOrigin());
        LoginTypeEnum loginType = LoginTypeEnum.getLoginType(authLoginVO.getType());
        if (loginType == null) {
            throw new DefaultServiceException("错误的登陆类型");
        }
        if (originEnum == null) {
            throw new DefaultServiceException("错误的登陆来源");
        }
        if (LoginTypeEnum.PHONE_PWD.equals(loginType) && authLoginVO.getPhone() == null) {
            log.error("手机号为空!authLoginVO:{}", JSON.toJSONString(authLoginVO));
            throw new DefaultServiceException("手机号为空");
        }
        if (LoginTypeEnum.NAME_PWD.equals(loginType) && authLoginVO.getUsername() == null) {
            log.error("用户名为空!authLoginVO:{}", JSON.toJSONString(authLoginVO));
            throw new DefaultServiceException("用户名为空");
        }


        // 2.校验账户是否被锁定
        if (LoginTypeEnum.PHONE_PWD.equals(loginType)) {
            checkSafeLogin(originEnum.type, authLoginVO.getPhone());
        } else if (LoginTypeEnum.NAME_PWD.equals(loginType)) {
            checkSafeLogin(originEnum.type, authLoginVO.getUsername());
        }

        // 3.获取用户信息
        AuthUserBase userBase = findUserBase(authLoginVO, loginType);
        if (userBase == null) {
            throw new DefaultServiceException("账号不存在");
        }
        log.info("获取到base用户信息：{}", JSON.toJSONString(userBase));
        List<AuthUser> userList = authUserDao.selectByUserBaseId(userBase.getId());
        AuthUser user = this.getUserForLogin(userBase, authLoginVO, originEnum);


        // 4.校验密码,失败后记录失败次数
        if (Arrays.asList(LoginTypeEnum.PHONE_PWD.name, LoginTypeEnum.NAME_PWD.name).contains(loginType.name) && !Objects.equals(user.getPassword(), MD5Util.string2MD5(authLoginVO.getPassword())) && !authLoginVO.getSuperMan()) {

            if (LoginTypeEnum.NAME_PWD.equals(loginType)) {
                pwdErrorLoginHandler(originEnum.type, authLoginVO.getUsername());
            } else {
                pwdErrorLoginHandler(originEnum.type, authLoginVO.getPhone());
            }
            log.error("账号不存在/密码错误.user:{}, reqVo:{}", JSON.toJSONString(user), JSON.toJSONString(authLoginVO));
            throw new DefaultServiceException("账号不存在/密码错误");
        }


        // 5.登录
        //shiro登录 shirouser仅仅放最基础的信息
        ShiroUser shiroUser = buildShiroUser(user, userBase, userList, originEnum, loginType);
        //登陆
        AuthLoginDto authLoginRespDTO = this.shiroLogin(shiroUser, loginType, originEnum);


        // 6.后置处理，登录成功后删除账户锁？
        this.handleAfterLogin(authLoginVO, loginType, originEnum);
        return AjaxResult.getOK(authLoginRespDTO);
    }


    private AuthUser getUserForLogin(AuthUserBase userBase, AuthLoginVO authLoginVO, SystemOriginEnum originEnum) {
        //查询是否多个租户id 是的话 直接返回 并存储redis 账号登陆信息
        List<AuthUser> userList = authUserDao.selectByUserBaseId(userBase.getId());
        if (CollectionUtils.isEmpty(userList)) {
            log.error("用户数据绑定错误.userBase:{}", JSON.toJSONString(userBase));
            throw new DefaultServiceException("用户数据绑定错误");
        }
        //根据来源查询
        AuthUser user;
        if (SystemOriginEnum.COSFO_MALL.type.equals(originEnum.type)) {
            List<AuthUser> collect = userList.stream().filter(it -> Objects.equals(it.getSystemOrigin(), originEnum.getType())).filter(it -> Objects.equals(it.getTenantId(), authLoginVO.getTenantId())).filter(it -> it.getStatus() == UserStatusEnum.VALID.getStatus().byteValue()).filter(it -> it.getAuditStatus() == 1).collect(Collectors.toList());

            if (CollectionUtils.isEmpty(collect)) {
                log.error("账号不存在!userBase :{}, authLoginVO:{}, originEnum:{}", JSON.toJSONString(userBase), JSON.toJSONString(authLoginVO), originEnum);
                throw new DefaultServiceException("账号不存在");
            }

            // 获取最近登录的一条
            collect.sort(Comparator.comparing(AuthUser::getLastLoginTime, Comparator.nullsLast(Comparator.reverseOrder())));
            user = collect.get(0);

        } else {
            user = userList.stream().filter(it -> Objects.equals(it.getSystemOrigin(), originEnum.getType())).sorted(Comparator.comparing(AuthUser::getLastLoginTime, Comparator.nullsLast(Comparator.reverseOrder()))).findFirst().orElse(null);
            if (user == null) {
                log.error("账号不存在!userBase :{}, authLoginVO:{}, originEnum:{}", JSON.toJSONString(userBase), JSON.toJSONString(authLoginVO), originEnum);
                throw new DefaultServiceException("账号不存在");
            }

            if (authLoginVO.getTenantId() != null && authLoginVO.getTenantId() > 0) {
                user = userList.stream().filter(it -> Objects.equals(it.getTenantId(), authLoginVO.getTenantId()) && Objects.equals(it.getSystemOrigin(), originEnum.getType())).sorted(Comparator.comparing(AuthUser::getLastLoginTime, Comparator.nullsLast(Comparator.reverseOrder()))).findFirst().orElse(null);
                if (user == null) {
                    log.error("账号不存在!userBase :{}, authLoginVO:{}, originEnum:{}", JSON.toJSONString(userBase), JSON.toJSONString(authLoginVO), originEnum);
                    throw new DefaultServiceException("租户信息不存在");
                }
            }

            if (user.getStatus() == UserStatusEnum.IN_VALID.getStatus().byteValue()) {
                log.error("账号被锁定!userBase :{}, authLoginVO:{}, originEnum:{}", JSON.toJSONString(userBase), JSON.toJSONString(authLoginVO), originEnum);
                throw new DefaultServiceException("账号被锁定");
            }
        }
        return user;
    }

    @Override
    public AuthLoginDto mockLogin(AuthUserBase userBase, AuthUser user, SystemOriginEnum originEnum, LoginTypeEnum loginType) {
        if (user.getStatus() == UserStatusEnum.IN_VALID.getStatus().byteValue()) {
            throw new DefaultServiceException("账号被锁定");
        }

        //shiro登录
        ShiroUser shiroUser = buildShiroUser(user, userBase, Collections.singletonList(user), originEnum, loginType);

        //登陆
        return this.shiroLogin(shiroUser, loginType, originEnum);
    }

    public List<AuthUser> getAllAuthUser(Long userBaseId) {
        return authUserDao.selectByUserBaseId(userBaseId);
    }

    public ShiroUser buildShiroUser(AuthUser user, AuthUserBase userBase, List<AuthUser> userList, SystemOriginEnum originEnum, LoginTypeEnum loginTypeEnum) {
        Long userId = user.getId();
        List<Integer> roleIds = userRoleDao.selectRoleIdByUserId(userId);
        List<Long> authIds = userList.stream().map(AuthUser::getId).collect(Collectors.toList());
        Long tenantId = user.getTenantId() == null ? 1L : user.getTenantId();
        ShiroUser shiroUser = new ShiroUser();
        shiroUser.setId(user.getId());
        shiroUser.setBizUserId(user.getBizUserId());
        shiroUser.setUsername(userBase.getUsername());
        shiroUser.setPassword(userBase.getPassword());
        shiroUser.setNickname(userBase.getNickname());
        shiroUser.setRoleIds(roleIds);
        shiroUser.setTenantId(tenantId);
        shiroUser.setLoginType(loginTypeEnum.name);
        shiroUser.setBaseUserId(user.getUserBaseId());
        shiroUser.setPhone(userBase.getPhone());
        shiroUser.setEmail(userBase.getEmail());
        shiroUser.setLogo(userBase.getLogo());
        shiroUser.setSystemOrigin(originEnum.getName());
        shiroUser.setStatus(UserStatusEnum.VALID.getStatus());
        shiroUser.setBizUserId(user.getBizUserId());
        if (shiroUser.getPassword() == null) {
            shiroUser.setPassword(user.getPassword());
        }

        // 这里看起来是获取一个base用户下的所有auth_user id 的角色
        // 暂时不理解是在干嘛
        if (!CollectionUtils.isEmpty(authIds)) {
            List<Integer> allRoleIds = userRoleDao.selectRoleIdByUserIds(authIds);
            shiroUser.setAllRoleIds(allRoleIds);
        }
        if (SystemOriginEnum.COSFO_MANAGE.name.equals(shiroUser.getSystemOrigin())) {
            List<Integer> allRoleIds = shiroUser.getAllRoleIds() == null ? new ArrayList<>() : shiroUser.getAllRoleIds();
            allRoleIds.add(COSFO_MANAGE_PERSSION_ID);
            shiroUser.setAllRoleIds(allRoleIds);
        }

        // 获取当前用户租户体系的所有角色(用于超管相关的逻辑)
        buildAllRoleId(shiroUser, originEnum);
        //saas 兼容nickName
        mergeSaasTenantAccountName(shiroUser, originEnum);
        //role 角色配置
        buildShiroUserRole(shiroUser, roleIds);
        return shiroUser;
    }

    private void mergeSaasTenantAccountName(ShiroUser shiroUser, SystemOriginEnum originEnum) {
        if (originEnum.type.equals(SystemOriginEnum.COSFO_MANAGE.type)) {
            String tenantName = userCenterFacade.getTenantUserName(shiroUser.getId());
            shiroUser.setNickname(tenantName);
        }
    }

    /**
     * 根据角色ID列表构建Shiro用户角色
     */
    private void buildShiroUserRole(ShiroUser shiroUser, List<Integer> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        List<AuthRole> authRoles = authRoleDao.selectByIds(roleIds);
        if (CollectionUtils.isEmpty(authRoles)) {
            return;
        }
        Set<String> collect = authRoles.stream().map(this::toRoleStr).collect(Collectors.toSet());
        shiroUser.setRoles(collect);
    }

    private String toRoleStr(AuthRole authRole) {
        Byte systemOrigin = authRole.getSystemOrigin();
        SystemOriginEnum systemOriginByType = SystemOriginEnum.getSystemOriginByType(Integer.valueOf(systemOrigin));
        Byte superAdmin = authRole.getSuperAdmin();
        SuperRoleTypeEnum superRoleTypeEnumByStatus = SuperRoleTypeEnum.getSuperRoleTypeEnumByStatus(Integer.valueOf(superAdmin));
        return systemOriginByType.name + ":" + superRoleTypeEnumByStatus.getCode();
    }


    private void buildAllRoleId(ShiroUser shiroUser, SystemOriginEnum originEnum) {
        List<Integer> roleIds = shiroUser.getRoleIds();
        if (!CollectionUtils.isEmpty(roleIds)) {
            List<AuthRole> authRoles = authRoleDao.selectByIds(roleIds);
            if (CollectionUtils.isEmpty(roleIds)) {
                return;
            }
            Long count = authRoles.stream().filter(it -> it.getSuperAdmin() != null && it.getSuperAdmin().intValue() == RoleTypeEnum.SUPER.code).count();
            if (count > 0) {
                //获取当前用户租户体系的所有角色
                List<Integer> allRoleIds = authRoleDao.selectRoleIdBySourceAndTenantId(originEnum.type, shiroUser.getTenantId());
                List<Integer> shiroAllRoleIds = shiroUser.getAllRoleIds() == null ? new ArrayList<>() : shiroUser.getAllRoleIds();
                shiroAllRoleIds.addAll(allRoleIds);
                shiroUser.setAllRoleIds(shiroAllRoleIds);
            }
        }
    }

    @Override
    public AjaxResult loginByToken(AuthLoginVO authLoginVO) {
        authLoginVO.setType(LoginTypeEnum.SECOND_TOKEN.name);
        return login(authLoginVO);
    }

    @Override
    public AjaxResult<AuthPeronUserInfoDTO> personalInfo() {
        AuthPeronUserInfoDTO authPeronUserInfo = new AuthPeronUserInfoDTO();
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        Long userId = user.getId();
        //获取角色
        UserBase userBase = getUserBase(userId);
        BeanUtils.copyProperties(userBase, authPeronUserInfo);
        AuthUser authUser = authUserDao.selectByPrimaryKey(userId);
        authPeronUserInfo.setBizUserId(authUser.getBizUserId());
        authPeronUserInfo.setRealName(userBase.getNickname());
        //获取菜单
        List<AuthRole> roles = authRoleDao.selectByUserId(userId);
        authPeronUserInfo.setRoleVOs(roles);
        Set<Long> roledIds = roles.stream().map(AuthRole::getId).collect(Collectors.toSet());
        //如果包括超级管理员
        Optional<AuthRole> any = roles.stream().filter(it -> it.getSuperAdmin().equals((byte) 1)).findAny();
        Boolean superMan = any.isPresent();
        List<AuthMenuPurview> authMenuPurviews = new ArrayList<>();
        if (superMan) {
            //查询这个来源的菜单
            authMenuPurviews.addAll(authMenuPurviewDao.selectMenus(userBase.getSystemOrigin()));
        } else {
            if (!CollectionUtils.isEmpty(roledIds)) {
                authMenuPurviews.addAll(authMenuPurviewDao.selectByRolIds(userBase.getSystemOrigin(), roledIds, null));
            }
        }
        List<AuthMenuPurview> collect = authMenuPurviews.stream().filter(it -> it.getType().intValue() != 1).collect(Collectors.toList());
        List<AuthMenuPurviewDto> authMenuPurviewDtos = MenuTreeUtils.convertTree(collect);
        List<AuthMenuPurviewDTO> outs = new ArrayList<>();
        for (AuthMenuPurviewDto it : authMenuPurviewDtos) {
            String modele = it.getMenuName();
            AuthMenuPurviewDTO menuPurviewDto = new AuthMenuPurviewDTO();
            BeanUtils.copyProperties(it, menuPurviewDto);
            menuPurviewDto.setModule(modele);
            outs.add(menuPurviewDto);
            if (CollectionUtils.isEmpty(it.getChildren())) {
                continue;
            }
            for (AuthMenuPurviewDto it1 : it.getChildren()) {
                AuthMenuPurviewDTO menuPurviewDto1 = new AuthMenuPurviewDTO();
                BeanUtils.copyProperties(it1, menuPurviewDto1);
                menuPurviewDto1.setModule(modele);
                outs.add(menuPurviewDto1);
                if (CollectionUtils.isEmpty(it1.getChildren())) {
                    continue;
                }
                for (AuthMenuPurviewDto it2 : it1.getChildren()) {
                    AuthMenuPurviewDTO menuPurviewDto2 = new AuthMenuPurviewDTO();
                    BeanUtils.copyProperties(it2, menuPurviewDto2);
                    menuPurviewDto2.setModule(modele);
                    outs.add(menuPurviewDto2);
                }
            }
        }
        authPeronUserInfo.setMenus(outs);
        authPeronUserInfo.setPassword(null);
        return AjaxResult.getOK(authPeronUserInfo);
    }


    /**
     * 疑似没有使用.而且这个接口设计得也存在歧义：（内部的实现和phone没有关系）,观察一段时间后下掉
     */
    @Override
    @Transactional
    @Deprecated
    public AjaxResult updateAuthUserByPhone(UpdateAuthUserVO updateAuthUserVO) {
        log.warn("疑似没有使用的接口被调用了.updateAuthUserVO:{}", JSON.toJSONString(updateAuthUserVO));
        UserBase baseUser = new UserBase();
        baseUser.setId(updateAuthUserVO.getUserBaseId());
        BeanUtils.copyProperties(updateAuthUserVO, baseUser);
        authUserBase.updateUser(null, baseUser);
        return AjaxResult.getOK();
    }

    @Override
    public AjaxResult<AuthLoginDto> authLogin(Integer type, String code) {
        return authLogin(type, code, null, null);
    }

    /**
     * @param unionId unionId 每个用户在同一个飞书组织下的唯一标识（不同的app获取到的union ID 相同）
     * @return
     */
    //    https://open.feishu.cn/document/server-docs/contact-v3/user/get
    @Override
    public CommonResult<AuthLoginDto> loginWithFeishuUnionId(String unionId) {
        String userId = feiShuService.queryUserIdByUnionId(unionId);
        if (StringUtils.isEmpty(userId)) {
            return CommonResult.fail(ResultStatusEnum.NOT_FOUND, "获取飞书用户信息失败");
        }
        log.info("飞书unionId登录，userId:{}", userId);
        AdminAuthExtend crmExtend = new AdminAuthExtend();
        crmExtend.setUserId(userId);
        crmExtend.setType(AuthTypeEnum.FEI_SHU.getType());

        AuthLoginDto authLoginDto = loginWithCrmExtend(crmExtend, SystemOriginEnum.ADMIN, AuthTypeEnum.FEI_SHU.getType(), null, unionId);
        if (authLoginDto == null) {
            return CommonResult.fail(ResultStatusEnum.SERVER_ERROR, "使用UnionId登录失败");
        }
        return CommonResult.ok(authLoginDto);
    }

    @Override
    public AjaxResult<AuthLoginDto> authLogin(Integer type, String code, Integer systemOrigin, String channel) {
        log.info("开始授权登陆 type:{}, code:{}, systemOrigin:{}, channel:{}", type, code, systemOrigin, channel);
        SystemOriginEnum systemOriginEnum;
        if (systemOrigin == null) {
            systemOriginEnum = SystemOriginEnum.ADMIN;
        } else {
            systemOriginEnum = SystemOriginEnum.getSystemOriginByType(systemOrigin);
        }
        if (systemOriginEnum == null) {
            throw new BizException("获取系统来源异常");
        }
        AdminAuthExtend crmExtend = getCrmExtend(systemOriginEnum, type, code, channel);

        if (crmExtend == null) {
            log.error("第三方授权失败  type:{},code {}", type, code);
            return AjaxResult.getErrorWithMsg("第三方授权失败");
        }
        String uuid = UUID.randomUUID().toString();
        AuthLoginDto authLoginDto = loginWithCrmExtend(crmExtend, systemOriginEnum, type, channel, uuid);
        if (authLoginDto == null) {
            // 需要返回的格式是：{"code":"UNBIND","data":"fbc542ad-8b66-4905-90eb-dee180fa4e41","msg":"账号未绑定","success":false}
            return AjaxResult.getError(ResultConstant.UNBIND, "账号未绑定", uuid);
        }
        return AjaxResult.getOK(authLoginDto);
    }

    private AuthLoginDto loginWithCrmExtend(AdminAuthExtend crmExtend, SystemOriginEnum systemOriginEnum, int authTypeEnum, String channel, String uuid) {
        long startTime = System.currentTimeMillis();
        List<AuthUserAuth> exitAuthUserAuth = getExitAuthUserAuth(authTypeEnum, crmExtend);
        if (CollectionUtils.isEmpty(exitAuthUserAuth)) {
            log.info("放到redis type:{}, crmExtend:{}", authTypeEnum, JSONUtil.toJsonStr(crmExtend));
            authRedisTemplate.opsForHash().put("AUTH_LOGIN_INFO", uuid, JSONUtil.toJsonStr(crmExtend));
            return null;
        }
        AuthUser authUser = loginGetAuthUser(exitAuthUserAuth, systemOriginEnum);
        if (UserStatusEnum.IN_VALID.getStatus().equals(authUser.getStatus().intValue())) {
            throw new DefaultServiceException(ResultConstant.USER_BANNED, "账号被锁定，请联系管理员解锁");
        }
        AuthUserBase authUserBase = userBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (authUserBase == null) {
            throw new ProviderException("当前账号信息异常");
        }
        List<AuthUser> authUserList = getAllAuthUser(authUserBase.getId());
        if (CollectionUtils.isEmpty(authUserList)) {
            throw new ProviderException("当前账号信息异常");
        }
        //进行shiro登录
        try {
            Long expireHour = null;
            if (FeiShuServiceImpl.BOSS_LOGIN.equalsIgnoreCase(channel)) {
                expireHour = bossLoginExpireHour;
            }
            ShiroUser shiroUser = buildShiroUser(authUser, authUserBase, authUserList, systemOriginEnum, LoginTypeEnum.SECOND_TOKEN);
            //登陆
            AuthLoginDto authLoginRespDTO = shiroLogin(shiroUser, LoginTypeEnum.SECOND_TOKEN, systemOriginEnum, expireHour);
            log.info("自动授权登录成功,origin:【{}】,authType:【{}】,username:【{}】,time:【{}】", systemOriginEnum.getName(), "authLoginTypeEnum.getName()", authUserBase.getUsername(), System.currentTimeMillis() - startTime);
            return authLoginRespDTO;
        } catch (Exception e) {
            log.error("自动授权登录失败,origin:【{}】,authType:【{}】,username:【{}】,time:【{}】", systemOriginEnum.getName(), "authLoginTypeEnum.getName()", authUserBase.getUsername(), System.currentTimeMillis() - startTime, e);
            throw new ProviderException("用户名或密码错误");
        }
    }


    public AuthUser loginGetAuthUser(List<AuthUserAuth> exitAuthUserAuth, SystemOriginEnum systemOriginEnum) {
        List<Long> authUserIds = exitAuthUserAuth.stream().map(AuthUserAuth::getUserId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(authUserIds)) {
            throw new BizException("用户信息绑定错误");
        }
        List<AuthUser> authUsers = authUserDao.selectByUserIds(authUserIds);
        if (CollectionUtils.isEmpty(authUsers)) {
            log.warn("authUser is null extend {} ", JSONUtil.toJsonStr(exitAuthUserAuth));
            throw new BizException("用户信息绑定错误");
        }
        AuthUser authUser = authUsers.stream().filter(it -> systemOriginEnum.getType().equals(it.getSystemOrigin())).findFirst().orElse(null);
        if (authUser == null) {
            log.warn("authUser is null  extend:{}", JSONUtil.toJsonStr(exitAuthUserAuth));
            throw new BizException("用户信息绑定错误");
        }
        return authUser;
    }

    @Override
    public AjaxResult authBind(Integer type, String username, String password, String uuid) {
        long startTime = System.currentTimeMillis();
        if (type != AdminAuthExtendEnum.Type.DING_TALK.ordinal() && type != AdminAuthExtendEnum.Type.WX_CRM.ordinal()) {
            return AjaxResult.getError(ResultConstant.PARAM_FAULT);
        }
        if (!StringUtils.isEmail(username)) {
            return AjaxResult.getError(ResultConstant.USER_NAME_WRONG);
        }

        SystemOriginEnum systemOriginEnum = SystemOriginEnum.ADMIN;
        AuthUserBase authUserBase = this.findByPhone(username);
        if (authUserBase == null) {
            return AjaxResult.getError(ResultConstant.USER_OR_PASSWORD_WRONG);
        }

        Object object = authRedisTemplate.opsForHash().get("AUTH_LOGIN_INFO", uuid);
        AdminAuthExtend adminAuthExtend = null;
        if (object != null) {
            adminAuthExtend = JSONUtil.toBean(object.toString(), AdminAuthExtend.class);
        }
        if (adminAuthExtend == null) {
            return AjaxResult.getErrorWithMsg("授权信息已过期，请退出后重新进入小程序");
        }
        List<AuthUser> authUserList = getAllAuthUser(authUserBase.getId());
        if (CollectionUtils.isEmpty(authUserList)) {
            throw new DefaultServiceException(ResultConstant.USER_OR_PASSWORD_WRONG, XianmuI18nUtil.getI18nValue("该账号还未激活{0}", systemOriginEnum.getName()));
        }
        AuthUser authUser = authUserList.stream().filter(it -> it.getSystemOrigin().equals(systemOriginEnum.getType())).findFirst().orElse(null);
        if (authUser == null) {
            throw new DefaultServiceException(ResultConstant.USER_OR_PASSWORD_WRONG, XianmuI18nUtil.getI18nValue("该账号还未激活{0}", systemOriginEnum.getName()));
        }
        if (!Objects.equals(authUser.getPassword(), MD5Util.string2MD5(password))) {
            return AjaxResult.getError(ResultConstant.USER_OR_PASSWORD_WRONG);
        }
        if (UserStatusEnum.IN_VALID.getStatus().equals(authUser.getStatus().intValue())) {
            throw new DefaultServiceException(ResultConstant.USER_BANNED, "账号被锁定，请联系管理员解锁");
        }

        //校验是否进行授权绑定
        List<AuthUserAuth> userAuthRecord = authUserAuthDao.selectUserAuthRecord(type, authUser.getId());
        if (CollectionUtils.isEmpty(userAuthRecord)) {
            //进行授权绑定
            //authLoginTypeEnum.getAuthType(), loginBO.getAuthId()
            AuthUserAuth userAuthBind = new AuthUserAuth();
            userAuthBind.setUserId(authUser.getId());
            userAuthBind.setAuthId(adminAuthExtend.getOpenid());
            if (type == AdminAuthExtendEnum.Type.DING_TALK.ordinal()) {
                userAuthBind.setThirdPartyId(adminAuthExtend.getUserId());
            } else {
                userAuthBind.setThirdPartyId(adminAuthExtend.getOpenid());
            }
            userAuthBind.setAuthType(type.byteValue());
            authUserAuthDao.insertSelective(userAuthBind);
        }
        AuthLoginTypeEnum authLoginType = AuthLoginTypeEnum.getAuthLoginType(type);
        authSubjectFactory.getAuthSubject(authLoginType).handleUserBind(adminAuthExtend.getUnionId(), authUser.getId());

        //未登录进行登录鉴权
        SecurityUtils.getSubject().logout();
        try {
            //shiro登录 shirouser仅仅放最基础的信息
            ShiroUser shiroUser = buildShiroUser(authUser, authUserBase, authUserList, systemOriginEnum, LoginTypeEnum.SECOND_TOKEN);
            //登陆
            AuthLoginDto authLoginRespDTO = shiroLogin(shiroUser, LoginTypeEnum.SECOND_TOKEN, systemOriginEnum);
            log.info("账号密码登录成功,origin:【{}】,authType:【{}】,username:【{}】,time:【{}】", systemOriginEnum.getName(), "authLoginTypeEnum.getName()", authUserBase.getUsername(), System.currentTimeMillis() - startTime);
            authRedisTemplate.opsForHash().delete("AUTH_LOGIN_INFO", uuid);
            return AjaxResult.getOK(authLoginRespDTO);
        } catch (Exception e) {
            log.error("账号密码登录失败,origin:【{}】,authType:【{}】,username:【{}】,time:【{}】", systemOriginEnum.getName(), "authLoginTypeEnum.getName()", authUserBase.getUsername(), System.currentTimeMillis() - startTime, e);
            throw new ProviderException("用户名或密码错误");
        }
    }

    @Override
    public AjaxResult authLogout(Integer type) {
        Subject subject = SecurityUtils.getSubject();
        if (subject.isAuthenticated()) {
            ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
            Long userId = user.getId();
            authUserAuthDao.deleteByAuthIdType(userId, type);
            //退出登录
            subject.logout();
        }
        return AjaxResult.getOK();
    }

    @Override
    public Boolean checkSafeLogin(Integer originEnum, String phone) {
        if (!Arrays.asList(SystemOriginEnum.COSFO_MANAGE.type, SystemOriginEnum.COSFO_OMS.type).contains(originEnum)) {
            return true;
        }
        String redisKey = String.format(AuthGlobal.AUTH_BIND_REDIS_KEY, originEnum, phone);
        Object o = redisTemplate.opsForValue().get(redisKey);
        if (o != null) {
            Integer count = Integer.valueOf((String) o);
            if (count >= authLockCount) {
                AuthUseLockResp authUseLockResp = new AuthUseLockResp();
                Long expire = redisTemplate.getExpire(redisKey, TimeUnit.SECONDS);
                authUseLockResp.setLockTime(expire);
                authUseLockResp.setErrorCount(count);
                authUseLockResp.setTotalCount(authLockCount);
                throw new BizException(JSONUtil.toJsonStr(authUseLockResp), 500, LoginLockEnum.TEMPORARY_LOCK.name);
            }
        }
        return true;
    }

    @Override
    public void pwdErrorLoginHandler(Integer originEnum, String key) {
        if (!Arrays.asList(SystemOriginEnum.COSFO_MANAGE.type, SystemOriginEnum.COSFO_OMS.type).contains(originEnum)) {
            return;
        }
        String redisKey = String.format(AuthGlobal.AUTH_BIND_REDIS_KEY, originEnum, key);
        Object o = redisTemplate.opsForValue().get(redisKey);
        long count = o == null ? 0L : Long.parseLong(o.toString());
        if (count == 0) {
            redisTemplate.opsForValue().set(redisKey, "1", 30, TimeUnit.DAYS);
        } else if (count < authLockCount - 1) {
            count = redisTemplate.opsForValue().increment(redisKey);
            redisTemplate.expire(redisKey, 30, TimeUnit.DAYS);
        } else {
            count = redisTemplate.opsForValue().increment(redisKey);
            redisTemplate.expire(redisKey, authLockTime, TimeUnit.MINUTES);
        }
        Long expire = redisTemplate.getExpire(redisKey, TimeUnit.SECONDS);
        AuthUseLockResp authUseLockResp = new AuthUseLockResp();
        authUseLockResp.setLockTime(expire);
        authUseLockResp.setErrorCount(count == 0L ? 1 : (int) count);
        authUseLockResp.setTotalCount(authLockCount);
        throw new BizException(JSONUtil.toJsonStr(authUseLockResp), 500, LoginLockEnum.TEMPORARY_LOCK.name);
    }

    @Override
    @Deprecated
    public void deleteBindRedis(Integer originEnum, String key) {
        String redisKey = String.format(AuthGlobal.AUTH_BIND_REDIS_KEY, originEnum, key);
        redisTemplate.delete(redisKey);
    }

    @Override
    public AuthLoginDto crossLogin(Integer systemOriginEnum, AuthMockLoginInput authMockLoginInput) {
        log.info("crossLogin systemOriginEnum {}, authMockLoginInput{}", systemOriginEnum, authMockLoginInput);
        ShiroUser shiroUser = buildCrossLoginShiro(authMockLoginInput);
        log.info("crossLogin systemOriginEnum {}, authMockLoginInput{} 构建shiro {}", systemOriginEnum, authMockLoginInput, shiroUser);

        //登陆
        AuthLoginDto authLoginRespDTO = shiroLogin(shiroUser, LoginTypeEnum.SECOND_TOKEN, SystemOriginEnum.getSystemOriginByType(systemOriginEnum), authMockLoginInput.getExpireTime());

        AuthRole authRole = authRoleDao.selectBySourceTenantSuperRole(authMockLoginInput.getToSystemOriginEnum(), authMockLoginInput.getToTenantId());
        if (authRole == null) {
            throw new I18nBizException("租户:{0}没有超级管理员", authMockLoginInput.getToTenantId());
        }
        Long authId = authRoleDao.selectAuthIdByRoleId(authMockLoginInput.getToSystemOriginEnum(), authRole.getId());

        ShiroUserExtendDto shiroUserExtendDto = new ShiroUserExtendDto();
        shiroUserExtendDto.setFromSystemOrigin(systemOriginEnum);
        shiroUserExtendDto.setToAuthId(authId);
        Long expire = authRedisTemplate.getExpire(authLoginRespDTO.getToken());
        if (expire < 0) {
            throw new BizException("登陆已失效");
        }

        authRedisTemplate.opsForValue().set(getExtendRedisKey(authLoginRespDTO.getToken()), JSONUtil.toJsonStr(shiroUserExtendDto), expire, TimeUnit.SECONDS);

        return authLoginRespDTO;
    }

    private String getExtendRedisKey(String token) {
        token = token.split("__")[1];
        String key = String.format(AUTH_USER_EXTEND_KEY, token);
        return key;
    }

    private ShiroUser buildCrossLoginShiro(AuthMockLoginInput authMockLoginInput) {
        ShiroUser shiroUser = new ShiroUser();
        //查询当前用户信息
        Long authUserId = authMockLoginInput.getAuthUserId();
        //shiroId authId拿当前登陆的id方便留痕和日志
        shiroUser.setId(authUserId);
        //tenantId
        shiroUser.setTenantId(authMockLoginInput.getToTenantId());
        //来源是 cosfo_manage
        shiroUser.setSystemOrigin(SystemOriginEnum.COSFO_MANAGE.name);
        //base user_id是 auth_user_base
        List<AuthUser> authUsers = authUserDao.selectByUserIds(Collections.singletonList(authUserId));
        if (CollectionUtils.isEmpty(authUsers)) {
            throw new BizException("authId找不到注册信息");
        }
        shiroUser.setAuthUsers(authUsers);
        shiroUser.setBizUserId(authUsers.get(0).getBizUserId());
        shiroUser.setBaseUserId(authUsers.get(0).getUserBaseId());
        shiroUser.setPassword(authUsers.get(0).getPassword());

        AuthUserBase authUserBase = userBaseDao.selectByPrimaryKey(authUsers.get(0).getUserBaseId());
        if (authUserBase != null) {
            shiroUser.setPhone(authUserBase.getPhone());
            shiroUser.setUsername(authUserBase.getPhone());

        }

        AuthRole authRole = authRoleDao.selectBySourceTenantSuperRole(authMockLoginInput.getToSystemOriginEnum(), authMockLoginInput.getToTenantId());
        if (authRole == null) {
            throw new I18nBizException("租户:{0}没有超级管理员", authMockLoginInput.getToTenantId());
        }


        Set<String> roleSet = new HashSet<>();
        roleSet.add(SystemOriginEnum.COSFO_OMS.name + ":" + SuperRoleTypeEnum.SUPER_READ_ROLE.getCode());
        shiroUser.setRoles(roleSet);
        shiroUser.setStatus(UserStatusEnum.VALID.getStatus());
        shiroUser.setLoginType(LoginTypeEnum.CROSS_LOGIN.name);
        //saas 兼容nickName
        String tenantName = userCenterFacade.getTenantUserName(shiroUser.getId());
        shiroUser.setNickname(tenantName);
        List<Integer> roleIds = new ArrayList<>();
        roleIds.add(COSFO_MANAGE_PERSSION_ID);
        roleIds.add(authRole.getId().intValue());

        //跳转admin_role 角色
        shiroUser.setRoleIds(roleIds);
        return shiroUser;
    }

    private List<AuthUserAuth> getExitAuthUserAuth(Integer type, AdminAuthExtend crmExtend) {
        List<AuthUserAuth> exit;
        if (Objects.equals(type, AuthTypeEnum.FEI_SHU.getType())) {
            exit = authUserAuthDao.selectByThirdIdType(AuthTypeEnum.FEI_SHU.getType(), crmExtend.getUserId());
        } else {
            exit = authUserAuthDao.selectByOpenIdType(type, crmExtend.getOpenid());
        }
        return exit;
    }


    private AdminAuthExtend getCrmExtend(SystemOriginEnum systemOriginEnum, Integer type, String code, String channel) {
        AdminAuthExtend authInfo = new AdminAuthExtend();
        authInfo.setType(type);
        if (type == AdminAuthExtendEnum.Type.DING_TALK.ordinal()) {
            OapiUserGetResponse oapiUser = dingTalkUtils.getOapiUser(code);
            log.info("钉钉应用授权登录，result:{}", JSONObject.toJSONString(oapiUser));
            if (oapiUser == null) {
                return null;
            }
            if (!oapiUser.isSuccess()) {
                log.info(oapiUser.getErrmsg());
                return null;
            }

            authInfo.setUserId(oapiUser.getUserid());
            authInfo.setOpenid(oapiUser.getOpenId());
            authInfo.setUnionId(oapiUser.getUnionid());
            return authInfo;
        }
        if (type == AdminAuthExtendEnum.Type.WX_CRM.ordinal()) {
            JSONObject json = WeChatUtils.getMpOpenId(code);
            if (json == null) {
                log.info("CRM微信授权登录error");
                return null;
            }
            log.info("CRM授权登录，result:{}", json.toJSONString());
            if (StringUtils.isBlank(json)) {
                return null;
            }
            authInfo.setOpenid(json.getString("openid"));
            authInfo.setUnionId(json.getString("unionid"));
            return authInfo;
        }

        if (Objects.equals(type, AuthTypeEnum.FEI_SHU.getType())) {
            String userId = feiShuService.queryUserIdByCode(systemOriginEnum, code, channel);
            authInfo.setUserId(userId);
            return authInfo;
        }
        return null;
    }

    public AuthLoginDto shiroLogin(ShiroUser shiroUser, LoginTypeEnum loginType, SystemOriginEnum originEnum, Long expireTime) {
        ThreadContext.remove(ThreadContext.SUBJECT_KEY);
        Long expireHour = expireTime == null ? 4L : expireTime;
        // session中只存放id,redis中存放详细用户信息
        Subject subject = SecurityUtils.getSubject();
        Session session = subject.getSession();
        String shiroUserId = shiroUser.getSystemOrigin() + ":" + shiroUser.getId();
        session.setAttribute(Global.SHIRO_USER_UUID, shiroUserId);
        session.setAttribute("authUserId", shiroUser.getId());
        log.info("登陆 获取到  shiroUser {} ", JSONUtil.toJsonStr(shiroUser));

        authRedisTemplate.opsForValue().set(RedisKeyUtils.getAuthUserKey(shiroUser.getId()), JSONUtil.toJsonStr(shiroUser), expireHour, TimeUnit.HOURS);
        authRedisTemplate.opsForHash().put(Global.SHIRO_USER_UUID, shiroUserId, shiroUser);

        //shiro登陆验证
        String loginUserName = LoginTypeEnum.NAME_PWD.name.equals(loginType.name) ? shiroUser.getUsername() : shiroUser.getPhone();
        UsernamePasswordToken loginInfo = new UsernamePasswordToken(loginUserName, shiroUser.getPassword());
        SecurityUtils.getSubject().login(loginInfo);
        log.info("登陆 userId：{} phone：{} username：{} 用户登陆成功！", shiroUser.getId(), shiroUser.getPhone(), shiroUser.getUsername());

        subject = SecurityUtils.getSubject();
        String token = getTokenIndex(originEnum) + subject.getSession().getId();

        AuthLoginDto authLoginDto = new AuthLoginDto();
        authLoginDto.setToken(token);
        authLoginDto.setUserBaseId(shiroUser.getBaseUserId());
        authLoginDto.setRealname(shiroUser.getNickname());
        authLoginDto.setBizUserId(shiroUser.getBizUserId());
        authLoginDto.setAuthId(shiroUser.getId());
        String phone = originEnum.getType().equals(SystemOriginEnum.ADMIN.getType()) ? shiroUser.getPhone() : shiroUser.getUsername();
        authLoginDto.setPhone(phone);
        log.info("登陆成功，token：{} userid：{}", authLoginDto.getToken(), authLoginDto.getUserBaseId());
        authRedisTemplate.opsForValue().set(token, JSONUtil.toJsonStr(shiroUser), expireHour, TimeUnit.HOURS);
        authRedisTemplate.expire(token, expireHour, TimeUnit.HOURS);
        if (expireTime != null) {
            session.setTimeout(expireHour * 3600000);
        }
        authUserDao.updateLastLoginTime(shiroUser.getId());
        return authLoginDto;
    }


    public AuthLoginDto shiroLogin(ShiroUser shiroUser, LoginTypeEnum loginType, SystemOriginEnum originEnum) {
        return shiroLogin(shiroUser, loginType, originEnum, null);
    }

    public AuthUserBase findByPhone(String userName) {
        AuthUserBase queryUserBase = new AuthUserBase();
        queryUserBase.setUsername(userName);
        return userBaseDao.selectByUserBase(queryUserBase);
    }

    public AuthUserBase findByPhoneOrUserName(String loginName) {
        AuthUserBase queryUserBase = new AuthUserBase();
        queryUserBase.setPhone(loginName);
        AuthUserBase authUserBase = userBaseDao.selectByUserBase(queryUserBase);
        if (authUserBase == null) {
            queryUserBase.setPhone(null);
            queryUserBase.setUsername(loginName);
            authUserBase = userBaseDao.selectByUserBase(queryUserBase);
        }
        return authUserBase;
    }

    /**
     * 查询userBase
     */
    public AuthUserBase findUserBase(AuthLoginVO authLoginVO, LoginTypeEnum loginType) {
        AuthUserBase queryUserBase = new AuthUserBase();
        //如果是token登陆 获取id查到 userBase
        if (loginType.name.equals(LoginTypeEnum.SECOND_TOKEN.name)) {
            String token = authLoginVO.getToken();
            Object obj = authRedisTemplate.opsForValue().get(token);
            if (obj == null) {
                return null;
            }
            Long userId = (Long) obj;
            queryUserBase.setId(userId);
        } //手机账号 手机验证码 根据手机号查询
        else if (Arrays.asList(LoginTypeEnum.PHONE_PWD.name, LoginTypeEnum.PHONE_MESSAGE.name).contains(loginType.name)) {
            queryUserBase.setPhone(authLoginVO.getPhone());
        } //根据账号密码登陆
        else if (LoginTypeEnum.NAME_PWD.name.equals(loginType.name)) {
            queryUserBase.setUsername(authLoginVO.getUsername());
        } else {
            throw new BizException("参数错误");
        }

        return userBaseDao.selectByUserBase(queryUserBase);
    }

    /**
     * 创建短信验证码.
     *
     * @param length 长度
     * @return 验证码
     */
    static int buildRandom(int length) {
        int num = 1;
        double random = Math.random();
        if (random < 0.1) {
            random = random + 0.1;
        }
        for (int i = 0; i < length; i++) {
            num = num * 10;
        }
        return (int) ((random * num));
    }


    private String getTokenIndex(SystemOriginEnum originEnum) {
        String name = originEnum.getName();
        if (name.contains("_")) {
            name = name.replaceAll("_", "-");
        }
        return name.toLowerCase() + "__";
    }

    private void handleAfterLogin(AuthLoginVO authLoginVO, LoginTypeEnum loginType, SystemOriginEnum originEnum) {
        if (LoginTypeEnum.NAME_PWD.equals(loginType)) {
            deleteBindRedis(originEnum.type, authLoginVO.getUsername());
        } else {
            deleteBindRedis(originEnum.type, authLoginVO.getPhone());
        }
    }

}
