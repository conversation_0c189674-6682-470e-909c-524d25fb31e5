package net.xianmu.authentication.service.impl;

import cn.hutool.core.util.StrUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.WeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.UserPropertiesExtInput;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.mapper.auth.*;
import net.xianmu.authentication.model.BO.AuthUserAuthBO;
import net.xianmu.authentication.model.VO.QueryCareWxVO;
import net.xianmu.authentication.model.entity.AuthWechatRelation;
import net.xianmu.authentication.service.WechatService;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.service.wechat.impl.WxServiceImpl;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.TENANT_ID;

/**
 * Description:微信相关实现层
 * date: 2022/7/22 16:31
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class WechatServiceImpl implements WechatService {

    @Resource
    private AuthUserBaseDao authUserBaseDao;
    @Resource
    private AuthUserDao authUserDao;
    @Resource
    private AuthUserPropertiesExtDao authUserPropertiesExtDao;
    @Resource
    private AuthUserAuthDao authUserAuthDao;
    @Resource
    private WxServiceImpl wxService;
    @Resource
    private AuthWechatRelationMapper authWechatRelationMapper;
    @Override
    public boolean toPayAttention(String unionId) {
        if (StrUtil.isBlank(unionId)){
            throw new DefaultServiceException("unionId不能为空");
        }
        return true;
    }

    @Override
    public DubboResponse<Boolean> createExtends(SystemOriginEnum systemOriginEnum, List<UserPropertiesExtInput> userPropertiesExtInputs) {
        //根据手机号来源找到auth_user_id
        if (CollectionUtils.isEmpty(userPropertiesExtInputs)){
            return DubboResponse.getOK(true);
        }
        for (UserPropertiesExtInput userPropertiesExtInput : userPropertiesExtInputs) {
            String phone = userPropertiesExtInput.getPhone();
            if (StringUtil.isEmpty(phone)){
                log.warn("追binlog createExtends 手机号为空 {}", phone);
                continue;
            }
            //根据手机号找到auth_user_id
            AuthUserBase queryUserBase = new AuthUserBase();
            queryUserBase.setPhone(phone);
            AuthUserBase authUserBase = authUserBaseDao.selectByUserBase(queryUserBase);
            if (authUserBase == null){
                log.warn("追binlog createExtends 手机号找不到 userbase  {}", phone);
                continue;
            }
            List<AuthUser> authUsers = authUserDao.selectByUserBaseId(authUserBase.getId()).stream().filter(
                    it->it.getSystemOrigin().equals(systemOriginEnum.getType())
            ).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(authUsers)){
                log.warn("追binlog createExtends 手机号找不到 userauth  phone {} source{}", phone, systemOriginEnum.getType());
                continue;
            }
            Long authUserId = authUsers.get(0).getId();
            AuthUserPropertiesExt ext = new AuthUserPropertiesExt();
            ext.setPropKey(userPropertiesExtInput.getPropKey());
            ext.setPropValue(userPropertiesExtInput.getPropValue());
            ext.setUserId(authUserId);
            authUserPropertiesExtDao.insertSelective(ext);
        }
        return DubboResponse.getOK(true);
    }

    @Override
    public AuthUserAuthResp queryUserAuth(SystemOriginEnum systemOriginEnum, Long userBaseId, Integer type) {
        AuthUser authUser = authUserDao.selectByUserBaseId(userBaseId).stream().filter(it -> it.getSystemOrigin().equals(systemOriginEnum.getType())).findFirst().orElse(null);
        if (authUser == null) {
            return null;
        }
        List<AuthUserAuth> authUserAuths = authUserAuthDao.selectUserAuthRecord(type, authUser.getId());
        if (CollectionUtils.isEmpty(authUserAuths)) {
            return null;
        }
        AuthUserAuthResp convert = convert(authUserAuths.get(0));
        convert.setBaseUserId(userBaseId);
        return convert;
    }

    @Override
    public List<AuthUserAuthResp> queryUsersAuth(SystemOriginEnum systemOriginEnum, List<Long> userBaseIds, Integer type) {
        List<AuthUserAuthBO> authUsers = authUserAuthDao.selectBySystemOriginEnumUserBaseIds(systemOriginEnum.getType(), userBaseIds, type);
        return authUsers.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public AuthUserAuthResp queryUserAuthByUserId(SystemOriginEnum systemOriginEnum, String thirdId, Integer type) {
        List<AuthUserAuth> authUserAuths = authUserAuthDao.selectByThirdIdType(type, thirdId);
        if (CollectionUtils.isEmpty(authUserAuths)) {
            return null;
        }
        List<Long> authUserIds = authUserAuths.stream().map(AuthUserAuth::getUserId).collect(Collectors.toList());
        Optional<AuthUser> first = authUserDao.selectByUserIds(authUserIds).stream().filter(
                it -> it.getSystemOrigin().equals(systemOriginEnum.getType())
        ).findFirst();
        if (!first.isPresent()) {
            return null;
        }
        AuthUser authUser = first.get();
        Long userBaseId = authUser.getUserBaseId();
        List<AuthUserAuth> collect = authUserAuths.stream().filter(it -> it.getUserId().equals(authUser.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        AuthUserAuthResp convert = convert(collect.get(0));
        convert.setBaseUserId(userBaseId);
        return convert;
    }

    @Override
    public String queryCare(QueryCareWxVO queryCareWxVO) {
        //check
        SystemOriginEnum systemOriginEnum = SystemOriginEnum.getSystemOriginByType(queryCareWxVO.getSystemOriginEnum());
        if (systemOriginEnum == null){
            throw new BizException("参数错误");
        }
        String unionId = queryCareWxVO.getUnionId();

        return authWechatRelationMapper.selectBySourceTenantIdAuthTypeUnionid(systemOriginEnum.getType(), TENANT_ID, AuthTypeEnum.OFFICIAL_WE_CHAT.getType(), unionId);
    }

    private AuthUserAuthResp convert(AuthUserAuth authUserAuth) {
        AuthUserAuthResp authUserAuthResp = new AuthUserAuthResp();
        authUserAuthResp.setAuthId(authUserAuth.getAuthId());
        authUserAuthResp.setThirdPartyId(authUserAuth.getThirdPartyId());
        authUserAuthResp.setUserId(authUserAuth.getUserId());
        authUserAuthResp.setAuthType(authUserAuth.getAuthType().intValue());
        authUserAuthResp.setId(authUserAuth.getId());
        authUserAuthResp.setCreateTime(authUserAuth.getCreateTime());
        return authUserAuthResp;
    }
    private AuthUserAuthResp convert(AuthUserAuthBO authUserAuth) {
        AuthUserAuthResp authUserAuthResp = new AuthUserAuthResp();
        authUserAuthResp.setAuthId(authUserAuth.getAuthId());
        authUserAuthResp.setThirdPartyId(authUserAuth.getThirdPartyId());
        authUserAuthResp.setUserId(authUserAuth.getUserId());
        authUserAuthResp.setBaseUserId(authUserAuth.getBaseUserId());
        authUserAuthResp.setAuthType(authUserAuth.getAuthType().intValue());
        authUserAuthResp.setId(authUserAuth.getId());
        authUserAuthResp.setCreateTime(authUserAuth.getCreateTime());
        return authUserAuthResp;
    }
}
