package net.xianmu.authentication.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.MD5Util;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.common.contexts.Global;
import net.xianmu.authentication.common.enmu.LoginTypeEnum;
import net.xianmu.authentication.common.enmu.SystemLoginTypeEnum;
import net.xianmu.authentication.enums.AuthLoginTypeEnum;
import net.xianmu.authentication.enums.UserStatusEnum;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.xianmu.authentication.model.BO.AuthLoginBO;
import net.xianmu.authentication.model.BO.LoginBO;
import net.xianmu.authentication.model.BO.LogoutBO;
import net.xianmu.authentication.model.DTO.AuthLoginRespDTO;
import net.xianmu.authentication.service.UnifiedLoginService;
import net.xianmu.authentication.tripartite.AuthInfo;
import net.xianmu.authentication.tripartite.AuthSubjectFactory;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.ProviderException;
import net.xianmu.i18n.exception.I18nBizException;
import net.xianmu.i18n.util.XianmuI18nUtil;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UnifiedLoginServiceImpl implements UnifiedLoginService {
    @Resource
    AuthSubjectFactory authSubjectFactory;
    @Resource
    AuthLoginServiceImpl authLoginService;
    @Resource
    AuthUserAuthDao authUserAuthDa;
    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthUserBaseDao authUserBaseDao;
    @Resource
    AuthUserPropertiesExtDao authUserPropertiesExtDao;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AuthLoginDto login(LoginBO loginBO) {
        long startTime = System.currentTimeMillis();
        SystemLoginTypeEnum systemLoginTypeEnum = loginBO.getSystemLoginType();
        SystemOriginEnum systemOriginEnum = systemLoginTypeEnum.getSystemOriginEnum();
        AuthLoginTypeEnum authLoginTypeEnum = systemLoginTypeEnum.getAuthLoginTypeEnum();

        AuthUserBase authUserBase = authLoginService.findByPhoneOrUserName(loginBO.getUsername());
        //根据用户名查询用户信息
        if (authUserBase == null) {
            throw new DefaultServiceException(ResultConstant.USER_NOT_EXIST, "用户名或密码错误");
        }

        List<AuthUser> authUserList = authLoginService.getAllAuthUser(authUserBase.getId());
        if (CollectionUtils.isEmpty(authUserList)) {
            throw new DefaultServiceException(ResultConstant.USER_OR_PASSWORD_WRONG, XianmuI18nUtil.getI18nValue("该账号还未激活{0}", systemOriginEnum.getName()));
        }
        AuthUser authUser = authUserList.stream().filter(it -> it.getSystemOrigin().equals(systemOriginEnum.getType())).findFirst().orElse(null);
        if (authUser == null) {
            throw new DefaultServiceException(ResultConstant.USER_OR_PASSWORD_WRONG, XianmuI18nUtil.getI18nValue("该账号还未激活{0}", systemOriginEnum.getName()));
        }
        if (UserStatusEnum.IN_VALID.getStatus().equals(authUser.getStatus().intValue())) {
            throw new DefaultServiceException(ResultConstant.USER_BANNED, "账号被锁定，请联系管理员解锁");
        }
        if (!Objects.equals(authUser.getPassword(), MD5Util.string2MD5(loginBO.getPassword()))) {
            throw new DefaultServiceException(ResultConstant.USER_OR_PASSWORD_WRONG, "用户名或密码错误");
        }


        //校验是否进行授权绑定
        List<AuthUserAuth> authUserAuths = authUserAuthDa.selectUserAuthRecord(authLoginTypeEnum.getAuthType(), authUser.getId());
        if (CollectionUtils.isEmpty(authUserAuths)) {
            //进行授权绑定
            AuthUserAuth userAuthBind = new AuthUserAuth();
            userAuthBind.setUserId(authUser.getId());
            userAuthBind.setAuthId(loginBO.getAuthId());
            userAuthBind.setThirdPartyId(loginBO.getUnionId());
            userAuthBind.setAuthType(authLoginTypeEnum.getAuthType().byteValue());
            authUserAuthDa.insertSelective(userAuthBind);
        }
        //维护三方系统用户ID与Auth服务账号的绑定关系
        authSubjectFactory.getAuthSubject(authLoginTypeEnum).handleUserBind(loginBO.getUnionId(), authUser.getId());

        //进行shiro登录
        try {
            //shiro登录 shirouser仅仅放最基础的信息
            ShiroUser shiroUser = authLoginService.buildShiroUser(authUser, authUserBase, authUserList, systemOriginEnum, LoginTypeEnum.SECOND_TOKEN);
            //登陆
            AuthLoginDto authLoginRespDTO = authLoginService.shiroLogin(shiroUser, LoginTypeEnum.SECOND_TOKEN, systemOriginEnum);
            authLoginRespDTO.setUnionId(loginBO.getUnionId());
            authLoginRespDTO.setOpenId(loginBO.getAuthId());
            log.info("账号密码登录成功,origin:【{}】,authType:【{}】,username:【{}】,time:【{}】", systemOriginEnum.getName(), authLoginTypeEnum.getName(), authUserBase.getUsername(), System.currentTimeMillis() - startTime);
            return authLoginRespDTO;
        } catch (Exception e) {
            log.error("账号密码登录失败,origin:【{}】,authType:【{}】,username:【{}】,time:【{}】", systemOriginEnum.getName(), authLoginTypeEnum.getName(), authUserBase.getUsername(), System.currentTimeMillis() - startTime, e);
            throw new ProviderException("用户名或密码错误");
        }
    }

    @Override
    public AuthLoginDto authLogin(AuthLoginBO authLoginBO) {
        long startTime = System.currentTimeMillis();
        //根据授权码获取对应登录类型的用户信息
        SystemLoginTypeEnum systemLoginTypeEnum = authLoginBO.getSystemLoginType();
        SystemOriginEnum systemOriginEnum = systemLoginTypeEnum.getSystemOriginEnum();
        AuthLoginTypeEnum authLoginTypeEnum = systemLoginTypeEnum.getAuthLoginTypeEnum();
        //获取授权信息
        AuthInfo authInfo = authSubjectFactory.getAuthSubject(authLoginTypeEnum).getAuthInfo(systemLoginTypeEnum, authLoginBO.getCode());
        //根据openId查询授权记录
        List<AuthUserAuth> userAuthRecords = authUserAuthDa.selectByOpenIdType(authLoginTypeEnum.getAuthType(), authInfo.getOpenId());

        if (CollectionUtils.isEmpty(userAuthRecords)) {
            //未授权 将用户授权ID返回进行授权绑定
            AuthLoginRespDTO authLoginRespDTO = new AuthLoginRespDTO();
            authLoginRespDTO.setUsername(authInfo.getAuthId());
            return authLoginRespDTO;
        }
        AuthUser authUser = authLoginService.loginGetAuthUser(userAuthRecords, systemOriginEnum);
        List<AuthUserAuth> bindAuth = userAuthRecords.stream().filter(it -> it.getUserId().equals(authUser.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(bindAuth)){
            AuthUserAuth authUserAuth = bindAuth.get(0);
            if (Objects.equals(authUserAuth.getAuthId(),authUserAuth.getThirdPartyId())){
                log.info("修复账号绑定问题  userid {}",authUser.getId());
                authUserAuth.setThirdPartyId(authInfo.getSubjectId());
                authUserAuth.setUpdateTime(new Date());
                authUserAuthDa.updateByPrimaryKeySelective(authUserAuth);
            }
        }
        if (authUser == null) {
            throw new ProviderException("当前账号信息异常");
        }
        if (UserStatusEnum.IN_VALID.getStatus().equals(authUser.getStatus().intValue())) {
            throw new DefaultServiceException(ResultConstant.USER_BANNED, "账号被锁定，请联系管理员解锁");
        }
        AuthUserBase authUserBase = authUserBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (authUserBase == null) {
            throw new ProviderException("当前账号信息异常");
        }
        List<AuthUser> authUserList = authLoginService.getAllAuthUser(authUserBase.getId());
        if (CollectionUtils.isEmpty(authUserList)) {
            throw new ProviderException("当前账号信息异常");
        }
        //进行shiro登录
        try {
            ShiroUser shiroUser = authLoginService.buildShiroUser(authUser, authUserBase, authUserList, systemOriginEnum, LoginTypeEnum.SECOND_TOKEN);
            //登陆
            AuthLoginDto authLoginRespDTO = authLoginService.shiroLogin(shiroUser, LoginTypeEnum.SECOND_TOKEN, systemOriginEnum);
            authLoginRespDTO.setUnionId(authInfo.getSubjectId());
            updateTime(shiroUser.getId(), authLoginTypeEnum.getAuthType(), authInfo);
            log.info("自动授权登录成功,origin:【{}】,authType:【{}】,username:【{}】,time:【{}】", systemOriginEnum.getName(), authLoginTypeEnum.getName(), authUserBase.getUsername(), System.currentTimeMillis() - startTime);
            authLoginRespDTO.setOpenId(authInfo.getOpenId());
            return authLoginRespDTO;
        } catch (Exception e) {
            log.error("自动授权登录失败,origin:【{}】,authType:【{}】,username:【{}】,time:【{}】", systemOriginEnum.getName(), authLoginTypeEnum.getName(), authUserBase.getUsername(), System.currentTimeMillis() - startTime, e);
            throw new ProviderException("用户名或密码错误");
        }
    }

    @Transactional
    public void updateTime(Long userId,Integer type,AuthInfo authInfo){
        authUserAuthDa.updateTime(userId, type, authInfo.getOpenId());
        authUserPropertiesExtDao.updateValue(userId, Global.WECHAT_OFFICIAL_PUSH_INFO,authInfo.getSubjectId());
    }

    @Override
    public void logout(LogoutBO logoutBO) {
        SystemLoginTypeEnum systemLoginTypeEnum = logoutBO.getSystemLoginType();
        SystemOriginEnum systemOriginEnum = systemLoginTypeEnum.getSystemOriginEnum();
        AuthLoginTypeEnum authLoginTypeEnum = systemLoginTypeEnum.getAuthLoginTypeEnum();
        Subject subject = SecurityUtils.getSubject();
        if (!subject.isAuthenticated()) {
            return;
        }
        ShiroUser shiroUser = getShiroUser();
        authUserAuthDa.deleteByAuthIdType(shiroUser.getId(), authLoginTypeEnum.getAuthType());
        //退出登录
        subject.logout();
        log.info("退出登录成功,origin:【{}】,authType:【{}】,username:【{}】】", systemOriginEnum.getName(), authLoginTypeEnum.getName(), shiroUser.getUsername());
    }


    public ShiroUser getShiroUser() {
        ShiroUser user = (ShiroUser) SecurityUtils.getSubject().getPrincipal();
        return user;
    }

}