package net.xianmu.authentication.service.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.BaseDateUtils;
import net.summerfarm.common.util.MD5Util;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import net.xianmu.authentication.client.dto.user.AuthUserLastUpdatePwdTimeResp;
import net.xianmu.authentication.client.input.user.AuthUserBaseQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.input.user.BaseUserUpdateInput;
import net.xianmu.authentication.client.resp.AuthUserBaseResp;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.xianmu.authentication.model.BO.AuthUserBO;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.authentication.service.AuthUserBaseService;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.TENANT_ID;

@Slf4j
@Service
public class AuthUserBaseServiceImpl implements AuthUserBaseService {
    @Resource
    AuthUserBaseDao authUserBaseDao;
    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthUserPropertiesExtDao authUserPropertiesExtDao;

    /**
     * 后续下个版本下掉
     * @param systemOriginEnum
     * @param baseUserUpdateInput
     * @return
     */
    @Override
    @Transactional
    public Boolean updateUserBase(SystemOriginEnum systemOriginEnum, BaseUserUpdateInput baseUserUpdateInput) {
        if (baseUserUpdateInput.getBizUserId() != null){
            return updateUserBaseNew(systemOriginEnum, baseUserUpdateInput);
        }
        if (systemOriginEnum.type>SystemOriginEnum.CRM.type){
            throw new BizException("参数错误");
        }
        AuthUserBase authUserBase = authUserBaseDao.selectByPrimaryKey(baseUserUpdateInput.getBaseUserId());
        if (authUserBase == null) {
            throw new BizException("该用户不存在");
        }
        if (!StringUtils.isEmpty(baseUserUpdateInput.getNikeName())) {
            authUserBase.setNickname(baseUserUpdateInput.getNikeName());
        }
        List<AuthUser> collect = authUserDao.selectByUserBaseId(authUserBase.getId()).stream().filter(it -> it.getSystemOrigin().equals(systemOriginEnum.getType())).collect(Collectors.toList());;
        List<AuthUser> updateList = collect.stream().filter(it->it.getTenantId().equals(TENANT_ID)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(updateList)){
            throw new BizException("用户数据异常");
        }
        if (!StringUtils.isEmpty(baseUserUpdateInput.getPassword())) {

            AuthUser authUser = updateList.get(0);
            AuthUser updateUser = new AuthUser();
            updateUser.setPassword(MD5Util.string2MD5(baseUserUpdateInput.getPassword()));
            updateUser.setId(authUser.getId());
            authUserDao.updateByPrimaryKeySelective(updateUser);
            if (systemOriginEnum.type.equals(SystemOriginEnum.SRM.type) || systemOriginEnum.type.equals(SystemOriginEnum.TMS.type)){
                updateAllPwd(authUserBase.getId(), baseUserUpdateInput.getPassword());
            }
        }
        if (baseUserUpdateInput.getStatus() != null) {
            collect.forEach(
                    it -> {
                        AuthUser updateUser = new AuthUser();
                        updateUser.setId(it.getId());
                        updateUser.setStatus(baseUserUpdateInput.getStatus().byteValue());
                        authUserDao.updateByPrimaryKeySelective(updateUser);
                    }
            );
        }
        if (!StringUtils.isEmpty(baseUserUpdateInput.getPhone())) {
            updatePhone(authUserBase, baseUserUpdateInput.getPhone());
        }
        authUserBase.setUpdateTime(new Date());
        authUserBaseDao.updateByPrimaryKeySelective(authUserBase);
        return true;
    }

    public Boolean updateUserBaseNew(SystemOriginEnum systemOriginEnum, BaseUserUpdateInput baseUserUpdateInput) {
        if (systemOriginEnum.type>SystemOriginEnum.CRM.type){
            throw new BizException("参数错误");
        }
        AuthUser authUser = authUserDao.selectByBizUserIdTenantId(systemOriginEnum.type, baseUserUpdateInput.getBizUserId(), TENANT_ID);
        if (authUser == null){
            throw new BizException("该用户不存在");
        }
        AuthUserBase authUserBase = authUserBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (authUserBase == null) {
            throw new BizException("该用户不存在");
        }
        if (!StringUtils.isEmpty(baseUserUpdateInput.getNikeName())) {
            authUserBase.setNickname(baseUserUpdateInput.getNikeName());
        }
        if (!StringUtils.isEmpty(baseUserUpdateInput.getPassword())) {
            AuthUser updateUser = new AuthUser();
            updateUser.setPassword(MD5Util.string2MD5(baseUserUpdateInput.getPassword()));
            updateUser.setId(authUser.getId());
            authUserDao.updateByPrimaryKeySelective(updateUser);
            if (systemOriginEnum.type.equals(SystemOriginEnum.SRM.type) || systemOriginEnum.type.equals(SystemOriginEnum.TMS.type)){
                updateAllPwd(authUserBase.getId(), baseUserUpdateInput.getPassword());
            }
        }
        if (baseUserUpdateInput.getStatus() != null) {
            AuthUser updateUser = new AuthUser();
            updateUser.setId(authUser.getId());
            updateUser.setStatus(baseUserUpdateInput.getStatus().byteValue());
            authUserDao.updateByPrimaryKeySelective(updateUser);
        }
        if (!StringUtils.isEmpty(baseUserUpdateInput.getPhone())
                && !Objects.equals(baseUserUpdateInput.getPhone(), authUserBase.getPhone())) {
            updatePhone(authUser, systemOriginEnum ,authUserBase, baseUserUpdateInput.getPhone());
        }
        authUserBase.setUpdateTime(new Date());
        authUserBaseDao.updateByPrimaryKeySelective(authUserBase);
        return true;
    }




    @Override
    public AuthUserBaseResp queryBizUserId(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {
        if (!SystemOriginEnum.ADMIN.type.equals(systemOriginEnum.type)) {
            throw new BizException("暂不支持来源");
        }
        String userName = authUserQueryInput.getUserName();
        AuthUserBase authUserBase = authUserBaseDao.selectByNameOrigin(userName);
        if (authUserBase == null){
            throw new BizException("该用户不存在");
        }
        Long authUserBaseId = authUserBase.getId();
        List<AuthUser> collect = authUserDao.selectByUserBaseId(authUserBaseId).stream().filter(it -> it.getSystemOrigin().equals(systemOriginEnum.type) && it.getTenantId().equals(TENANT_ID)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)){
            throw new BizException("该用户不存在");
        }
        return merge(authUserBase, collect.get(0));
    }

    private AuthUserBaseResp merge(AuthUserBase authUserBase,AuthUser authUser){
        AuthUserBaseResp resp = new AuthUserBaseResp();
        resp.setBaseUserId(authUserBase.getId());
        resp.setName(authUserBase.getNickname());
        resp.setBizUserId(authUser.getBizUserId());
        resp.setBaseUserId(authUserBase.getId());
        return resp;
    }

    @Override
    public AuthUserBaseResp queryRealName(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {
        if (!SystemOriginEnum.ADMIN.type.equals(systemOriginEnum.type)) {
            throw new BizException("暂不支持来源");
        }
        Long bizId = authUserQueryInput.getBizId();
        AuthUser authUser = authUserDao.selectByBizUserId(systemOriginEnum.type, bizId);
        if (authUser == null){
            throw new BizException("该用户不存在");
        }
        AuthUserBase authUserBase = authUserBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (authUserBase == null){
            throw new BizException("该用户不存在");
        }
        return merge(authUserBase, authUser);
    }

    @Override
    public AuthUserBaseResp queryPassword(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {
        Long bizId = authUserQueryInput.getBizId();
        AuthUser authUser = authUserDao.selectByBizUserId(systemOriginEnum.type, bizId);
        if (authUser == null){
            throw new BizException("该用户不存在");
        }
        List<AuthUserPropertiesExt> authUserPropertiesExts = authUserPropertiesExtDao.selectValue(authUser.getId(), AuthGlobal.PLAINTEXT_PASSWORD);
        if (CollectionUtils.isEmpty(authUserPropertiesExts)){
            throw new BizException("数据同步错误,没有获取到明文密码");
        }
        AuthUserBaseResp authUserBaseResp = new AuthUserBaseResp();
        authUserBaseResp.setBizUserId(bizId);
        authUserBaseResp.setPassword(authUserPropertiesExts.get(0).getPropValue());
        return authUserBaseResp;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateAllPwd(Long userBaseId, String pwd) {
        List<AuthUser> authUserList = authUserDao.selectByUserBaseId(userBaseId);
        if (CollectionUtils.isEmpty(authUserList)){
            return true;
        }
        List<Long> ids = authUserList.stream().map(AuthUser::getId).collect(Collectors.toList());
        authUserPropertiesExtDao.deleteByAllUserIdsKey(ids, AuthGlobal.PLAINTEXT_PASSWORD);
        List<AuthUserPropertiesExt> authUserPropertiesExts = new ArrayList<>();
        ids.forEach(
                it->{
                    AuthUserPropertiesExt authUserPropertiesExt = new AuthUserPropertiesExt();
                    authUserPropertiesExt.setPropKey(AuthGlobal.PLAINTEXT_PASSWORD);
                    authUserPropertiesExt.setPropValue(pwd);
                    authUserPropertiesExt.setUserId(it);
                    authUserPropertiesExt.setCreateTime(new Date());
                    authUserPropertiesExt.setUpdateTime(new Date());
                    authUserPropertiesExts.add(authUserPropertiesExt);
                }
        );
        authUserPropertiesExtDao.batchAdd(authUserPropertiesExts);
        return true;
    }

    @Override
    public List<AuthUserBase> batchQueryUserBaseByPhone(List<String> phoneList) {
        if (CollectionUtils.isEmpty(phoneList)) {
            return null;
        }
        return authUserBaseDao.selectByPhoneList(phoneList);
    }

    @Override
    public List<AuthUserPhoneDTO> selectAuthUserBaseWithOrigin(SystemOriginEnum originEnum, int pageIndex, int pageSize) {
        return authUserBaseDao.selectWithOrigin(originEnum.type,pageIndex * pageSize, pageSize);
    }

    @Override
    public List<AuthUserPhoneDTO> selectWithOriginWithOutBigCustomer(SystemOriginEnum originEnum, int pageIndex, int pageSize) {
        return authUserBaseDao.selectWithOriginWithOutBigCustomer(originEnum.type,pageIndex * pageSize, pageSize);
    }

    @Override
    public AuthUserLastUpdatePwdTimeResp queryLastUpdatePwdTime(SystemOriginEnum systemOriginEnum, String phone) {
        List<AuthUserBO> authUserBOS = authUserDao.selectUserIdPhoneBySourcePhone(systemOriginEnum.type, Collections.singletonList(phone));
        if(CollectionUtils.isEmpty(authUserBOS)){
            throw new BizException("该用户不存在");
        }
        List<Long> ids = authUserBOS.stream().map(AuthUserBO::getId).collect(Collectors.toList());

        List<AuthUserPropertiesExt> authUserPropertiesExts = authUserPropertiesExtDao.selectByUserIdsAndKey(ids, AuthGlobal.LAST_UPDATE_PWD_TIME);
        if (CollectionUtils.isEmpty(authUserPropertiesExts)){
            AuthUserLastUpdatePwdTimeResp resp = new AuthUserLastUpdatePwdTimeResp();
            AuthUserBO authUserBO = authUserBOS.stream().sorted(Comparator.comparing(AuthUserBO::getCreateTime).reversed()).findFirst().orElse(null);
            if (authUserBO != null && authUserBO.getCreateTime() != null){
                resp.setLastUpdatePwdTime(BaseDateUtils.date2LocalDateTime(authUserBO.getCreateTime()));
            }
            resp.setPhone(phone);
            return resp;
        }

        AuthUserLastUpdatePwdTimeResp resp = new AuthUserLastUpdatePwdTimeResp();
        String propValue = authUserPropertiesExts.get(0).getPropValue();
        LocalDateTime localDateTime = BaseDateUtils.stringToLocalDateTime(propValue);
        resp.setLastUpdatePwdTime(localDateTime);
        resp.setPhone(phone);
        return resp;
    }

    @Override
    public List<AuthUserBase> queryAuthUserBase(AuthUserBaseQueryInput authUserBaseQueryInput) {
        if (CollectionUtils.isEmpty(authUserBaseQueryInput.getPhoneList()) && org.apache.commons.lang3.StringUtils.isBlank(authUserBaseQueryInput.getPhone()) && org.apache.commons.lang3.StringUtils.isBlank(authUserBaseQueryInput.getUsername())) {
            log.warn("请求参数为空!");
            return Collections.emptyList();
        }
        return authUserBaseDao.queryAuthUserBase(authUserBaseQueryInput);
    }

    public void updatePhone(AuthUser authUser, SystemOriginEnum systemOriginEnum,
                             AuthUserBase oldAuthUserBase, String phone) {
        AuthUserBase queryAuthUserBase = new AuthUserBase();
        queryAuthUserBase.setPhone(phone);
        AuthUserBase authUserBase = authUserBaseDao.selectByUserBase(queryAuthUserBase);
        //手机号没被注册
        if (authUserBase == null) {
            oldAuthUserBase.setPhone(phone);
            authUserBaseDao.updateByPrimaryKeySelective(oldAuthUserBase);
            return;
        }
        //手机号已经被本来源注册
        List<AuthUser> authUsers = authUserDao.selectBySourceTenantIdPhone(systemOriginEnum.type, TENANT_ID, phone);
        if (!CollectionUtils.isEmpty(authUsers)){
            throw new BizException("手机号已被注册");
        }
        AuthUser updateAuthUser = new AuthUser();
        updateAuthUser.setId(authUser.getId());
        updateAuthUser.setUserBaseId(authUserBase.getId());
        authUserDao.updateByPrimaryKeySelective(updateAuthUser);
    }

    //后续删除
    @Deprecated
    private void updatePhone(AuthUserBase oldAuthUserBase, String phone) {
        AuthUserBase queryAuthUserBase = new AuthUserBase();
        queryAuthUserBase.setPhone(phone);
        AuthUserBase authUserBase = authUserBaseDao.selectByUserBase(queryAuthUserBase);
        if (authUserBase == null){
            AuthUserBase usernameQuery = new AuthUserBase();
            usernameQuery.setUsername(phone);
            authUserBase = authUserBaseDao.selectByUserBase(usernameQuery);
        }
        //手机号没被注册
        if (authUserBase == null) {
            oldAuthUserBase.setPhone(phone);
            authUserBaseDao.updateByPrimaryKeySelective(oldAuthUserBase);
        } else {

            throw new BizException("该手机号已存在不能被修改");
        }
    }
}
