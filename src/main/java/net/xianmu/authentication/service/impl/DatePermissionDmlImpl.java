package net.xianmu.authentication.service.impl;

import cn.hutool.core.lang.Pair;
import net.xianmu.authentication.common.DbTableDml;
import net.xianmu.authentication.common.dto.DtsModel;
import net.xianmu.authentication.common.enmu.BinlogEventEnum;
import net.xianmu.authentication.common.enmu.PermissionTypeEnum;
import net.xianmu.authentication.model.entity.BusinessDataPermission;
import net.xianmu.authentication.service.permission.PermissionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
public class DatePermissionDmlImpl implements DbTableDml {
    @Resource
    PermissionService permissionService;

    @Override
    public void handel(DtsModel dtsModel) {
        //新增操作
        if (dtsModel.getType().equals(BinlogEventEnum.INSERT.getEvent())) {
            dtsModel.getData().forEach(
                    it -> {
                        String name = it.get("store_name");
                        String value = it.get("store_no");
                        BusinessDataPermission businessDataPermission = new BusinessDataPermission();
                        businessDataPermission.setPermissionType(PermissionTypeEnum.CITY_WAREHOUSE.code.byteValue());
                        businessDataPermission.setPermissionValue(value);
                        businessDataPermission.setPermissionName(name);
                        businessDataPermission.setCreateTime(new Date());
                        businessDataPermission.setUpdateTime(new Date());
                        permissionService.addPermission(businessDataPermission);
                    }
            );

        }
        //修改操作
        if (dtsModel.getType().equals(BinlogEventEnum.UPDATE.getEvent())) {
            List<Pair<Map<String, String>, Map<String, String>>> alignedData = getAlignedData(dtsModel);
            for (Pair<Map<String, String>, Map<String, String>> alignedDatum : alignedData) {
                Map<String, String> updateDate = alignedDatum.getValue();
                if (!updateDate.containsKey("store_name")) {
                    continue;
                }
                Map<String, String> newDate = alignedDatum.getKey();
                String value = newDate.get("store_no");
                //修改操作
                permissionService.updatePermission(value, PermissionTypeEnum.CITY_WAREHOUSE.code, newDate.get("store_name"));
            }
        }


    }

    /**
     * 获取DtsModel对齐的数据
     *
     * @return pair集合（pair左边是data，右边是old）
     */
    public static List<Pair<Map<String, String>, Map<String, String>>> getAlignedData(DtsModel dtsModelEvent) {
        List<Pair<Map<String, String>, Map<String, String>>> pairList = new ArrayList<>();
        int size = dtsModelEvent.getData() == null ? 0 : dtsModelEvent.getData().size();
        int oldSize = dtsModelEvent.getOld() == null ? 0 : dtsModelEvent.getOld().size();
        if (size != oldSize) {
            return pairList;
        }
        for (int i = 0; i < size; i++) {
            pairList.add(new Pair<>(dtsModelEvent.getData().get(i), dtsModelEvent.getOld().get(i)));
        }
        return pairList;
    }
}
