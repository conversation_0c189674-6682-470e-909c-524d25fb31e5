package net.xianmu.authentication.service.impl;

import com.alibaba.fastjson.JSON;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.input.wechat.WechatCareQrInput;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.xianmu.authentication.model.BO.AuthUserBO;
import net.xianmu.authentication.model.DTO.AuthUserAuthThirdPartyDTO;
import net.xianmu.authentication.service.AuthUserAuthService;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.exception.BizException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/7/17  15:25
 */
@Service
@Slf4j
public class AuthUserAuthServiceImpl implements AuthUserAuthService {

    @Resource
    private AuthUserAuthDao authUserAuthDao;
    @Resource
    private AuthUserDao authUserDao;
    @Resource
    private AuthUserPropertiesExtDao authUserPropertiesExtDao;
    @Resource
    private TransactionTemplate transactionTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchUpsertUserAuth(List<AuthUserAuthThirdPartyDTO> userAuthList) {
        if (CollectionUtils.isEmpty(userAuthList)) {
            return;
        }

        for (AuthUserAuthThirdPartyDTO authUserAuth : userAuthList) {
            List<AuthUserAuth> authUserAuths = authUserAuthDao.selectUserAuthRecord(Integer.valueOf(authUserAuth.getAuthType()), authUserAuth.getUserId());
            //新增
            if (CollectionUtils.isEmpty(authUserAuths)) {
                authUserAuthDao.insertSelective(authUserAuth);
            } //修改
            else {
                Long id = authUserAuths.get(0).getId();
                authUserAuth.setId(id);
                authUserAuthDao.updateByPrimaryKeySelective(authUserAuth);
            }
        }
    }

    @Override
    public List<AuthUserAuthResp> queryUserRespByPhones(AuthUserAuthQueryInput authUserAuthQueryInput) {
        List<AuthUserAuthResp> authUserAuthResps = new ArrayList<>();
        if (CollectionUtils.isEmpty(authUserAuthQueryInput.getPhones())) {
            throw new BizException("手机号不能为空");
        }

        if (StringUtils.isEmpty(authUserAuthQueryInput.getChannelCode())
            || WxOfficialAccountsChannelEnum.getByChannelCode(authUserAuthQueryInput.getChannelCode()) == null) {
            throw new BizException("错误的渠道来源");
        }
        if (authUserAuthQueryInput.getSystemOriginEnum() == null) {
            throw new BizException("系统参数或者类型不能为空");
        }
        //根据手机来源查询id后去查询扩展表
        List<AuthUserBO> authUserBOS = authUserDao.selectUserIdPhoneBySourcePhone(authUserAuthQueryInput.getSystemOriginEnum().getType(), authUserAuthQueryInput.getPhones());
        if (CollectionUtils.isEmpty(authUserBOS)) {
            return new ArrayList<>();
        }
        List<Long> userIds = authUserBOS.stream().map(AuthUserBO::getId).collect(Collectors.toList());
        //查询扩展的authId
        List<AuthUserPropertiesExt> authUserPropertiesExts = authUserPropertiesExtDao.selectByUserIdsAndKey(userIds, WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        Map<String, List<AuthUserBO>> map = authUserBOS.stream().collect(Collectors.groupingBy(AuthUserBO::getPhone));
        for (String phone : map.keySet()) {
            //手机号
            List<AuthUserBO> phoneAuthUser = map.get(phone);
            List<Long> phoneUserIds = phoneAuthUser.stream().map(AuthUserBO::getId).collect(Collectors.toList());
            AuthUserPropertiesExt authUserPropertiesExt = authUserPropertiesExts.stream().filter(u -> phoneUserIds.contains(u.getUserId())).findFirst().orElse(null);
            if (authUserPropertiesExt != null) {
                AuthUserAuthResp resp = new AuthUserAuthResp();
                resp.setAuthId(authUserPropertiesExt.getPropValue());
                resp.setPhone(phone);
                authUserAuthResps.add(resp);
            }
        }
        return authUserAuthResps;
    }

    @Override
    public Boolean bindWechatCare(WechatCareQrInput wechatCareQrInput) {
        if (StringUtils.isEmpty(wechatCareQrInput.getPhone())) {
            throw new BizException("手机号不能为空");
        }
        if (StringUtils.isEmpty(wechatCareQrInput.getChannelCode())
            || WxOfficialAccountsChannelEnum.getByChannelCode(wechatCareQrInput.getChannelCode()) == null) {
            throw new BizException("错误的渠道来源");
        }
        if (wechatCareQrInput.getSystemOriginEnum() == null) {
            throw new BizException("系统参数或者类型不能为空");
        }
        if (StringUtils.isEmpty(wechatCareQrInput.getOpenId())) {
            throw new BizException("openId不能为空");
        }
        //手机号+来源 查询saas的所有的账号
        List<AuthUserBO> authUserBOS = authUserDao.
            selectUserIdPhoneBySourcePhone(wechatCareQrInput.getSystemOriginEnum().getType(), Collections.singletonList(wechatCareQrInput.getPhone()));
        if (CollectionUtils.isEmpty(authUserBOS)) {
            throw new BizException("手机号未注册");
        }
        List<Long> userIds = authUserBOS.stream().map(AuthUserBO::getId).collect(Collectors.toList());
        List<AuthUserPropertiesExt> authUserPropertiesExts = authUserPropertiesExtDao.selectByUserIdsAndKey(userIds, WxOfficialAccountsChannelEnum.FTGYL.channelCode);
        if (!CollectionUtils.isEmpty(authUserPropertiesExts) && userIds.size() == authUserPropertiesExts.size()) {
            return true;
        }
        transactionTemplate.execute((action) -> {
            careDbOperation(userIds, wechatCareQrInput.getChannelCode(), wechatCareQrInput.getOpenId());
            return null;
        });
        return true;
    }

    private void careDbOperation(List<Long> userIds, String channelCode, String openId) {
        if (CollectionUtils.isEmpty(userIds)) {
            return;
        }
        authUserPropertiesExtDao.deleteByAllUserIdsKey(userIds, channelCode);
        List<AuthUserPropertiesExt> collect = userIds.stream().map(
            it -> {
                AuthUserPropertiesExt ext = new AuthUserPropertiesExt();
                ext.setUserId(it);
                ext.setPropKey(channelCode);
                ext.setPropValue(openId);
                ext.setUpdateTime(new Date());
                ext.setCreateTime(new Date());
                return ext;
            }
        ).collect(Collectors.toList());
        authUserPropertiesExtDao.batchAdd(collect);
    }

    @Override
    public Boolean closeWechatCare(WechatCareQrInput wechatCareQrInput) {
        if (StringUtils.isEmpty(wechatCareQrInput.getChannelCode())
            || WxOfficialAccountsChannelEnum.getByChannelCode(wechatCareQrInput.getChannelCode()) == null) {
            throw new BizException("错误的渠道来源");
        }
        //openid 回调用
        String openId = wechatCareQrInput.getOpenId();
        if (!StringUtils.isEmpty(openId)) {
            //根据openid
            closeByOpenId(wechatCareQrInput);
            return true;
        }
        //手机调用
        String phone = wechatCareQrInput.getPhone();
        if (!StringUtils.isEmpty(phone)) {
            closeByPhone(wechatCareQrInput);
            return true;
        }
        return false;
    }

    private void closeByPhone(WechatCareQrInput wechatCareQrInput) {
        List<Long> userIds = authUserDao.
            selectUserIdPhoneBySourcePhone(wechatCareQrInput.getSystemOriginEnum().getType(), Collections.singletonList(wechatCareQrInput.getPhone())).stream()
            .map(AuthUserBO::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(userIds)) {
            log.warn("closeWechatCare 查询不到userid {}", JSON.toJSONString(wechatCareQrInput));
            return;
        }
        List<AuthUserPropertiesExt> authUserPropertiesExts = authUserPropertiesExtDao.selectByUserIdsAndKey(userIds, wechatCareQrInput.getChannelCode());
        if (!CollectionUtils.isEmpty(authUserPropertiesExts)) {
            authUserPropertiesExtDao.deleteByAllUserIdsKey(userIds, wechatCareQrInput.getChannelCode());
        }
    }

    private void closeByOpenId(WechatCareQrInput wechatCareQrInput) {
        List<AuthUserPropertiesExt> authUserPropertiesExts = authUserPropertiesExtDao.selectALLByKeyValue(wechatCareQrInput.getChannelCode(), wechatCareQrInput.getOpenId());
        if (CollectionUtils.isEmpty(authUserPropertiesExts)) {
            return;
        }
        List<Long> userIds = authUserPropertiesExts.stream().map(AuthUserPropertiesExt::getUserId).collect(Collectors.toList());
        authUserPropertiesExtDao.deleteByAllUserIdsKey(userIds, wechatCareQrInput.getChannelCode());
    }
}
