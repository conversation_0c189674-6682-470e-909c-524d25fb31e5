package net.xianmu.authentication.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.client.input.purview.TenantPrivilegesInput;
import net.xianmu.authentication.mapper.auth.AuthMenuPurviewDao;
import net.xianmu.authentication.mapper.auth.AuthTenantPrivilegesDao;
import net.xianmu.authentication.model.BO.AuthTenantPrivilegesBo;
import net.xianmu.authentication.model.entity.AuthTenantPrivileges;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.common.exception.BizException;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.transaction.Transactional;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.TENANT_ID;
import static net.xianmu.authentication.common.AuthGlobal.TRUE;

@Service
public class AuthTenantPrivilegesServiceImpl implements AuthTenantPrivilegesService {
    @Resource
    private AuthTenantPrivilegesDao authTenantPrivilegesDao;
    @Resource(name = "authRedisTemplate")
    RedisTemplate redisTemplate;
    @Resource
    private AuthMenuPurviewDao authMenuPurviewDao;
    @NacosValue(value = "${filterTenantPrivileges:true}",autoRefreshed = true)
    private String filterTenantPrivileges;
    @Override
    @Transactional
    /**
     * 添加租户权限
     *
     * @param authTenantPrivilegesInput 租户权限输入参数
     * @return 返回是否成功添加租户权限的布尔值
     */
    public Boolean addTenantPrivileges(AuthTenantPrivilegesInput authTenantPrivilegesInput) {

        check(authTenantPrivilegesInput);

        List<TenantPrivilegesInput> tenantPrivilegesInputs = authTenantPrivilegesInput.getTenantPrivilegesInputs();
        List<Long> collect = tenantPrivilegesInputs.stream().map(TenantPrivilegesInput::getMenuId).distinct().collect(Collectors.toList());
        Map<Long, AuthMenuPurview> menuPurviewMap = authMenuPurviewDao.selectByIds(collect).stream().collect(Collectors.toMap(AuthMenuPurview::getId, el -> el));


        List<AuthTenantPrivilegesBo> authTenantPrivileges = buildAuthTenantPrivileges(authTenantPrivilegesInput, menuPurviewMap);

        authTenantPrivilegesDao.deleteByTenantId(authTenantPrivilegesInput.getTenantId());
        authTenantPrivilegesDao.insertOrUpdateBatch(authTenantPrivileges);

        sysRedis(authTenantPrivilegesInput.getTenantId(), authTenantPrivileges);

        return true;
    }

    @Override
    /**
     * 根据租户ID查询权限列表
     *
     * @param tenantId 租户ID
     * @return AuthTenantPrivileges对象列表
     */
    public List<AuthTenantPrivileges> selectByTenantId(Long tenantId) {
        return authTenantPrivilegesDao.selectByTenantId(tenantId);
    }

    public List<AuthMenuPurview>  mergeTenantPurviewExpireTime(SystemOriginEnum systemOriginEnum, Long  tenantId, List<AuthMenuPurview> outs) {
        if (!SystemOriginEnum.COSFO_MANAGE.getType().equals(systemOriginEnum.getType())){
            return outs;
        }
        //租户>=1的过虑
        if (tenantId == null || tenantId <= TENANT_ID){
            return outs;
        }
        if (Objects.equals(filterTenantPrivileges, TRUE)){
            List<AuthTenantPrivileges> authTenantPrivileges = authTenantPrivilegesDao.selectByTenantId(tenantId);
            if (CollectionUtil.isEmpty(authTenantPrivileges)){
                return new ArrayList<>();
            }
            Map<Long, AuthTenantPrivileges> collect = authTenantPrivileges.stream().collect(Collectors.toMap(AuthTenantPrivileges::getMenuId, Function.identity()));
            outs = outs.stream().filter(it-> collect.containsKey(it.getId())).collect(Collectors.toList());
            outs.forEach(
                    it->{
                        AuthTenantPrivileges tenantPrivileges = collect.get(it.getId());
                        it.setExpireTime(tenantPrivileges.getExpireTime());
                    }
            );
        }
        return outs;
    }

    /**
     * 检查租户id和来源是否为空
     */
    private void check(AuthTenantPrivilegesInput authTenantPrivilegesInput) {
        // 检查tenantId是否为空
        if (authTenantPrivilegesInput.getTenantId() == null) {
            throw new BizException("租户ID不能为空");
        }

        if (authTenantPrivilegesInput.getSystemOrigin() == null
                || SystemOriginEnum.getSystemOriginByType(authTenantPrivilegesInput.getSystemOrigin()) == null) {
            throw new BizException("错误的系统来源");
        }

        if (CollectionUtil.isEmpty(authTenantPrivilegesInput.getTenantPrivilegesInputs())) {
            throw new BizException("租户权益不能为空");
        }
    }

    private List<AuthTenantPrivilegesBo> buildAuthTenantPrivileges(AuthTenantPrivilegesInput authTenantPrivilegesInput,
                                                                   Map<Long, AuthMenuPurview> map) {

        return authTenantPrivilegesInput.getTenantPrivilegesInputs().stream().map(
                it -> {
                    AuthTenantPrivilegesBo authTenantPrivileges = new AuthTenantPrivilegesBo();
                    authTenantPrivileges.setTenantId(authTenantPrivilegesInput.getTenantId());
                    authTenantPrivileges.setMenuId(it.getMenuId());
                    authTenantPrivileges.setExpireTime(it.getExpireTime());
                    AuthMenuPurview authMenuPurview = map.get(authTenantPrivileges.getMenuId());
                    if (authMenuPurview != null) {
                        authTenantPrivileges.setPermissionCode(authMenuPurview.getUrl());
                    }
                    return authTenantPrivileges;
                }
        ).collect(Collectors.toList());
    }

    /**
     * 同步Redis操作
     *
     * @param tenantId             租户ID
     * @param authTenantPrivileges 权限列表
     */
    private void sysRedis(Long tenantId, List<AuthTenantPrivilegesBo> authTenantPrivileges) {
        String key = getKeys(tenantId);
        redisTemplate.delete(key);
        if (!CollectionUtil.isEmpty(authTenantPrivileges)) {
            Map<String, Long> map = new HashMap<>(authTenantPrivileges.size());
            authTenantPrivileges.forEach(
                    u->{
                        if (!StringUtils.isEmpty(u.getPermissionCode())){
                          map.put(u.getPermissionCode(), u.getExpireTime().getTime());
                        }
                    }
            );
            redisTemplate.opsForHash().putAll(key, map);
        }
    }

    /**
     * 根据给定的租户ID获取对应的键值
     *
     * @param tenantId 租户ID
     * @return 对应的键值
     */
    private String getKeys(Long tenantId) {
        return String.format(TENANT_EQUITY, tenantId);
    }

    public static final String TENANT_EQUITY = "auth:tenant:equity:%s";

}
