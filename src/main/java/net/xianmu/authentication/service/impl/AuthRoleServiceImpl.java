package net.xianmu.authentication.service.impl;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.client.input.AuthRoleQueryVO;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.UserRoleInput;
import net.xianmu.authentication.client.utils.RedisKeyUtils;
import net.xianmu.authentication.common.enmu.RoleTypeEnum;
import net.xianmu.authentication.common.util.MenuTreeUtils;
import net.xianmu.authentication.common.util.SqlUtils;
import net.xianmu.authentication.mapper.auth.*;
import net.xianmu.authentication.model.DTO.AuthUserRoleIdDto;
import net.xianmu.authentication.model.DTO.RoleUserCountDTO;
import net.xianmu.authentication.model.entity.AuthTenantPrivileges;
import net.xianmu.authentication.service.AuthRoleService;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.common.util.StringUtils;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import org.springframework.beans.BeanUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.TRUE;

@Service
@Slf4j
public class AuthRoleServiceImpl extends BaseService implements AuthRoleService {
    @Resource
    private AuthRoleDao roleDao;
    @Resource(name = "authRedisTemplate")
    RedisTemplate redisTemplate;
    @Resource
    AuthUserRoleDao authUserRoleDao;
    @Resource
    AuthMenuPurviewDao authMenuPurviewDao;
    @Resource
    AuthRolePurviewDao authRolePurviewDao;
    @Resource
    AuthUserBaseDao authUserBaseDao;
    @Resource
    AuthUserDao authUserDao;

    @Resource
    private AuthTenantPrivilegesService authTenantPrivilegesService;

    @Override
    public PageInfo<AuthRoleDTO> roleList(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleQueryVO roleQueryVO) {
        Integer type = systemOriginEnum.getType();
        roleQueryVO.setSystemOrigin(type);
        roleQueryVO.setTenantId(tenantId);
        String orderBy = SqlUtils.getOrderBySql(roleQueryVO.getSortList());
        if (StringUtils.isEmpty(orderBy)){
            PageHelper.startPage(roleQueryVO.getPageIndex(), roleQueryVO.getPageSize());
        }else {
            PageHelper.startPage(roleQueryVO.getPageIndex(), roleQueryVO.getPageSize(), orderBy);
        }
        List<AuthRoleDTO> authRoleList = roleDao.selectAuthRoleQueryVO(roleQueryVO);
        if (!CollectionUtils.isEmpty(authRoleList)) {
            List<Long> roleIds = authRoleList.stream().map(AuthRole::getId).collect(Collectors.toList());
            Map<Long, RoleUserCountDTO> roleCountMap = authUserRoleDao.countUserByUserId(roleIds).stream().collect(Collectors.toMap(RoleUserCountDTO::getRoleId, Function.identity()));
            authRoleList.stream().forEach(
                    it -> {
                        RoleUserCountDTO roleUserCountDTO = roleCountMap.get(it.getId());
                        if (roleUserCountDTO != null) {
                            it.setRoleCount(roleUserCountDTO.getUserCount());
                        } else {
                            it.setRoleCount(0);
                        }
                    }
            );
        }
        return PageInfoHelper.createPageInfo(authRoleList);
    }

    @Override
    public AuthRoleDetailsDTO roleDetail(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, Long roleId) {
        AuthRoleDetailsDTO roleDetailsDTO = new AuthRoleDetailsDTO();
        AuthRole authRole = roleDao.selectByPrimaryKey(roleId);
        if (authRole == null) {
            return null;
        }
        BeanUtils.copyProperties(authRole, roleDetailsDTO);
        //查询菜单权限
        List<AuthMenuPurview> menuPreviews = authMenuPurviewDao.selectRoleMenus(systemOriginEnum.getType(), tenantId, roleId);

        menuPreviews = authTenantPrivilegesService.mergeTenantPurviewExpireTime(systemOriginEnum, tenantId, menuPreviews);

        roleDetailsDTO.setList(menuPreviews);
        roleDetailsDTO.setTree(MenuTreeUtils.convertTree(menuPreviews));
        return roleDetailsDTO;
    }

    @Override
    @Transactional
    public int updateRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleUpdateVO updateVO) {
        //跟新角色表的相关信息
        AuthRole authRole = new AuthRole();
        BeanUtils.copyProperties(updateVO, authRole);
        UserBase userBase = getUserBase(userId);
        if (userBase != null) {
            authRole.setLastUpdater(userBase.getPhone());
        }
        int i = roleDao.updateByPrimaryKeySelective(authRole);
        //跟新角色权限的相关信息
        List<Long> menuPurviewIds = updateVO.getMenuPurviewIds();
        if (!CollectionUtils.isEmpty(menuPurviewIds)) {
            List<Long> menuIds = getMenuIds(menuPurviewIds, systemOriginEnum);
            //删除
            addRolePurview(updateVO.getId(), tenantId, menuIds);
            log.info("menuIds {}", menuIds);
            //更新角色redis的相关详细
            synRedis(systemOriginEnum, menuIds, updateVO.getId());
        }
        return 1;
    }

    @Override
    @Transactional
    public AuthRole addRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleUpdateVO updateVO) {
        AuthRole oldAuthRole = roleDao.selectByOriginTenantIdRoleName(systemOriginEnum.getType(), tenantId, updateVO.getRolename());
        if (oldAuthRole != null) {
            throw new BizException("角色名称已经存在");
        }
        AuthRole authRole = new AuthRole();
        BeanUtils.copyProperties(updateVO, authRole);
        authRole.setTenantId(tenantId);
        authRole.setSystemOrigin(systemOriginEnum.getType().byteValue());
        UserBase userBase = getUserBase(userId);
        if (userBase != null) {
            authRole.setLastUpdater(userBase.getPhone());
        }
        int superMan = updateVO.getSuperAdmin() == null ? 0 : updateVO.getSuperAdmin();
        authRole.setSuperAdmin((byte) superMan);
        authRole.setCreateTime(new Date());
        authRole.setUpdateTime(new Date());
        roleDao.insertSelective(authRole);
        List<Long> menuPurviewIds = updateVO.getMenuPurviewIds();
        if (!CollectionUtils.isEmpty(menuPurviewIds)) {
            List<Long> menuIds = getMenuIds(menuPurviewIds, systemOriginEnum);
            addRolePurview(authRole.getId(), tenantId, menuIds);
            synRedis(systemOriginEnum, menuIds, authRole.getId());
        }
        //超级管理员的话
        if (RoleTypeEnum.SUPER.code == superMan && CollectionUtils.isEmpty(menuPurviewIds)){
            synSuperRedis(systemOriginEnum, authRole.getId());
        }
        //更新角色redis的相关详细
        return authRole;
    }

    @Override
    @Transactional
    public int deleteRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, Long roleId) {
        //角色下存在id 不让删除
        if (authUserRoleDao.countUserIdListByRoleId(roleId) > 0) {
            throw new BizException("角色下有账号不能删除");
        }
        //删除角色
        roleDao.deleteByPrimaryKey(roleId);
        //删除用户角色 删除 角色菜单
        authRolePurviewDao.deleteByRoleId(roleId, tenantId);
        //user role
        authUserRoleDao.deleteByRoleId(roleId);
        String redisKey = RedisKeyUtils.getRolePurviewKey();
        redisTemplate.opsForHash().delete(redisKey, roleId + "");
        //删除 redis的角色信息
        return 1;
    }

    @Override
    public List<Long> getUserIdListByRoleId(List<Long> roleIds) {
        return authUserRoleDao.selectUserIdListByRoleId(roleIds);
    }

    @Override
    public List<AuthUserRoleDto> getUserRoleByUserList(List<Long> userIdList) {
        List<AuthUser> authUsers = authUserDao.selectByUserIds(userIdList);
        if (CollectionUtils.isEmpty(authUsers)) {
            return new ArrayList<>();
        }
        List<Long> baseUserIds = authUsers.stream().map(AuthUser::getUserBaseId).collect(Collectors.toList());
        Map<Long, AuthUserBase> authUserBaseMap = authUserBaseDao.selectByIds(baseUserIds).stream().collect(Collectors.toMap(AuthUserBase::getId, Function.identity()));
        if (CollectionUtils.isEmpty(authUsers)) {
            return new ArrayList<>();
        }
        //根据用户id查询角色信息
        List<AuthUserRoleIdDto> authRoles = roleDao.selectByUserIds(userIdList);
        Map<Long, List<AuthUserRoleIdDto>> roleMapList = authRoles.stream().collect(Collectors.groupingBy(AuthUserRoleIdDto::getUserId));
        return merge(authUsers, roleMapList, authUserBaseMap);
    }

    @Override
    public void createUserRole(SystemOriginEnum systemOriginEnum, List<UserRoleInput> userRoleInputs) {
        List<AuthUserRole> authUserRoles = new ArrayList<>(userRoleInputs.size());
        for (UserRoleInput it : userRoleInputs) {//一手机号为主
            String roleName = it.getRoleName();
            String userName = it.getUserName();
            if (StringUtils.isEmpty(roleName) || StringUtils.isEmpty(userName)) {
                log.warn("binlog追数据问题 用户角色 rolename {}, username {}", roleName, userName);
                continue;
            }
            //根据 角色名称+来源+租户id(0)查询角色信息
            AuthRole authRole = roleDao.selectByOriginTenantIdRoleName(systemOriginEnum.getType(), 0l, roleName);
            if (authRole == null) {
                log.warn("binlog追数据问题 用户角色 角色名称为空 rolename {}, username {}", roleName, userName);
                continue;
            }
            //更具用户名称去寻找auth_user_id
            AuthUserBase authUserBase = authUserBaseDao.selectByNameOrigin(userName);
            if (authUserBase == null) {
                log.warn("binlog追数据问题 用户角色 名称为空 rolename {}, username {}", roleName, userName);
                continue;
            }
            List<AuthUser> authUsers = authUserDao.selectByUserBaseId(authUserBase.getId()).stream().
                    filter(u -> u.getSystemOrigin().equals(systemOriginEnum.getType())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(authUsers)) {
                log.warn("binlog追数据问题 找不到 auth_user rolename {}, username {}", roleName, userName);
            }
            AuthUserRole authUserRole = new AuthUserRole();
            authUserRole.setRoleId(authRole.getId().intValue());
            authUserRole.setUserId(authUsers.get(0).getId().intValue());
            authUserRoles.add(authUserRole);
        }
        authUserRoleDao.batchAdd(authUserRoles);
    }

    private List<AuthUserRoleDto> merge(List<AuthUser> authUsers, Map<Long, List<AuthUserRoleIdDto>> roleMapList,
                                        Map<Long, AuthUserBase> authUserBaseMap) {
        return authUsers.stream().map(
                it -> {
                    AuthUserRoleDto authUserRoleDto = new AuthUserRoleDto();
                    Long userId = it.getId();
                    Long userBaseId = it.getUserBaseId();
                    AuthUserBase authUserBase = authUserBaseMap.get(userBaseId);
                    if (authUserBase != null) {
                        BeanUtils.copyProperties(authUserBase, authUserRoleDto);
                        authUserBase.setPassword(null);
                    }
                    authUserRoleDto.setRoles(roles(roleMapList.get(userId)));
                    authUserRoleDto.setId(userId);
                    return authUserRoleDto;
                }
        ).collect(Collectors.toList());
    }


    private List<AuthRole> roles(List<AuthUserRoleIdDto> roleIdDtos) {
        if (CollectionUtils.isEmpty(roleIdDtos)) {
            return new ArrayList<>();
        }
        return roleIdDtos.stream().map(
                it -> {
                    AuthRole authRole = new AuthRole();
                    BeanUtils.copyProperties(it, authRole);
                    return authRole;
                }
        ).collect(Collectors.toList());
    }


    private void addRolePurview(Long roleId, Long tenantId, List<Long> menuPurviewIds) {
        //删除权限 角色表的关联关系
        authRolePurviewDao.deleteByRoleId(roleId, tenantId);
        //批量新增角色权限表
        List<AuthRolePurview> authRolePurview = new ArrayList<>(menuPurviewIds.size());
        for (Long menuPurviewId : menuPurviewIds) {
            AuthRolePurview rolePurview = new AuthRolePurview();
            rolePurview.setRoleId(roleId.intValue());
            rolePurview.setPurviewId(menuPurviewId.intValue());
            rolePurview.setTenantId(tenantId.intValue());
            authRolePurview.add(rolePurview);
        }
        authRolePurviewDao.batchAdd(authRolePurview);
    }

    private void synRedis(SystemOriginEnum systemOriginEnum, List<Long> menuPurviewIds, Long roleId) {
        //根据权限id 获取权限 和 关联权限
        List<AuthMenuPurview> menus = authMenuPurviewDao.selectByIds(menuPurviewIds);
        Set<String> collect = menus.stream().map(AuthMenuPurview::getUrl).collect(Collectors.toSet());
        menus.stream().map(AuthMenuPurview::getSonPurview).collect(Collectors.toSet()).forEach(
                it -> {
                    if (StringUtils.isNotBlank(it)) {
                        String[] split = it.split(",");
                        collect.addAll(Arrays.asList(split));
                    }
                }
        );
        String redisKey = RedisKeyUtils.getRolePurviewKey();
        redisTemplate.opsForHash().put(redisKey, roleId.toString(), collect);
    }

    private void synSuperRedis(SystemOriginEnum systemOriginEnum, Long roleId) {
        //根据权限id 获取权限 和 关联权限
        List<AuthMenuPurview> menus = authMenuPurviewDao.selectMenus(systemOriginEnum.getType());
        Set<String> collect = menus.stream().map(AuthMenuPurview::getUrl).collect(Collectors.toSet());
        menus.stream().map(AuthMenuPurview::getSonPurview).collect(Collectors.toSet()).forEach(
                it -> {
                    if (StringUtils.isNotBlank(it)) {
                        String[] split = it.split(",");
                        collect.addAll(Arrays.asList(split));
                    }
                }
        );
        String redisKey = RedisKeyUtils.getRolePurviewKey();
        redisTemplate.opsForHash().put(redisKey, roleId.toString(), collect);
    }



    private List<Long> getMenuIds(List<Long> menuPurviewIds, SystemOriginEnum systemOriginEnum) {
        if (CollectionUtils.isEmpty(menuPurviewIds)) {
            return new ArrayList<>();
        }
        List<AuthMenuPurview> authMenuPurviews = authMenuPurviewDao.selectMenus(systemOriginEnum.getType());
        if (CollectionUtils.isEmpty(authMenuPurviews)) {
            return new ArrayList<>();
        }
        List<Long> allIds = authMenuPurviews.stream().map(AuthMenuPurview::getId).collect(Collectors.toList());
        menuPurviewIds.forEach(
                it -> {
                    if (!allIds.contains(it)) {
                        throw new BizException("该菜单也被删除");
                    }
                }
        );
        Map<Long, Integer> collect = authMenuPurviews.stream().collect(Collectors.toMap(AuthMenuPurview::getId, AuthMenuPurview::getParentId));
        List<Long> totalMenuPurviews = new ArrayList<>();
        for (Long it : menuPurviewIds) {
            Long parentId = collect.get(it) == null ? -1L : collect.get(it).longValue();
            if (parentId == -1) {
                continue;
            }
            totalMenuPurviews.add(parentId);
            Long p2ParentId = collect.get(parentId) == null ? -1L : collect.get(parentId).longValue();
            if (p2ParentId == -1) {
                continue;
            }
            totalMenuPurviews.add(p2ParentId);
            Long p3ParentId = collect.get(p2ParentId) == null ? -1L : collect.get(p2ParentId).longValue();
            if (p3ParentId == -1) {
                continue;
            }
            totalMenuPurviews.add(p3ParentId);
        }
        totalMenuPurviews.addAll(menuPurviewIds);
        return totalMenuPurviews.stream().distinct().collect(Collectors.toList());
    }


    public List<AuthMenuPurview> getAuthMenuPurviewIds(List<Long> menuPurviewIds, SystemOriginEnum systemOriginEnum) {
        List<Long> menuIds = getMenuIds(menuPurviewIds, systemOriginEnum);
        List<AuthMenuPurview> menus = authMenuPurviewDao.selectMenus(systemOriginEnum.getType()).stream().filter
                (it->menuIds.contains(it.getId())).collect(Collectors.toList());
        return menus;
    }
}
