package net.xianmu.authentication.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.dto.AuthMenuPurviewDto;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthRolePurview;
import net.xianmu.authentication.client.input.PurviewWeighVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.RolePreviewInput;
import net.xianmu.authentication.client.utils.RedisKeyUtils;
import net.xianmu.authentication.common.enmu.RoleTypeEnum;
import net.xianmu.authentication.common.util.MenuTreeUtils;
import net.xianmu.authentication.mapper.auth.AuthMenuPurviewDao;
import net.xianmu.authentication.mapper.auth.AuthRoleDao;
import net.xianmu.authentication.mapper.auth.AuthRolePurviewDao;
import net.xianmu.authentication.model.entity.AuthTenantPrivileges;
import net.xianmu.authentication.service.AuthMenuService;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.user.UserBase;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.TRUE;

@Service
@Slf4j
public class AuthMenuServiceImpl extends BaseService implements AuthMenuService {
    @Resource
    private AuthMenuPurviewDao menuPurviewDao;
    @Resource
    private AuthRolePurviewDao authRolePurviewDao;
    @Resource(name = "authRedisTemplate")
    private RedisTemplate redisTemplate;
    @Resource
    AuthRoleDao authRoleDao;
    @Resource
    AuthMenuPurviewDao authMenuPurviewDao;
    @Resource
    AuthRoleServiceImpl roleService;


    @Resource
    private AuthTenantPrivilegesService authTenantPrivilegesService;
    @Override
    public List<AuthMenuPurview> getAuthMenuPurviews(SystemOriginEnum systemOriginEnum, Long tenantId, Long userId) {
        //查询某人的菜单权限
        List<AuthRole> roles = authRoleDao.selectByUserId(userId).stream().filter(it->it.getTenantId().equals(tenantId)).collect(Collectors.toList());
        //如果包括超级管理员
        Set<Long> roledIds = roles.stream().map(AuthRole::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(roledIds)) {
            return new ArrayList<>();
        }
        Optional<AuthRole> any = roles.stream().filter(it -> it.getSuperAdmin().equals((byte) 1)).findAny();
        List<AuthMenuPurview> outs = new ArrayList<>();
        if (any.isPresent()){
            outs.addAll(menuPurviewDao.selectMenus(systemOriginEnum.getType()));
        }else {
            outs.addAll(menuPurviewDao.selectByRolIds(systemOriginEnum.getType(), roledIds, tenantId));
        }
        outs = authTenantPrivilegesService.mergeTenantPurviewExpireTime(systemOriginEnum, tenantId , outs);
        return outs;
    }

    @Override
    public List<AuthMenuPurview> getAuthMenuPurviews(SystemOriginEnum systemOriginEnum) {
        return menuPurviewDao.selectMenus(systemOriginEnum.getType());
    }

    @Override
    public List<AuthMenuPurview> getAuthTenantMenuPurview(SystemOriginEnum systemOriginEnum, Long tenantId) {
        List<AuthMenuPurview> authMenuPurviews = getAuthMenuPurviews(systemOriginEnum);
        return authTenantPrivilegesService.mergeTenantPurviewExpireTime(systemOriginEnum, tenantId, authMenuPurviews);
    }


    @Override
    public int addPurviews(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthMenuPurview authMenuPurview) {
        //根据菜单名称查询失败抛出异常
        AuthMenuPurview selectByName = menuPurviewDao.selectByName(systemOriginEnum.getType(), authMenuPurview.getMenuName());
        if (selectByName != null) {
            //自定义异常
            throw new BizException("菜单名称已经存在");
        }
        if (!StringUtils.isEmpty(authMenuPurview.getUrl())){
            AuthMenuPurview url = menuPurviewDao.selectByUrl(systemOriginEnum.getType(), authMenuPurview.getUrl());
            if (url != null) {
                //自定义异常
                throw new BizException("权限码已经存在");
            }
        }

        authMenuPurview.setSystemOrigin(systemOriginEnum.getType().byteValue());
        UserBase userBase = getUserBase(userId);
        if (userBase != null) {
            authMenuPurview.setLastUpdater(userBase.getPhone());
        }
        //查询父id获取的最大的weight 父id和来源呢
        int maxWeight = menuPurviewDao.selectMaxWeightBySystemOriginEnumParentId(systemOriginEnum.getType(),  authMenuPurview.getParentId());
        authMenuPurview.setWeight(maxWeight);
        return menuPurviewDao.insertSelective(authMenuPurview);
    }

    @Override
    @Transactional
    public int updatePurviews(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthMenuPurview authMenuPurview) {
        //查询菜单名称是否存在
        AuthMenuPurview oldPurview = menuPurviewDao.selectByPrimaryKey(authMenuPurview.getId());
        if (oldPurview == null) {
            throw new BizException("权限呢已被删除");
        }
        if (!StringUtils.isEmpty(authMenuPurview.getMenuName()) && !oldPurview.getMenuName().equals(authMenuPurview.getMenuName())) {
            AuthMenuPurview selectByName = menuPurviewDao.selectByName(systemOriginEnum.getType(), authMenuPurview.getMenuName());
            if (selectByName != null) {
                throw new BizException("菜单名称已经存在");
            }
        }
        if (!StringUtils.isEmpty(authMenuPurview.getUrl()) && !oldPurview.getUrl().equals(authMenuPurview.getUrl())){
            AuthMenuPurview url = menuPurviewDao.selectByUrl(systemOriginEnum.getType(), authMenuPurview.getUrl());
            if (url != null) {
                //自定义异常
                throw new BizException("权限码已经存在");
            }
        }
        UserBase userBase = getUserBase(userId);
        if (userBase != null) {
            authMenuPurview.setLastUpdater(userBase.getPhone());
        }
        //如果权限变成 同步变更到redis 权限的url和相关url 查询所有角色的url
        int i = menuPurviewDao.updateByPrimaryKeySelective(authMenuPurview);
        //更新redis的配置查询所有角色的key 操作很重
        if (!Objects.equals(authMenuPurview.getUrl(), oldPurview.getUrl()) || !Objects.equals(authMenuPurview.getSonPurview(), oldPurview.getSonPurview())) {
            List<Long> roleIds = authRolePurviewDao.selectRoleIdByPurviewId(authMenuPurview.getId().intValue());
            for (Long roleId : roleIds) {
                synRedis(systemOriginEnum ,roleId, authMenuPurview);
            }
        }
        return i;
    }

    public void synRedis(SystemOriginEnum systemOriginEnum, Long roleId, AuthMenuPurview dbAuthMenuPurview) {
        String redisKey = RedisKeyUtils.getRolePurviewKey();

        AuthRole authRole = authRoleDao.selectByPrimaryKey(roleId);
        if (authRole == null){
            return;
        }
        Set<Long> roleIDs = new HashSet<>();
        roleIDs.add(roleId);
        List<AuthMenuPurview> menus = Objects.equals(authRole.getSuperAdmin().intValue(), RoleTypeEnum.SUPER.code) ? authMenuPurviewDao.selectMenus(systemOriginEnum.getType()) : authMenuPurviewDao.selectByRolIds(systemOriginEnum.getType(), roleIDs, null);
        Set<String> collect = menus.stream().map(AuthMenuPurview::getUrl).collect(Collectors.toSet());
        menus.stream().map(AuthMenuPurview::getSonPurview).collect(Collectors.toSet()).forEach(
                u -> {
                    if (net.summerfarm.common.util.StringUtils.isNotBlank(u)) {
                        String[] split = u.split(",");
                        collect.addAll(Arrays.asList(split));
                    }
                }
        );
        log.info("roleID {} urls {}", roleId, JSONUtil.toJsonStr(collect));
        redisTemplate.opsForHash().put(redisKey, roleId.toString(), collect);
    }

    @Override
    @Transactional
    public int deletePurviews(SystemOriginEnum systemOriginEnum, Long userId, Long purviewId) {
        //删除菜单权限 删除 url菜单权限
        //若下面有子就不让删除
        int count = menuPurviewDao.countMenusByPid(purviewId);
        if (count > 0) {
            throw new BizException("菜单下有子菜单禁止删除");
        }
        return menuPurviewDao.deleteByPrimaryKey(purviewId);
    }

    @Override
    @Transactional
    public int updateMenusWeigh(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, List<PurviewWeighVO> purviewWeighVOS) {
        if (CollectionUtils.isEmpty(purviewWeighVOS)){
            throw new BizException("参数异常");
        }
        PurviewWeighVO purviewWeighVO = purviewWeighVOS.get(0);
        //更改权重
        AuthMenuPurview authMenuPurview  = menuPurviewDao.selectByPrimaryKey(purviewWeighVO.getId());
        if (authMenuPurview == null){
            throw new BizException("权限不存在");
        }
        UserBase userBase = getUserBase(userId);
        /**
         *  -1 上升 +1 下沉
         */
        int weight = purviewWeighVO.getWeight();
        int lastWeight = 0;

        //获取同一级别父id的所有权限
        List<AuthMenuPurview> authMenuPurviews = menuPurviewDao.selectBySourceParentId(systemOriginEnum.getType(), authMenuPurview.getParentId(),weight);
        for (int i = 0; i < authMenuPurviews.size(); i++) {
            if (authMenuPurview.getId().equals(authMenuPurviews.get(i).getId())) {
                break;
            }
            AuthMenuPurview authMenuPurview1 = authMenuPurviews.get(i);
            int newWeight;
            if (weight>0){
                newWeight = (authMenuPurviews.size()-i)  + authMenuPurview1.getWeight();
            }else {
                newWeight = authMenuPurview1.getWeight() - (authMenuPurviews.size()-i);
            }
            authMenuPurview1.setWeight(newWeight);
            lastWeight = newWeight;
            menuPurviewDao.updateByPrimaryKeySelective(authMenuPurview1);
        }
        //自己或者减1
        AuthMenuPurview dbAuthMenuPurview = new AuthMenuPurview();
        dbAuthMenuPurview.setId(authMenuPurview.getId());
        dbAuthMenuPurview.setWeight(lastWeight+weight);
        //根据更新时间排序 更新时间很重要
        dbAuthMenuPurview.setUpdateTime(new Date());
        if (userBase != null) {
            dbAuthMenuPurview.setLastUpdater(userBase.getPhone());
        }
        menuPurviewDao.updateByPrimaryKeySelective(dbAuthMenuPurview);
        //查询 父id一致的 根据weigh排序
        return 1;
    }

    @Override
    public int createRolePurviews(SystemOriginEnum systemOriginEnum, List<RolePreviewInput> rolePreviewInputs) {
        if (CollectionUtils.isEmpty(rolePreviewInputs)) {
            return 1;
        }
        List<AuthRolePurview> authRolePurview = new ArrayList<>(rolePreviewInputs.size());
        for (RolePreviewInput rolePreviewInput : rolePreviewInputs) {
            String roleName = rolePreviewInput.getRoleName();
            if (StringUtils.isEmpty(roleName)){
                log.warn("binlog修复数据 角色菜单 角色名为空 {} ",roleName);
                continue;
            }
            String urls = rolePreviewInput.getUrls();
            String menus = rolePreviewInput.getMenus();
            if (StringUtils.isEmpty(urls) && StringUtils.isEmpty(menus)) {
                log.warn("binlog修复数据 角色菜单 菜单名称 or url为空 {}   {}", menus, urls);
                continue;
            }
            AuthRole authRole = authRoleDao.selectByOriginTenantIdRoleName(systemOriginEnum.getType(), 0l, roleName);
            if (authRole  == null){
                log.warn("binlog修复数据  角色找不到 {}", roleName);
                continue;
            }
            AuthMenuPurview authMenuPurview = menuPurviewDao.selectBySourceNameUrls(systemOriginEnum.getType(), menus, urls);
            if (authMenuPurview  == null){
                log.warn("binlog修复数据  菜单找不到 menus:{} urls:{}", menus, urls);
                continue;
            }
            AuthRolePurview rolePurview = new AuthRolePurview();
            rolePurview.setRoleId(authRole.getId().intValue());
            rolePurview.setPurviewId(authMenuPurview.getId().intValue());
            rolePurview.setTenantId(0);
            authRolePurview.add(rolePurview);
        }
        authRolePurviewDao.batchAdd(authRolePurview);
        return 1;
    }

    @Override
    public List<AuthMenuPurviewDto> getMenusTreeByMenuIds(SystemOriginEnum systemOriginEnum, Long tenantId, List<Long> menuIds) {
        if (CollectionUtils.isEmpty(menuIds)){
            return new ArrayList<>();
        }
        List<AuthMenuPurview> authMenuPurviews = roleService.getAuthMenuPurviewIds(menuIds, systemOriginEnum);
        return MenuTreeUtils.convertTree(authMenuPurviews);
    }

}