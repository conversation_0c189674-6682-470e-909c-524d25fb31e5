package net.xianmu.authentication.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.org.checkerframework.checker.units.qual.A;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.PropertiesUtils;
import net.summerfarm.common.util.weixin.WeChatUtils;
import net.summerfarm.enums.login.UserStatusEnum;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.login.AuthClientLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthClientMallLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;
import net.xianmu.authentication.common.config.XianMuWxConfig;
import net.xianmu.authentication.common.enmu.LoginTypeEnum;
import net.xianmu.authentication.common.util.WxUtils;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.model.BO.WxOpenIdBO;
import net.xianmu.authentication.model.DTO.WechatQueryDTO;
import net.xianmu.authentication.service.AuthClientLoginService;
import net.xianmu.common.enums.base.auth.AccountStatusEnum;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import org.apache.shiro.SecurityUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AuthClientLoginServiceImpl implements AuthClientLoginService {
    @Resource
    AuthLoginServiceImpl authLoginService;
    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthUserBaseDao authUserBaseDao;
    @Resource
    AuthUserAuthDao authUserAuthDao;
    @Resource
    XianMuWxConfig xianMuWxConfig;

    @Resource
    WxUtils wxUtils;
    private static final String MP_APPID = "xm.mall-wechat.mp-app.id";
    private static final String MP_SECRET = "xm.mall-wechat.mp-app.secret";

    private static final String APPID = "xm.mall-wechat.app.id";
    private static final String SECRET = "xm.mall-wechat.app.secret";

    @Override
    @Transactional
    public AuthLoginDto authClientLogin(AuthClientLoginProviderInput authClientLoginProviderInput) {
        if (authClientLoginProviderInput.getSystemOriginEnum() == null || authClientLoginProviderInput.getSystemOriginEnum().type<SystemOriginEnum.COSFO_MALL.type){
            throw new DefaultServiceException("systemOriginEnum 错误");
        }
        if (authClientLoginProviderInput.getBizId()==null){
            throw new DefaultServiceException("参数错误 bizId不能为空");
        }
        if (authClientLoginProviderInput.getTenantId() == null){
            throw new DefaultServiceException("参数错误 tenantId 不能为空");
        }
        AuthUser authUser = authUserDao.selectByBizUserIdTenantId(authClientLoginProviderInput.getSystemOriginEnum().type, authClientLoginProviderInput.getBizId(), authClientLoginProviderInput.getTenantId());
        if (authUser == null){
            throw new DefaultServiceException("用户不存在");
        }
        if (Objects.equals(UserStatusEnum.IN_VALID.getStatus(),authUser.getStatus().intValue())){
            throw new DefaultServiceException("用户已被删除");
        }
//        if (!Objects.equals(AccountStatusEnum.AUDIT_SUCCESS.status,authUser.getAuditStatus().intValue())){
//            throw new DefaultServiceException("用户在审核中");
//        }
        AuthUserBase userBase = authUserBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (userBase == null){
            throw new DefaultServiceException("用户数据错误");
        }
        //shiro登录 shirouser仅仅放最基础的信息
        ShiroUser shiroUser = authLoginService.buildShiroUser(authUser, userBase, new ArrayList<>(), authClientLoginProviderInput.getSystemOriginEnum(), LoginTypeEnum.SECOND_TOKEN);
        //登陆
        AuthLoginDto authLoginRespDTO = authLoginService.shiroLogin(shiroUser, LoginTypeEnum.SECOND_TOKEN, authClientLoginProviderInput.getSystemOriginEnum(), authClientLoginProviderInput.getExpireTime());

        if (!StringUtils.isEmpty(authClientLoginProviderInput.getOpenid())
                && authClientLoginProviderInput.getLoginType() != null) {
            authUserAuthDao.deleteByAuthIdType(authUser.getId(), authClientLoginProviderInput.getLoginType().getType());
            AuthUserAuth authUserAuth = new AuthUserAuth();
            authUserAuth.setUserId(authUser.getId());
            authUserAuth.setAuthType(authClientLoginProviderInput.getLoginType().getType().byteValue());
            authUserAuth.setAuthId(authClientLoginProviderInput.getOpenid());
            authUserAuth.setThirdPartyId(authClientLoginProviderInput.getUnionId());
            authUserAuth.setCreateTime(new Date());
            authUserAuth.setUpdateTime(new Date());
            authUserAuthDao.insertSelective(authUserAuth);
        }
        return authLoginRespDTO;
    }

    @Override
    public AuthLoginDto authLogin(AuthClientMallLoginProviderInput input) {
        if (input.getSystemOriginEnum() == null || !Objects.equals(SystemOriginEnum.MALL.type, input.getSystemOriginEnum().type)){
            throw new DefaultServiceException("systemOriginEnum 错误");
        }
        if (input.getTenantId() == null){
            throw new DefaultServiceException("参数错误 tenantId 不能为空");
        }
        if (StringUtils.isEmpty(input.getUnionid()) && StringUtils.isEmpty(input.getOpenid()) && input.getBizId() == null){
            throw new DefaultServiceException("ou信息 或 bizId 不能为空");
        }
        //获取openid unionid
        WxOpenIdBO xwBo = new WxOpenIdBO();
        if (!StringUtils.isEmpty(input.getUnionid())){
            xwBo.setUnionid(input.getUnionid());
        }
        if (!StringUtils.isEmpty(input.getOpenid())){
            xwBo.setOpenid(input.getOpenid());
        }
        AuthUser authUser = findAuthUser(input, xwBo);
        if (authUser == null){
            log.warn("用户授权信息查不到 {} ",JSONObject.toJSONString(xwBo));
            return null;
        }
        AuthUserBase userBase = authUserBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (userBase == null){
            throw new DefaultServiceException("用户数据错误");
        }

        ShiroUser shiroUser = authLoginService.buildShiroUser(authUser, userBase, new ArrayList<>(), input.getSystemOriginEnum(), LoginTypeEnum.SECOND_TOKEN);
        //登陆
        AuthLoginDto authLoginDto = authLoginService.shiroLogin(shiroUser, LoginTypeEnum.SECOND_TOKEN, input.getSystemOriginEnum());
        if (xwBo != null) {
            if (!StringUtils.isEmpty(xwBo.getOpenid())) {
                authLoginDto.setOpenId(xwBo.getOpenid());
            }
            if (!StringUtils.isEmpty(xwBo.getUnionid())) {
                authLoginDto.setUnionId(xwBo.getUnionid());
            }
        }
        return authLoginDto;
    }

    @Override
    public AuthQueryWechatInfoDTO authMallQueryWechatInfo(AuthQueryWechatInfoInput authQueryWechatInfoInput) {
        WechatQueryDTO  input = new WechatQueryDTO();
        input.setPopMerchant(false);
        input.setLoginType(authQueryWechatInfoInput.getType());
        input.setCode(authQueryWechatInfoInput.getCode());
        WxOpenIdBO xwBo = findXwBo(input);
        if (xwBo == null){
            return null;
        }
        AuthQueryWechatInfoDTO dto = new AuthQueryWechatInfoDTO();
        dto.setUnionid(xwBo.getUnionid());
        dto.setOpenid(xwBo.getOpenid());
        return dto;
    }

    @Override
    public AuthQueryWechatInfoDTO authPopMallQueryWechatInfo(AuthQueryWechatInfoInput authQueryWechatInfoInput) {
        WechatQueryDTO  input = new WechatQueryDTO();
        input.setPopMerchant(true);
        input.setLoginType(authQueryWechatInfoInput.getType());
        input.setCode(authQueryWechatInfoInput.getCode());
        WxOpenIdBO xwBo = findXwBo(input);
        if (xwBo == null){
            return null;
        }
        AuthQueryWechatInfoDTO dto = new AuthQueryWechatInfoDTO();
        dto.setUnionid(xwBo.getUnionid());
        dto.setOpenid(xwBo.getOpenid());
        return dto;
    }

    public WxOpenIdBO findXwBo(WechatQueryDTO input){
        AuthTypeEnum loginType = input.getLoginType();
        boolean isPop = input.isPopMerchant();
        WxOpenIdBO wxOpenIdBO;
        String appId = null;
        String appSecret = null;
        // 鲜沐商城
        log.info("AuthClientLoginServiceImpl.findXwBo input :{}", JSON.toJSONString(input));
        if(isPop) {
            //小程序登陆
            if (loginType.getType().equals(AuthTypeEnum.WEI_CHAT.getType())) {
                appId = xianMuWxConfig.getPopMallWxAppId();
                appSecret = xianMuWxConfig.getPopMallWxSecret();
                //公账号登陆
            } else if (loginType.getType().equals(AuthTypeEnum.OFFICIAL_WE_CHAT.getType())) {
                appId = xianMuWxConfig.getPopMallWxOaAppId();
                appSecret = xianMuWxConfig.getPopMallWxOaSecret();
            }
        } else {
            //小程序登陆
            if (loginType.getType().equals(AuthTypeEnum.WEI_CHAT.getType())) {
                appId = xianMuWxConfig.getXmMallWxAppId();
                appSecret = xianMuWxConfig.getXmMallWxSecret();
                //公账号登陆
            } else if (loginType.getType().equals(AuthTypeEnum.OFFICIAL_WE_CHAT.getType())) {
                appId = xianMuWxConfig.getXmMallWxOaAppId();
                appSecret = xianMuWxConfig.getXmMallWxOaSecret();
            }
        }
        wxOpenIdBO = wxUtils.getOaOpenId(input.getLoginType() , input.getCode(), appId, appSecret);
        log.info("获取微信小程序的ou信息, code{} result{}", input.getCode(), JSONUtil.toJsonStr(wxOpenIdBO));
        return wxOpenIdBO;
    }

    private AuthUser findAuthUser(AuthClientMallLoginProviderInput  input, WxOpenIdBO wxOpenIdBO){
        if (input.getBizId() != null) {
            return authUserDao.selectByBizUserIdTenantId(input.getSystemOriginEnum().type, input.getBizId(), input.getTenantId());
        }
        AuthUser  authUser;
        //先查询uninid
        if (!StringUtils.isEmpty(wxOpenIdBO.getUnionid())){
            authUser = query(input,null,wxOpenIdBO.getUnionid());
            if (authUser != null){
                return authUser;
            }
        }
        if (!StringUtils.isEmpty(wxOpenIdBO.getOpenid())){
            authUser = query(input,wxOpenIdBO.getOpenid(), null);
            if (authUser != null) {
                return authUser;
            }
        }
        return  null;
    }


    private AuthUser query(AuthClientMallLoginProviderInput  input, String openid ,String unionid){
        if (StringUtils.isEmpty(openid) && StringUtils.isEmpty(unionid)){
            return  null;
        }
        List<AuthUserAuth> authUserAuths = new ArrayList<>();
        if (!StringUtils.isEmpty(openid)){
            authUserAuths.addAll(authUserAuthDao.selectByOpenIdType(input.getLoginType().getType(), openid));
        }
        if (!StringUtils.isEmpty(unionid)){
            authUserAuths.addAll(authUserAuthDao.selectByThirdIdType(input.getLoginType().getType(), unionid));
        }
        if (!CollectionUtils.isEmpty(authUserAuths)){
            List<Long> userIds = authUserAuths.stream().map(AuthUserAuth::getUserId).distinct().collect(Collectors.toList());
            AuthUser authUser = authUserDao.selectByUserIds(userIds).stream().filter
                    (it -> Objects.equals(it.getSystemOrigin(), input.getSystemOriginEnum().type)
                            && it.getTenantId().equals(input.getTenantId())).findFirst().orElse(null);
            return authUser;
        }
        return null;
    }

}
