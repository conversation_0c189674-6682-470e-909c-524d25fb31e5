package net.xianmu.authentication.service.impl;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.MD5Util;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.AuthUserRole;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.authentication.mapper.auth.AuthRoleDao;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.xianmu.authentication.mapper.auth.AuthUserRoleDao;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.authentication.service.AuthUserBaseService;
import net.xianmu.authentication.service.AuthUserServiceV2;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.exception.ParamsException;
import net.xianmu.common.user.UserBase;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.PWD_PREFIX;
import static net.xianmu.authentication.common.AuthGlobal.TENANT_ID;
import static net.xianmu.common.enums.base.auth.SystemOriginEnum.COSFO_MALL;

@Service
@Slf4j
public class AuthUserServiceV2Impl extends BaseService implements AuthUserServiceV2 {
    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthUserBaseDao userBaseDao;
    @Resource
    AuthUserRoleDao authUserRoleDao;
    @Resource
    AuthRoleDao roleDao;
    @Resource
    AuthUserPropertiesExtDao authUserPropertiesExtDao;
    @Resource(name = "authRedisTemplate")
    RedisTemplate redisTemplate;

    @Resource(name = "redisTemplate")
    RedisTemplate baseRedisTemplate;
    @Resource
    AuthUserBaseService authUserBaseService;
    @Resource
    AuthUserAuthDao authUserAuthDao;
    @Resource
    @Lazy
    AuthLoginServiceImpl authLoginService;
    @Resource
    AuthTenantPrivilegesService authTenantPrivilegesService;


    @Override
    @Transactional
    public UserBase createUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {

        if (SystemOriginEnum.SRM.getType().equals(systemOriginEnum.getType()) ||
                SystemOriginEnum.TMS.getType().equals(systemOriginEnum.getType()) ||
                SystemOriginEnum.ADMIN.getType().equals(systemOriginEnum.getType()) ||
                SystemOriginEnum.CRM.getType().equals(systemOriginEnum.getType())){
            return createUser(systemOriginEnum, userBase, false);
        }

        if (SystemOriginEnum.COSFO_MANAGE.getType().equals(systemOriginEnum.getType()) ||
                SystemOriginEnum.COSFO_OMS.getType().equals(systemOriginEnum.getType())){
            return createSaaSManageUser(systemOriginEnum, userBase, baseUserExtend);
        }

        if (SystemOriginEnum.COSFO_MALL.getType().equals(systemOriginEnum.getType()) ||
                SystemOriginEnum.MALL.getType().equals(systemOriginEnum.getType())){
            return createMallUser(systemOriginEnum, userBase, baseUserExtend);
        }

        log.error("暂不支持的系统来源，systemOriginEnum：{}", systemOriginEnum);
        throw new ParamsException("未知的系统来源!");
    }



    private UserBase createMallUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend){
        log.warn("新的创建mall,cosfo-mall账号的入口被触发了。systemOriginEnum:{}, userBase：{}, baseUserExtend:{}", systemOriginEnum, JSON.toJSONString(userBase), JSON.toJSONString(baseUserExtend));
        //兼容 cosfo_oms 和 cosfo_manage
        check(systemOriginEnum, userBase, baseUserExtend);

        // 删除账号？
        if (baseUserExtend.getDeleteAccountRelation()!=null
                &&baseUserExtend.getDeleteAccountRelation() ){
            deleteRelation(systemOriginEnum, userBase ,baseUserExtend);
            return null ;
        }
        //基础表新增
        addUserBase(userBase);

        //auth_user新增
        addAuthUser(systemOriginEnum, userBase, baseUserExtend);
        //扩展属性
        addAuthUserAuth(systemOriginEnum, userBase, baseUserExtend);
        return userBase;
    }






    @Transactional
    public UserBase createUser(SystemOriginEnum systemOriginEnum, UserBase baseUser,Boolean oldUser) {
        if (systemOriginEnum == null) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "系统来源不能为空");
        }
        log.warn("新的创建通用鲜沐账号的入口被触发了。systemOriginEnum:{}, userBase：{}, baseUserExtend:{}", systemOriginEnum, JSON.toJSONString(baseUser));

        String pwd = baseUser.getPassword();
        Long tenantId = baseUser.getTenantId() == null ? TENANT_ID : baseUser.getTenantId();
        //初始化用户信息
        UserBase userBase = createUserBase(systemOriginEnum, baseUser, oldUser);
        //初始化用户角色信息
        List<Long> roleIds = baseUser.getRoleIds() == null ? new ArrayList<>() : baseUser.getRoleIds();
        List<Long> roleIDs = new ArrayList<>(roleIds);
        if (roleIDs.isEmpty() && SystemOriginEnum.COSFO_OMS.getType().equals(systemOriginEnum.getType())) {
            AuthRole authRole = roleDao.selectBySourceTenantSuperRole(systemOriginEnum.getType(), tenantId);
            if (authRole == null) {
                throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "初始化数据问题");
            }
            roleIDs.add(authRole.getId());
        }
        // roleIds初始化
        if (!CollectionUtils.isEmpty(baseUser.getRoleIds())) {
            List<AuthUserRole> authUserRoles = roleIDs.stream().map(it -> {
                        AuthUserRole authRle = new AuthUserRole();
                        authRle.setRoleId(it.intValue());
                        authRle.setUserId(userBase.getId().intValue());
                        authRle.setCreateTime(new Date());
                        authRle.setUpdateTime(new Date());
                        return authRle;
                    }
            ).collect(Collectors.toList());
            authUserRoleDao.batchAdd(authUserRoles);
        }
        if (SystemOriginEnum.TMS.getType().equals(systemOriginEnum.getType())
                || SystemOriginEnum.SRM.getType().equals(systemOriginEnum.getType())){
            authUserBaseService.updateAllPwd(userBase.getUserBaseId(),pwd);
        }
        return userBase;
    }

    public UserBase createUserBase(SystemOriginEnum systemOriginEnum, UserBase baseUser, Boolean oldUser) {

        // 前置参数校验
        if (StringUtils.isEmpty(baseUser.getUsername())) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "用户名不能为空");
        }
        if (StringUtils.isEmpty(baseUser.getPhone()) && StringUtils.isEmpty(baseUser.getEmail())) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "手机号和邮箱必须填写一个");
        }
        Long tenantId = baseUser.getTenantId() == null ? TENANT_ID : baseUser.getTenantId();
        String phone = baseUser.getPhone();
        String email = baseUser.getEmail();


        // 获取唯一base信息
        AuthUserBase userBase = getAuthUserBaseByPhoneOrEmail(phone, email);
        if (userBase != null) {
            // 判断当前体系+租户是否已经存在 用户
            List<AuthUser> authUsers = authUserDao.selectByUserBaseId(userBase.getId()).stream().filter(
                    it -> it.getTenantId().equals(tenantId) && it.getSystemOrigin().equals(systemOriginEnum.getType())
            ).collect(Collectors.toList());
            if (!authUsers.isEmpty()) {
                log.info("该体系下账户已存在!authUser:{}", JSON.toJSONString(authUsers));
                throw new DefaultServiceException(ResultConstant.MNAME_EXIST, "账号已存在");
            }
            // 来自admin的特殊操作
            updateUserName(systemOriginEnum, userBase, baseUser);
            AuthUser authUser = addAuthUser(baseUser, systemOriginEnum, tenantId, userBase.getId());
            baseUser.setId(authUser.getId());
            baseUser.setUserBaseId(authUser.getUserBaseId());
            return baseUser;
        }


        //创建userBase
        // 兜底校验
        this.checkUsernameExistForCreate(baseUser.getUsername());
        AuthUserBase record = new AuthUserBase();
        BeanUtils.copyProperties(baseUser, record);
        if (!StringUtils.isEmpty(baseUser.getPassword()) && !oldUser) {
            record.setPassword(MD5Util.string2MD5(baseUser.getPassword()));
        }
        userBaseDao.insertSelective(record);


        //创建 authUser
        AuthUser authUser = addAuthUser(baseUser, systemOriginEnum, tenantId, record.getId());
        baseUser.setId(authUser.getId());
        baseUser.setUserBaseId(record.getId());
        return baseUser;
    }

    private AuthUserBase getAuthUserBaseByPhoneOrEmail(String phone, String email) {
        // 检查手机号和邮箱获取base用户信息
        AuthUserBase userBase = userBaseDao.selectByPhone(phone);
        if (userBase == null) {
            userBase = userBaseDao.selectByEmail(email);
        }
        return userBase;
    }

    private void checkUsernameExistForCreate(String username){
        //创建userBase, 兜底校验
        AuthUserBase authUserBase = userBaseDao.selectByNameOrigin(username);
        if(authUserBase != null) {
            log.error("当前用户名已被使用!占用的账户为：{}", JSON.toJSONString(authUserBase));
            throw new BizException("当前用户名已被使用!");
        }
    }

    public void  updateUserName(SystemOriginEnum systemOriginEnum,AuthUserBase oldUserBase, UserBase baseUser){
        if (!systemOriginEnum.equals(SystemOriginEnum.ADMIN)){
            return;
        }
        log.info("先在别的域添加账号后 添加账号 oldUserBase:{}, new baseUser:{}", JSON.toJSONString(oldUserBase) ,JSON.toJSONString(baseUser));

        // 更新用户名、昵称
        if(oldUserBase.getNickname() == null) {
            AuthUserBase update = new AuthUserBase();
            update.setId(oldUserBase.getId());
            update.setNickname(baseUser.getNickname());
            userBaseDao.updateByPrimaryKeySelective(update);
        }
    }

    private AuthUser addAuthUser(UserBase baseUser, SystemOriginEnum systemOriginEnum, Long tenantId, Long userBaseId) {
        String encryptedPassword = MD5Util.string2MD5(baseUser.getPassword());

        // 如果是COSFO_MANAGE新增用户，该用户手机号可能已经注册过其他租户。如果注册过，记录保存老密码；未注册，使用传参新密码
        if(SystemOriginEnum.COSFO_MANAGE == systemOriginEnum){
            //查询userBaseId是否注册过其他租户
            List<AuthUser> userList = authUserDao.selectByUserBaseId(userBaseId);
            //根据来源查询
            AuthUser user = Optional.ofNullable(userList).orElse(Collections.emptyList()).stream()
                    .filter(it -> Objects.equals(it.getSystemOrigin(), systemOriginEnum.getType()))
                    .findFirst()
                    .orElse(null);
            if (user != null){
                encryptedPassword = user.getPassword();
                log.info("auth新增cosfo-manage用户 tenantId={}, userBaseId={}，没有使用新密码newPassword={}, 使用了旧密码user={}", tenantId, userBaseId, baseUser.getPassword(), JSON.toJSONString(user));
            }
        }

        AuthUser authUser = new AuthUser();
        authUser.setStatus((byte) 0);
        authUser.setTenantId(tenantId);
        authUser.setUserBaseId(userBaseId);
        authUser.setUpdateTime(new Date());
        authUser.setCreateTime(new Date());
        authUser.setSystemOrigin(systemOriginEnum.getType());
        authUser.setPassword(encryptedPassword);
        authUserDao.insertSelective(authUser);
        // 来自admin和 cosfo的  user_auth bizId 也就是 id
        Long userId = authUser.getId();
        AuthUser updateAetherUser =new AuthUser();
        updateAetherUser.setId(userId);
        updateAetherUser.setBizUserId(userId);
        authUserDao.updateByPrimaryKeySelective(updateAetherUser);
        return authUser;
    }


    public void deleteRelation(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend){
        AuthUser authUser = authUserDao.selectByBizUserIdTenantId(systemOriginEnum.getType(), baseUserExtend.getBizUserId(),userBase.getTenantId());
        if (authUser == null){
            return;
        }
        authUserDao.deleteByPrimaryKey(authUser.getId());
        authUserAuthDao.deleteByAuthIdType(authUser.getId(), AuthTypeEnum.OFFICIAL_WE_CHAT.getType());
        authUserAuthDao.deleteByAuthIdType(authUser.getId(), AuthTypeEnum.WEI_CHAT.getType());
    }

    private void check(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {
        if (systemOriginEnum == null || userBase == null) {
            throw new BizException("参数错误");
        }
        if (SystemOriginEnum.COSFO_MANAGE.getType() > systemOriginEnum.getType()) {
            throw new BizException("错误的系统来源");
        }
        if (StringUtils.isEmpty(userBase.getPhone())) {
            throw new BizException("手机号不能为空");
        }
        if (StringUtils.isEmpty(userBase.getTenantId())) {
            throw new BizException("租户id 不能为空");
        }
        if (StringUtils.isEmpty(userBase.getStatus())) {
            throw new BizException("用户状态不能为空");
        }
    }


    private void addUserBase(UserBase userBase){
        String phone = userBase.getPhone();
        AuthUserBase authUserBase = this.getAuthUserBaseByPhoneOrEmail(phone, null);
        //创建userBase
        if (authUserBase == null){
            AuthUserBase insertUserBase = new AuthUserBase();
            String username = userBase.getUsername() == null ? phone : userBase.getUsername();
            this.checkUsernameExistForCreate(username);
            insertUserBase.setPhone(phone);
            insertUserBase.setUsername(username);
            insertUserBase.setPassword(initPhonePwd(phone));
            insertUserBase.setNickname(userBase.getNickname());
            insertUserBase.setCreateTime(new Date());
            userBaseDao.insertSelective(insertUserBase);
            userBase.setUserBaseId(insertUserBase.getId());
            return ;
        }
        userBase.setUserBaseId(authUserBase.getId());
    }


    private void addAuthUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {
        Long bizUserId = baseUserExtend.getBizUserId();
        AuthUser authUser = null;
        if (bizUserId != null){
            authUser = authUserDao.selectByBizUserIdTenantId(systemOriginEnum.getType(), bizUserId, userBase.getTenantId());
        }
        if (authUser == null) {
            AuthUser addUser = new AuthUser();
            addUser.setBizUserId(baseUserExtend.getBizUserId());
            addUser.setUserBaseId(userBase.getUserBaseId());
            if (userBase.getStatus() != null) {
                addUser.setStatus(userBase.getStatus().byteValue());
            }
            addUser.setSystemOrigin(systemOriginEnum.getType());
            addUser.setPassword(getUserPassword(systemOriginEnum, userBase));
            addUser.setUpdateTime(new Date());
            addUser.setCreateTime(new Date());
            addUser.setLastLoginTime(baseUserExtend.getLastLoginTime());
            if (baseUserExtend.getAuditStatus() != null) {
                addUser.setAuditStatus(baseUserExtend.getAuditStatus().byteValue());
            }
            addUser.setTenantId(userBase.getTenantId());
            authUserDao.insertSelective(addUser);
            if (bizUserId == null){
                AuthUser updateAuthUser = new AuthUser();
                updateAuthUser.setId(addUser.getId());
                updateAuthUser.setBizUserId(addUser.getId());
                authUserDao.updateByPrimaryKeySelective(updateAuthUser);
            }
            userBase.setId(addUser.getId());
            return;
        }
        updateAuthUser(userBase,  baseUserExtend, authUser);
        userBase.setId(authUser.getId());
    }

    private String getUserPassword(SystemOriginEnum systemOriginEnum, UserBase userBase){
        String encryptedPassword;
        if (StringUtils.isEmpty(userBase.getPassword())){
            encryptedPassword = initPhonePwd(userBase.getPhone());
        }else {
            encryptedPassword = MD5Util.string2MD5(userBase.getPassword());
        }
        // 如果是COSFO_mall新增用户，该用户手机号可能已经注册过其他门店了。如果注册过，记录保存老密码；未注册，使用传参新密码
        if(SystemOriginEnum.COSFO_MALL == systemOriginEnum){
            if(org.apache.commons.lang3.StringUtils.isNotBlank(userBase.getEncryptedPassword())) {
                log.info("auth新增cosfo-mall userBase{}，没有使用新密码,使用了旧密码", JSON.toJSONString(userBase));
                encryptedPassword = userBase.getEncryptedPassword();
            }
        }
        return encryptedPassword;
    }








    public void  updateAuthUser(UserBase userBase, BaseUserExtend baseUserExtend, AuthUser authUser){
        if (userBase.getUserBaseId()!=null && !authUser.getUserBaseId().equals(userBase.getUserBaseId())) {
            //更新user_base_id
            authUserDao.updateBaseUserIdById(authUser.getId(), userBase.getUserBaseId());
        }
        if (userBase.getStatus()!=null && !Objects.equals(authUser.getStatus().intValue(), userBase.getStatus())) {
            //更新 status
            authUserDao.updateStatusById(authUser.getId(), userBase.getStatus());
        }
        if (baseUserExtend.getLastLoginTime() != null && !Objects.equals(authUser.getLastLoginTime(), baseUserExtend.getLastLoginTime())) {
            //更新最后登陆时间
            authUserDao.updateLastLoginTimeByIdTime(authUser.getId(), baseUserExtend.getLastLoginTime());
        }
        if (baseUserExtend.getAuditStatus() != null && !Objects.equals(authUser.getAuditStatus(), baseUserExtend.getAuditStatus().byteValue())) {
            //更新门店审核状态
            authUserDao.updateAuditStatusById(authUser.getId(), baseUserExtend.getAuditStatus());
        }
    }

    public void addAuthUserAuth(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend){
        Long userId  = userBase.getId();

        if (systemOriginEnum.getType().equals(SystemOriginEnum.MALL.getType())){
            //商城的逻辑open_id,mp_openid可能为空
           // open_id,unionid一定是一对存在的
            String mpOpenid = baseUserExtend.getMpOpenid() == null ? "" : baseUserExtend.getMpOpenid();
            String mpUnionId = baseUserExtend.getMpUnionId() == null ? "" : baseUserExtend.getMpUnionId();
            String openId = baseUserExtend.getOpenid() == null ? "" : baseUserExtend.getOpenid();
            String unionId = baseUserExtend.getUnionId()== null ? "" : baseUserExtend.getUnionId();
            addAuth(userId, AuthTypeEnum.WEI_CHAT, mpOpenid, mpUnionId);
            addAuth(userId, AuthTypeEnum.OFFICIAL_WE_CHAT, openId, unionId);
            return;
        }
        // 小程序openID
        if (!StringUtils.isEmpty(baseUserExtend.getMpOpenid())){
            //查询是否有 open_id的
            addAuth(userId, AuthTypeEnum.WEI_CHAT, baseUserExtend.getMpOpenid(), baseUserExtend.getMpUnionId());
        }
        // 微信公众号
        if (!StringUtils.isEmpty(baseUserExtend.getOpenid())){
            addAuth(userId, AuthTypeEnum.OFFICIAL_WE_CHAT, baseUserExtend.getOpenid(), baseUserExtend.getUnionId());
        }
    }

    public void addAuth(Long userId, AuthTypeEnum authTypeEnum, String openid, String unionId) {
        //判断是否有这个条记录
        authUserAuthDao.deleteByAuthIdType(userId, authTypeEnum.getType());
        AuthUserAuth authUserAuth = new AuthUserAuth();
        authUserAuth.setAuthType(authTypeEnum.getType().byteValue());
        authUserAuth.setUserId(userId);
        authUserAuth.setThirdPartyId(unionId);
        authUserAuth.setAuthId(openid);
        authUserAuth.setCreateTime(new Date());
        authUserAuthDao.insertSelective(authUserAuth);
    }













    private String initPhonePwd(String phone){
        return PWD_PREFIX+ MD5Util.string2MD5(phone);
    }


    public UserBase createSaaSManageUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {
        log.warn("新的创建cosfo-manage、cosfo-oms账号的入口被触发了。systemOriginEnum:{}, userBase：{}, baseUserExtend:{}", systemOriginEnum, JSON.toJSONString(userBase), JSON.toJSONString(baseUserExtend));
        UserBase user = createUser(systemOriginEnum, userBase, false);
        if (user != null && baseUserExtend != null) {
            if (!CollectionUtils.isEmpty(baseUserExtend.getAuthEquityInputs()) && !Objects.equals(userBase.getTenantId(), TENANT_ID)) {
                AuthTenantPrivilegesInput authTenantPrivilegesInput = new AuthTenantPrivilegesInput();
                authTenantPrivilegesInput.setTenantPrivilegesInputs(baseUserExtend.getAuthEquityInputs());
                authTenantPrivilegesInput.setSystemOrigin(systemOriginEnum.getType());
                authTenantPrivilegesInput.setTenantId(userBase.getTenantId());
                authTenantPrivilegesService.addTenantPrivileges(authTenantPrivilegesInput);
            }
        }
        return user;
    }
}
