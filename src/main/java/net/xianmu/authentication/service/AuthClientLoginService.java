package net.xianmu.authentication.service;

import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.input.login.AuthClientLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthClientMallLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;

public interface AuthClientLoginService {
    AuthLoginDto authClientLogin(AuthClientLoginProviderInput authClientLoginProviderInput);

    AuthLoginDto authLogin(AuthClientMallLoginProviderInput authClientMallLoginProviderInput);

    AuthQueryWechatInfoDTO authMallQueryWechatInfo(AuthQueryWechatInfoInput authQueryWechatInfoInput);

    AuthQueryWechatInfoDTO authPopMallQueryWechatInfo(AuthQueryWechatInfoInput authQueryWechatInfoInput);
}
