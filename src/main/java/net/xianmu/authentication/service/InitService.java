package net.xianmu.authentication.service;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.StringUtils;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.utils.RedisKeyUtils;
import net.xianmu.authentication.mapper.auth.AuthMenuPurviewDao;
import net.xianmu.authentication.mapper.auth.AuthRoleDao;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class InitService {
    @Resource
    AuthMenuPurviewDao authMenuPurviewDao;
    @Resource(name = "authRedisTemplate")
    RedisTemplate redisTemplate;
    @Resource
    AuthRoleDao roleDao;

    public void initRedisKey(Integer source) {
        List<AuthRole> authRoles = roleDao.selectRoleIds(source);
        if (CollectionUtils.isEmpty(authRoles)) {
            return;
        }
        //查询角色的
        authRoles.forEach(
                it -> {
                    HashSet<Long> objects = new HashSet<>();
                    objects.add(it.getId());
                    String roleIdStr = it.getId().toString();
                    List<AuthMenuPurview> menus = Objects.equals(it.getSuperAdmin().intValue(), 1) ? authMenuPurviewDao.selectMenus(source) : authMenuPurviewDao.selectByRolIds(source, objects, null);
                    Set<String> collect = menus.stream().map(AuthMenuPurview::getUrl).collect(Collectors.toSet());
                    menus.stream().map(AuthMenuPurview::getSonPurview).collect(Collectors.toSet()).forEach(
                            u -> {
                                if (StringUtils.isNotBlank(u)) {
                                    String[] split = u.split(",");
                                    collect.addAll(Arrays.asList(split));
                                }
                            }
                    );
                    String redisKey = RedisKeyUtils.getRolePurviewKey();
                    log.info("roleID {} urls {}", it.getId(), JSONUtil.toJsonStr(collect));
                    redisTemplate.opsForHash().put(redisKey, roleIdStr, collect);
                }
        );

    }

}
