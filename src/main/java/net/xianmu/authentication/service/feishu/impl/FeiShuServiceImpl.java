package net.xianmu.authentication.service.feishu.impl;

import cn.hutool.http.ContentType;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.SHA1Util;
import net.summerfarm.common.util.URLUtils;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.authentication.common.util.SpringContextUtil;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.model.DTO.AuthUserAuthThirdPartyDTO;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.authentication.model.resp.FeiShuTicketResp;
import net.xianmu.authentication.service.AuthUserAuthService;
import net.xianmu.authentication.service.AuthUserBaseService;
import net.xianmu.authentication.service.feishu.FeiShuService;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.enums.base.feishu.FeiShuTokenCacheKey;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.CommonResult;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.security.NoSuchAlgorithmException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 维护飞书token
 *
 * <AUTHOR>
 * @date 2023/7/10  15:53
 */
@Slf4j
@Component
public class FeiShuServiceImpl implements FeiShuService {

    @NacosValue(value = "${feishu.appId}")
    private String appId;
    @NacosValue(value = "${feishu.appSecret}")
    private String appSecret;

    @NacosValue(value = "${cosfo.feishu.appId}", autoRefreshed = true)
    private String cosfoAppId;
    @NacosValue(value = "${cosfo.feishu.appSecret}", autoRefreshed = true)
    private String cosfoAppSecret;

    @NacosValue(value = "${cosfologin.feishu.appId:cli_a56fd191fc78100e}", autoRefreshed = true)
    private String cosfoBoosLoginAppId;
    @NacosValue(value = "${cosfologin.feishu.appSecret:vxv66zAvqeZzSLGjGbigy17l6PqdA2t3}", autoRefreshed = true)
    private String cosfoBoosLoginAppSecret;

    @Resource(name = "redisTemplate")
    private RedisTemplate redisTemplate;
    @Resource
    private AuthUserAuthService authUserAuthService;
    @Resource
    private AuthUserBaseService authUserBaseService;
    @Resource
    private AuthUserAuthDao authUserAuthDao;
    @Resource
    private AuthUserDao authUserDao;

    public final static String BOSS_LOGIN = "boss_login";

    private final static String TENANT_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal";
    private final static String APP_TOKEN_URL = "https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal";
    private final static String JSAPI_TICKET_URL = "https://open.feishu.cn/open-apis/jssdk/ticket/get";
    private final static String USER_ID_URL = "https://open.feishu.cn/open-apis/contact/v3/users/batch_get_id?user_id_type=user_id";
    private final static String GET_FEISHU_USER_BY_ID_URL = "https://open.feishu.cn/open-apis/contact/v3/users/";
    private final static String CODE_LOGIN_URL = "https://open.feishu.cn/open-apis/authen/v1/access_token";

    private final static String USER_TOKEN_URL = "https://open.feishu.cn/open-apis/authen/v1/oidc/access_token";

    private final static String USER_INFO_URL = "https://open.feishu.cn/open-apis/authen/v1/user_info";


    @Override
    public void accessTokenTask() {
        log.info("飞书token更新任务开始执行>>>");

        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("app_id", appId);
        paramMap.put("app_secret", appSecret);
        log.info("appid:{},secret:{}", appId, appSecret);

        log.info("cosfo appid:{},secret:{}", cosfoAppId, cosfoAppSecret);
        Map<String, Object> cosfoParamMap = new HashMap<>();
        cosfoParamMap.put("app_id", cosfoAppId);
        cosfoParamMap.put("app_secret", cosfoAppSecret);

        log.info("cosfoLogin appid:{},secret:{}", cosfoBoosLoginAppId, cosfoBoosLoginAppSecret);
        Map<String, Object> cosfoLoginParamMap = new HashMap<>();
        cosfoLoginParamMap.put("app_id", cosfoBoosLoginAppId);
        cosfoLoginParamMap.put("app_secret", cosfoBoosLoginAppSecret);
        //更新tenant access token
        updateToken(FeiShuTokenCacheKey.TENANT_ACCESS_TOKEN, () -> {
            String resStr = HttpUtil.post(TENANT_TOKEN_URL, paramMap);
            JSONObject resJson = JSONObject.parseObject(resStr);
            if (!Objects.equals(resJson.getInteger("code"), 0)) {
                log.error("飞书tenantAccessToken更新失败，响应：{}, app:{}", resJson, paramMap, new RuntimeException("token update error"));
                return null;
            }

            //存储token
            Integer expire = resJson.getInteger("expire");
            String tenantAccessToken = resJson.getString("tenant_access_token");
            log.info("飞书tenantAccessToken更新成功，响应：{}, app:{}", resJson, paramMap);
            return new FeiShuToken(expire, tenantAccessToken);
        });

        //更新cosfo tenant_access_token
        updateToken(getFeiShuRedisKey(SystemOriginEnum.COSFO_OMS, FeiShuTokenCacheKey.TENANT_ACCESS_TOKEN), () -> {
            String resStr = HttpUtil.post(TENANT_TOKEN_URL, cosfoParamMap);
            JSONObject resJson = JSONObject.parseObject(resStr);
            if (!Objects.equals(resJson.getInteger("code"), 0)) {
                log.error("飞书appAccessToken更新失败，响应：{}", resJson, new RuntimeException("token update error"));
                return null;
            }

            //存储token
            Integer expire = resJson.getInteger("expire");
            String appAccessToken = resJson.getString("tenant_access_token");
            return new FeiShuToken(expire, appAccessToken);
        });

        //更新xianmu app accecc token
        updateToken(FeiShuTokenCacheKey.APP_ACCESS_TOKEN, () -> {
            String resStr = HttpUtil.post(APP_TOKEN_URL, paramMap);
            JSONObject resJson = JSONObject.parseObject(resStr);
            if (!Objects.equals(resJson.getInteger("code"), 0)) {
                log.error("飞书appAccessToken更新失败，响应：{}", resJson, new RuntimeException("token update error"));
                return null;
            }

            //存储token
            Integer expire = resJson.getInteger("expire");
            String appAccessToken = resJson.getString("app_access_token");
            return new FeiShuToken(expire, appAccessToken);
        });
        //更新cosfo app accecc token
        updateToken(getFeiShuRedisKey(SystemOriginEnum.COSFO_OMS, FeiShuTokenCacheKey.APP_ACCESS_TOKEN), () -> {
            String resStr = HttpUtil.post(APP_TOKEN_URL, cosfoParamMap);
            JSONObject resJson = JSONObject.parseObject(resStr);
            if (!Objects.equals(resJson.getInteger("code"), 0)) {
                log.error("飞书appAccessToken更新失败，响应：{}", resJson, new RuntimeException("token update error"));
                return null;
            }

            //存储token
            Integer expire = resJson.getInteger("expire");
            String appAccessToken = resJson.getString("app_access_token");
            return new FeiShuToken(expire, appAccessToken);
        });

        //更新 xianm tickets
        updateToken(FeiShuTokenCacheKey.JSAPI_TICKET, () -> {
            String tenantAccessToken = (String) redisTemplate.opsForValue().get(FeiShuTokenCacheKey.TENANT_ACCESS_TOKEN);
            HttpResponse response = HttpRequest.post(JSAPI_TICKET_URL)
                    .header("Authorization", "Bearer " + tenantAccessToken)
                    .header("Content-Type", ContentType.JSON.getValue())
                    .execute();
            String res = response.body();
            log.info("飞书查询jsapi_ticket结果，响应：{}", res);

            JSONObject jsonRes = JSONObject.parseObject(res);
            if (!Objects.equals(0, jsonRes.getInteger("code"))) {
                log.error("飞书jsapi ticket更新失败，响应：{}", jsonRes, new RuntimeException("jsapi ticket update error"));
                return null;
            }

            Integer expire = jsonRes.getJSONObject("data").getInteger("expire_in");
            String jsapiTicket = jsonRes.getJSONObject("data").getString("ticket");
            return new FeiShuToken(expire, jsapiTicket);
        });

        //更新 cosfo tickets
        updateToken(getFeiShuRedisKey(SystemOriginEnum.COSFO_OMS, FeiShuTokenCacheKey.JSAPI_TICKET), () -> {
            String tenantAccessToken = (String) redisTemplate.opsForValue().get(getFeiShuRedisKey(SystemOriginEnum.COSFO_OMS, FeiShuTokenCacheKey.TENANT_ACCESS_TOKEN));
            HttpResponse response = HttpRequest.post(JSAPI_TICKET_URL)
                    .header("Authorization", "Bearer " + tenantAccessToken)
                    .header("Content-Type", ContentType.JSON.getValue())
                    .execute();
            String res = response.body();
            log.info("飞书查询jsapi_ticket结果，响应：{}", res);

            JSONObject jsonRes = JSONObject.parseObject(res);
            if (!Objects.equals(0, jsonRes.getInteger("code"))) {
                log.error("飞书jsapi ticket更新失败，响应：{}", jsonRes, new RuntimeException("jsapi ticket update error"));
                return null;
            }

            Integer expire = jsonRes.getJSONObject("data").getInteger("expire_in");
            String jsapiTicket = jsonRes.getJSONObject("data").getString("ticket");
            return new FeiShuToken(expire, jsapiTicket);
        });

        //更新bossLogin app \token
        updateToken(getFeiShuRedisKey(SystemOriginEnum.COSFO_OMS, FeiShuTokenCacheKey.APP_ACCESS_TOKEN, BOSS_LOGIN), () -> {
            String resStr = HttpUtil.post(APP_TOKEN_URL, cosfoLoginParamMap);
            JSONObject resJson = JSONObject.parseObject(resStr);
            if (!Objects.equals(resJson.getInteger("code"), 0)) {
                log.error("飞书appAccessToken更新失败，响应：{}", resJson, new RuntimeException("token update error"));
                return null;
            }

            //存储token
            Integer expire = resJson.getInteger("expire");
            String appAccessToken = resJson.getString("app_access_token");
            return new FeiShuToken(expire, appAccessToken);
        });

        log.info("<<<飞书token更新任务执行完成");
    }

    @Override
    public void updateFeiShuUserIdTask() {
        log.info("同步飞书userId任务开始>>>");
        initFeiShuUserId(SystemOriginEnum.ADMIN);
        initFeiShuUserId(SystemOriginEnum.CRM);
        initFeiShuUserId(SystemOriginEnum.COSFO_OMS);
        initFeiShuUserId(SystemOriginEnum.COSFO_MANAGE);
        log.info("<<<同步飞书userId任务结束");
    }

    private void initFeiShuUserId(net.xianmu.common.enums.base.auth.SystemOriginEnum originEnum) {
        int pageIndex = 0;
        int pageSize = 50;
        while (true) {
            List<AuthUserPhoneDTO> authUserBases = authUserBaseService.selectAuthUserBaseWithOrigin(originEnum, pageIndex, pageSize);

            List<AuthUserPhoneDTO> userBaseList = authUserBases.stream()
                    .filter(el -> {
                        String phone = el.getPhone();
                        if (phone.startsWith("_")) {
                            return false;
                        }

                        //处理手机号
                        if (phone.contains("_")) {
                            String[] spl = phone.split("_");
                            el.setPhone(spl[0]);
                        }
                        return true;
                    }).collect(Collectors.toList());

            SpringContextUtil.getApplicationContext().getBean(FeiShuService.class).batchUpdateUserId(userBaseList);

            if (authUserBases.size() < pageSize) {
                break;
            }
            pageIndex++;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<AuthUserAuthThirdPartyDTO> batchUpdateUserId(List<AuthUserPhoneDTO> authUserBaseList) {
        if (CollectionUtils.isEmpty(authUserBaseList)) {
            return null;
        }

        //调用飞书接口查询
        List<String> phoneList = authUserBaseList.stream().map(AuthUserPhoneDTO::getPhone).collect(Collectors.toList());
        String tenantAccessToken = (String) redisTemplate.opsForValue().get(FeiShuTokenCacheKey.TENANT_ACCESS_TOKEN);
        HttpResponse response = HttpRequest.post(USER_ID_URL)
                .header("Authorization", "Bearer " + tenantAccessToken)
                .header("Content-Type", ContentType.JSON.getValue())
                .body(JSONObject.toJSONString(Collections.singletonMap("mobiles", phoneList)))
                .execute();
        String res = response.body();
        log.info("飞书用户信息查询结果，参数：{}，响应：{}", phoneList, res);

        //更新三方用户信息
        Map<String, String> resMap = new HashMap<>();
        JSONObject jsonRes = JSONObject.parseObject(res);
        if (Objects.equals(0, jsonRes.getInteger("code"))) {
            JSONObject jsonData = jsonRes.getJSONObject("data");
            JSONArray jsonArray = jsonData.getJSONArray("user_list");
            for (Object o : jsonArray) {
                JSONObject el = (JSONObject) o;
                String userId = el.getString("user_id");
                if (StringUtils.isEmpty(userId)) {
                    continue;
                }

                String mobile = el.getString("mobile");
                resMap.put(mobile, userId);
            }
        }

        List<AuthUserAuthThirdPartyDTO> authList = new ArrayList<>();
        for (AuthUserPhoneDTO phoneDTO : authUserBaseList) {
            String thirdPartyId = resMap.get(phoneDTO.getPhone());
            if (!StringUtils.isEmpty(thirdPartyId)) {
                AuthUserAuthThirdPartyDTO dto = new AuthUserAuthThirdPartyDTO();
                dto.setBaseUserId(phoneDTO.getUserBaseId());
                dto.setUserId(phoneDTO.getId());
                dto.setAuthType(AuthTypeEnum.FEI_SHU.getType().byteValue());
                dto.setThirdPartyId(thirdPartyId);
                authList.add(dto);
            }
        }

        //更新数据库
        authUserAuthService.batchUpsertUserAuth(authList);

        return authList;
    }

    @Override
    public CommonResult<FeiShuTicketResp> getFeiShuSignature(String url) {
        return getFeiShuSignature(null, url);
    }

    @Override
    public CommonResult<FeiShuTicketResp> getFeiShuSignature(SystemOriginEnum systemOriginEnum, String url) {
        String resultAppId = systemOriginEnum == null ? appId : cosfoAppId;
        String ticket = (String) redisTemplate.opsForValue().get(getFeiShuRedisKey(systemOriginEnum, FeiShuTokenCacheKey.JSAPI_TICKET));
        String noncestr = UUID.randomUUID().toString().replace("-", "").substring(0, 16);
        url = URLUtils.decode(url, "UTF-8");
        Long timestamp = System.currentTimeMillis();
        log.info("getFeiShuSignature url {} resultAppId {} " +
                "ticket {} noncestr {} timestamp {}", url, resultAppId, ticket, noncestr, timestamp
        );

        String signStr = "jsapi_ticket=" + ticket
                + "&noncestr=" + noncestr
                + "&timestamp=" + timestamp
                + "&url=" + url;
        try {
            String sign = SHA1Util.encrypt(signStr);
            log.info("getFeiShuSignature sign {}", sign);
            FeiShuTicketResp resp = new FeiShuTicketResp();
            resp.setSignature(sign);
            resp.setTimestamp(timestamp);
            resp.setNoncestr(noncestr);
            resp.setUrl(url);
            resp.setAppId(resultAppId);
            return CommonResult.ok(resp);

        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param unionId unionId, 飞书用户唯一标识, 每个用户在每个飞书租户下唯一
     * @return
     */
    @Override
    public String queryUserIdByUnionId(String unionId) {
        FeiShuUserInfo feiShuUserInfo = getFeishuUserInfoByUnionId(unionId);
        if (feiShuUserInfo == null) {
            return null;
        }
        return feiShuUserInfo.getUserId();
    }

    @Override
    public String queryUserIdByCode(SystemOriginEnum systemOriginEnum, String code, String channel) {
        String feiShuRedisKey = getFeiShuRedisKey(systemOriginEnum, FeiShuTokenCacheKey.APP_ACCESS_TOKEN, channel);
        if (!redisTemplate.hasKey(feiShuRedisKey)) {
            log.error("飞书app_access_token不存在", new RuntimeException("token error"));
            return null;
        }

        String appAccessToken = (String) redisTemplate.opsForValue().get(feiShuRedisKey);
        FeiShuUserInfo feishuUserinfo = getFeiShuUserinfo(appAccessToken, code, channel);
        if (feishuUserinfo == null) {
            throw new BizException("飞书授权失败稍后重试");
        }
        String userId = feishuUserinfo.getUserId();
        String phone = feishuUserinfo.getPhone();

        //未入库的userId存到数据库
        List<AuthUserAuth> auths = authUserAuthDao.selectByThirdIdType(AuthTypeEnum.FEI_SHU.getType(), userId);
        if (!CollectionUtils.isEmpty(auths)) {
            //过滤下是自己域的
            List<Long> userIds = auths.stream().map(AuthUserAuth::getUserId).distinct().collect(Collectors.toList());
            List<Long> authUserIds
                    = authUserDao.selectByUserIds(userIds).stream()
                    .filter(it -> Objects.equals(it.getSystemOrigin(), systemOriginEnum.type)).map(AuthUser::getId).collect(Collectors.toList());
            auths = auths.stream().filter(it -> authUserIds.contains(it.getUserId())).collect(Collectors.toList());
        }

        if (CollectionUtils.isEmpty(auths)) {
            log.info("获取到为空 开始查询绑定信息  phone:{}", phone);
            //根据手机号和租户id+来源查询 auth_id
            //List<AuthUserBase> baseList = authUserBaseService.batchQueryUserBaseByPhone(Collections.singletonList(phone));
            Long tenantId = AuthGlobal.TENANT_ID;
            if (SystemOriginEnum.COSFO_OMS.equals(systemOriginEnum)) {
                tenantId = 0L;
            }
            List<AuthUser> authUsers = authUserDao.selectBySourceTenantIdPhone(systemOriginEnum.type, tenantId, phone);// NOSONAR
            if (CollectionUtils.isEmpty(authUsers)) {
                log.error("登录失败，飞书手机和系统手机号不一致");
                return null;
            }
            AuthUserAuthThirdPartyDTO dto = new AuthUserAuthThirdPartyDTO();
            dto.setUserId(authUsers.get(0).getId());
            dto.setAuthType(AuthTypeEnum.FEI_SHU.getType().byteValue());
            dto.setThirdPartyId(userId);
            authUserAuthService.batchUpsertUserAuth(Collections.singletonList(dto));
        }

        return userId;
    }

    private FeiShuUserInfo getFeishuUserInfoByUnionId(String unionId) {
        // curl -i -X GET 'https://open.feishu.cn/open-apis/contact/v3/users/on_532d9932e4795b5ad416d24917d6698b?department_id_type=open_department_id&user_id_type=union_id' \
        //-H 'Authorization: Bearer u-fO9ltYgeFfvr4qsHF0gqocl0m1VN4gaNpW20l4008Jce'
        String feiShuRedisKey = getFeiShuRedisKey(SystemOriginEnum.ADMIN, FeiShuTokenCacheKey.APP_ACCESS_TOKEN, null);
        String appAccessToken = (String) redisTemplate.opsForValue().get(feiShuRedisKey);
        if (StringUtils.isEmpty(appAccessToken)) {
            log.error("飞书app_access_token不存在", new RuntimeException("token error"));
            return null;
        }

        String url = GET_FEISHU_USER_BY_ID_URL + unionId + "?department_id_type=open_department_id&user_id_type=union_id";

        log.info("飞书用户信息查询，参数：{}, appAccessToken: {}, url: {}", unionId, appAccessToken, url);
        HttpResponse response = HttpRequest.get(url)
                .header("Authorization", "Bearer " + appAccessToken)
                .body(JSONObject.toJSONString(Collections.singletonMap("union_id", unionId)))
                .execute();
        String res = response.body();
        log.info("飞书用户信息查询结果，参数：{}，响应：{}", unionId, res);

        JSONObject jsonRes = JSONObject.parseObject(res);
        if (!Objects.equals(0, jsonRes.getInteger("code"))) {
            log.error("飞书app_access_token请求异常, appAccessToken: {}", appAccessToken, new RuntimeException("token error"));
            return null;
        }
        JSONObject dataJson = jsonRes.getJSONObject("data").getJSONObject("user");
        String userId = dataJson.getString("user_id");
        String phone = dataJson.getString("mobile");
        if (!StringUtils.isEmpty(phone)) {
            phone = phone.replace("+86", "");
        }
        FeiShuUserInfo feiShuUserInfo = new FeiShuUserInfo();
        feiShuUserInfo.setPhone(phone);
        feiShuUserInfo.setUserId(userId);
        return feiShuUserInfo;
    }

    private FeiShuUserInfo getFeiShuUserinfo(String appAccessToken, String code, String channel) {
        FeiShuUserInfo feiShuUserInfo = new FeiShuUserInfo();
        JSONObject dataJson;
        if (StringUtils.isEmpty(channel)) {
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("grant_type", "authorization_code");
            paramMap.put("code", code);
            HttpResponse response = HttpRequest.post(CODE_LOGIN_URL)
                    .header("Authorization", "Bearer " + appAccessToken)
                    .header("Content-Type", ContentType.JSON.getValue())
                    .body(JSONObject.toJSONString(paramMap))
                    .execute();
            String res = response.body();
            log.info("飞书用户信息查询结果，参数：{}，响应：{}", paramMap, res);

            JSONObject jsonRes = JSONObject.parseObject(res);
            if (!Objects.equals(0, jsonRes.getInteger("code"))) {
                log.error("飞书app_access_token请求异常", new RuntimeException("token error"));
                return null;
            }
            dataJson = jsonRes.getJSONObject("data");
        } else {
            // 拿code换user_token
            Map<String, String> paramMap = new HashMap<>();
            paramMap.put("code", code);
            paramMap.put("grant_type", "authorization_code");
            HttpResponse response = HttpRequest.post(USER_TOKEN_URL)
                    .header("Authorization", "Bearer " + appAccessToken)
                    .header("Content-Type", ContentType.JSON.getValue())
                    .body(JSONObject.toJSONString(paramMap))
                    .execute();
            String res = response.body();
            log.info("拿code换user_token，参数：{}，响应：{}", paramMap, res);
            JSONObject jsonRes = JSONObject.parseObject(res);
            if (!Objects.equals(0, jsonRes.getInteger("code"))) {
                log.error("飞书 user_access_token请求异常", new BizException("user_access_token请求异常"));
                return null;
            }
            String accesToken = jsonRes.getJSONObject("data").getString("access_token");

            HttpResponse userInfoResponse = HttpRequest.get(USER_INFO_URL)
                    .header("Authorization", "Bearer " + accesToken)
                    .body(JSONObject.toJSONString(paramMap))
                    .execute();
            String userInfo = userInfoResponse.body();
            JSONObject userInfoJsonRes = JSONObject.parseObject(userInfo);
            dataJson = userInfoJsonRes.getJSONObject("data");
        }
        String userId = dataJson.getString("user_id");
        String phone = dataJson.getString("mobile");
        if (!StringUtils.isEmpty(phone)) {
            phone = phone.replace("+86", "");
        }
        feiShuUserInfo.setPhone(phone);
        feiShuUserInfo.setUserId(userId);
        return feiShuUserInfo;
    }


    @Override
    public CommonResult<FeiShuTicketResp> getFeiShuConfig(SystemOriginEnum systemOriginByType, String channel) {
        FeiShuTicketResp resp = new FeiShuTicketResp();
        resp.setAppId(cosfoBoosLoginAppId);
        resp.setAppSecret(cosfoBoosLoginAppSecret);
        return CommonResult.ok(resp);
    }

    private void updateToken(String redisKey, Supplier<FeiShuToken> getToken) {
        //判断redis key过期时间、30分钟以上不调用api
        long expire = redisTemplate.getExpire(redisKey, TimeUnit.SECONDS);
        if (expire > 30 * 60L) {
            log.info("redis key:{} 过期时间:{}秒, 大于30分钟, 不调用api", redisKey, expire);
            return;
        }

        //调用api查询新token并更新
        FeiShuToken feiShuToken = getToken.get();
        if (feiShuToken == null) {
            return;
        }
        redisTemplate.opsForValue().set(redisKey, feiShuToken.getToken(), feiShuToken.getExpire() - 30, TimeUnit.SECONDS);
    }

    @Data
    @AllArgsConstructor
    private class FeiShuToken {

        /**
         * token过期时间
         */
        private Integer expire;

        /**
         * token
         */
        private String token;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    private class FeiShuUserInfo {

        /**
         * phone
         */
        private String phone;

        /**
         * token
         */
        private String userId;
    }


    private String getFeiShuRedisKey(SystemOriginEnum systemOriginEnum, String key) {
        return getFeiShuRedisKey(systemOriginEnum, key, null);
    }

    private String getFeiShuRedisKey(SystemOriginEnum systemOriginEnum, String key, String channel) {
        if (systemOriginEnum == null) {
            return key;
        }
        if (Objects.equals(SystemOriginEnum.ADMIN.type, systemOriginEnum.type)) {
            return key;
        }
        String result = systemOriginEnum.name + ":" + key;
        if (!StringUtils.isEmpty(result)) {
            result = result + ":" + channel;
        }
        return result;
    }
}
