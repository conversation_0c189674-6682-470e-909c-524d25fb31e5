package net.xianmu.authentication.service.feishu;

import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.model.DTO.AuthUserAuthThirdPartyDTO;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.authentication.model.resp.FeiShuTicketResp;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.CommonResult;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/17  13:39
 */
public interface FeiShuService {
    /**
     * 更新飞书tenant_access_toke、app_access_token
     */
    void accessTokenTask();

    /**
     * 更新飞书userId
     */
    void updateFeiShuUserIdTask();

    /**
     * 批量更新用户飞书userId
     * @param authUserBaseList 用户信息
     * @return List<AuthUserAuthThirdPartyDTO>
     */
    List<AuthUserAuthThirdPartyDTO> batchUpdateUserId(List<AuthUserPhoneDTO> authUserBaseList);

    /**
     * 查询飞书签名
     * @param url 前端访问页面
     * @return token
     */
    CommonResult<FeiShuTicketResp> getFeiShuSignature(String url);


    CommonResult<FeiShuTicketResp> getFeiShuSignature(SystemOriginEnum systemOriginEnum, String url);


    String queryUserIdByUnionId(String unionId);

    /**
     * 飞书工作台应用登录
     * @param code code
     * @return userId
     */
    String queryUserIdByCode(SystemOriginEnum systemOriginEnum, String code,String channel);



    CommonResult<FeiShuTicketResp> getFeiShuConfig(SystemOriginEnum systemOriginByType, String channel);
}
