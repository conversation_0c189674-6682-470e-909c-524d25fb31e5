package net.xianmu.authentication.service;

import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.model.entity.AuthTenantPrivileges;

import java.util.List;

public interface AuthTenantPrivilegesService {
    /**
     * 添加租户权限
     *
     * @param authTenantPrivilegesInput 租户权限输入参数
     * @return 添加成功返回true，否则返回false
     */
    Boolean addTenantPrivileges(AuthTenantPrivilegesInput authTenantPrivilegesInput);

    /**
     * 根据租户ID选择权限列表
     *
     * @param tenantId 租户ID
     * @return 权限列表
     */
    List<AuthTenantPrivileges> selectByTenantId(Long tenantId);


    /**
     * 合并租户权限过期时间
     *
     * @param tenantId 租户ID
     * @param outs     权限列表
     * @return 合并后的权限列表
     */
    List<AuthMenuPurview> mergeTenantPurviewExpireTime(SystemOriginEnum systemOriginEnum, Long tenantId, List<AuthMenuPurview> outs);
}

