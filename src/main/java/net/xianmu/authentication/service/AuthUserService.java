package net.xianmu.authentication.service;


import com.github.pagehelper.PageInfo;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.authentication.model.VO.AuthUserVo;
import net.xianmu.authentication.model.input.AuthUserPasswordCommandInput;
import net.xianmu.authentication.model.input.AuthUserQueryInput;
import net.xianmu.common.user.UserBase;
import net.xianmu.common.result.DubboResponse;

import java.util.List;

public interface AuthUserService {
    /**
     * @param systemOriginEnum 创建用户
     * @param baseUser         用户基础 baseUserId为空会生成一个负的业务id 业务id生成后修改
     */
    UserBase createUser(SystemOriginEnum systemOriginEnum, UserBase baseUser);

    /**
     * @param systemOriginEnum 修改用户
     * @param baseUser         用户基础 baseUserId为空会生成一个负的业务id 业务id生成后修改
     */
    UserBase updateUser(SystemOriginEnum systemOriginEnum, UserBase baseUser);

    Boolean checkPhonePassword(SystemOriginEnum systemOriginEnum, String phone, String pwd);

    Boolean checkUserNamePassword(SystemOriginEnum systemOriginEnum, String phone, String pwd);

    Boolean updatePasswordByPhone(SystemOriginEnum systemOriginEnum, String phone, String password);

    UserBase queryUserBase(SystemOriginEnum systemOriginEnum, String userName, Long tenantId);

    /**
     * binlog创建用户
     * @param systemOriginEnum 系统来源
     * @param baseUser   baseUser
     * @return
     */
    DubboResponse<UserBase> binlogCreateUser(SystemOriginEnum systemOriginEnum, UserBase baseUser);

    /**
     * 查询auth user主键，不存在就创建
     * @param authUser authUser
     * @return 主键
     */
    Long selectAuthUserIdIWithInsert(AuthUser authUser);

    /**
     *
     * @param systemOriginEnum 来源
     * @param userBase  用户信息
     * @param baseUserExtend 扩展信息
     * @return
     */
    UserBase createUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend);


    PageInfo<AuthUserVo> selectAuthUserBySourceRoleIds(AuthUserQueryInput input);

    String resetUserPwd(AuthUserPasswordCommandInput input);

    void updateAuthUserPasswordByOrigin(AuthUserPasswordCommandInput input);
}
