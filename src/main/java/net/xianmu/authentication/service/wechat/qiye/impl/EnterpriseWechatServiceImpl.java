package net.xianmu.authentication.service.wechat.qiye.impl;

import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.schedulerx.common.util.JsonUtil;
import com.github.rholder.retry.Retryer;
import com.github.rholder.retry.RetryerBuilder;
import com.github.rholder.retry.StopStrategies;
import com.github.rholder.retry.WaitStrategies;
import lombok.Data;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.EnterpriseWeChatTokenTypeEnum;
import net.xianmu.authentication.common.util.RedisLockUtils;
import net.xianmu.authentication.common.util.SpringContextUtil;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.xianmu.authentication.model.entity.EnterpriseWechatUserRelation;
import net.xianmu.authentication.mapper.offline.EnterpriseWechatUserRelationDao;
import net.xianmu.authentication.service.AuthUserBaseService;
import net.xianmu.authentication.service.wechat.qiye.EnterpriseWechatService;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.*;

@Service
@Slf4j
public class EnterpriseWechatServiceImpl implements EnterpriseWechatService {
    /**
     * 企业微信url
     */
    private static final String TOKEN_URL = "https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid=%s&corpsecret=%s";

    private static final String USER_ID_URL = "https://qyapi.weixin.qq.com/cgi-bin/user/getuserid?access_token=%s";
    /**
     * 通讯录的 企业id
     */
    private static final String CORPID = "wwebcb5c28d5bf3bad";
    /**
     * 通讯录的密钥
     */
    private static final String TXL_CORPSECRET = "S88ehQli8JzdutJZdGXRUO0Z6SBGTvaiPWPjMMGYGCc";

    private static final String NO_USER = "46004_user no exist";

    /**
     * 客户关系的密钥
     */
    private static final String CUASTOMER_CORPSECRET = "VXtk8FKqBrp8XleFLI-sDe_jXPHRuKRoGkw2tJpT3gc";
    @Resource(name = "redisTemplate")
    private RedisTemplate redisTemplate;
    @Resource
    RedisLockUtils redisLockUtils;
    @Resource
    private AuthUserBaseService authUserBaseService;
    @Resource
    private AuthUserAuthDao authUserAuthDao;
    @Resource
    EnterpriseWechatUserRelationDao enterpriseWechatUserRelationDao;
    @Resource
    AuthUserPropertiesExtDao authUserPropertiesExtDao;
    private static Map<String, String> corpsecretMap = new HashMap<>();
    public  static Retryer<String> retarder;


    @PostConstruct
    public void init() {
        corpsecretMap.put(EnterpriseWeChatTokenTypeEnum.TXL_ACCESS_TOKEN.name(), TXL_CORPSECRET);
        corpsecretMap.put(EnterpriseWeChatTokenTypeEnum.CUSTOMER_ACCESS_TOKEN.name(), CUASTOMER_CORPSECRET);
         retarder = RetryerBuilder.<String>newBuilder()
                .retryIfResult(Objects::isNull)// 1.1当重试的方法返回null时候进行重试
                .retryIfExceptionOfType(IOException.class)// 1.2当重试方法抛出IOException类型异常时候进行重试
                .withStopStrategy(StopStrategies.stopAfterAttempt(3))// 1.3尝试执行三次（也就是重试2次）
                .withWaitStrategy(WaitStrategies.fixedWait(500, TimeUnit.MILLISECONDS))//1.4重试间隔
                .build();
    }




    @Override
    public void accessTokenTask() {
        log.info("企业微信token更新任务开始执行>>>");
        EnterpriseWeChatTokenTypeEnum[] values = EnterpriseWeChatTokenTypeEnum.values();
        for (EnterpriseWeChatTokenTypeEnum value : values) {
            String txlRedisKey = ENTERPRISE_WECHAT_REDIS + value.name().toLowerCase();
            updateToken(txlRedisKey, () -> {
                String secret = corpsecretMap.get(value.name());
                String url = String.format(TOKEN_URL, CORPID, secret);
                String resp = HttpUtil.get(url);
                if (StringUtils.isEmpty(resp)) {
                    log.error("企业微信token 请求错误 {}", value.name(), new RuntimeException("企业微信token 请求错误"));
                    return null;
                }
                return JSONUtil.toBean(resp, EnterpriseWechatResp.class);
            });
        }

        log.info("企业微信token更新任务执行结束>>>");
    }

    @Override
    public String getAccessToken(EnterpriseWeChatTokenTypeEnum enterpriseWeChatTokenTypeEnum, Boolean update) {
        String key = ENTERPRISE_WECHAT_REDIS + enterpriseWeChatTokenTypeEnum.name().toLowerCase();
        if (update == null || !update) {
            return (String) redisTemplate.opsForValue().get(key);
        }
        updateToken(key, () -> {
            String secret = corpsecretMap.get(enterpriseWeChatTokenTypeEnum.name());
            String url = String.format(TOKEN_URL, CORPID, secret);
            String resp = HttpUtil.get(url);
            if (StringUtils.isEmpty(resp)) {
                log.error("企业微信token 请求错误", new RuntimeException("企业微信token 请求错误"));
                return null;
            }
            return JSONUtil.toBean(resp, EnterpriseWechatResp.class);
        });
        return (String) redisTemplate.opsForValue().get(key);
    }

    @Override
    public void updateUserId() {
        log.info("企业用户userId更新任务开始执行>>>");
        initUserId();
        log.info("企业用户userId更新任务执行结束>>>");
    }

    @Override
    public void batchUpdateUserId(List<EnterpriseWechatUserRelation> authUserBaseList) {
        if (CollectionUtils.isEmpty(authUserBaseList)) {
            return;
        }
        List<AuthUserAuth> list = new ArrayList<>(authUserBaseList.size());
        Map<Long, String> map = new HashMap<>(authUserBaseList.size());
        authUserBaseList.forEach(
                it -> {
                        AuthUserAuth authUserAuth = new AuthUserAuth();
                        authUserAuth.setThirdPartyId(it.getThirdPartyId());
                        authUserAuth.setUserId(it.getAuthId());
                        authUserAuth.setAuthType(AuthTypeEnum.ENTERPRISE_WE_CHAT.getType().byteValue());
                        list.add(authUserAuth);
                        if (!StringUtils.isEmpty(it.getQrCodeUrl())){
                            map.put(authUserAuth.getUserId(),it.getQrCodeUrl());
                        }
                }
        );
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        List<Long> ids = authUserBaseList.stream().map(EnterpriseWechatUserRelation::getAuthId).collect(Collectors.toList());

        dbOperation(ids, list, map);
    }


    @Transactional
    public void dbOperation(List<Long> ids, List<AuthUserAuth> list, Map<Long, String> urlmap) {
        //删除所有的list
        authUserAuthDao.deleteByAuthIdsType(ids, AuthTypeEnum.ENTERPRISE_WE_CHAT.getType());

        //全部插入
        authUserAuthDao.batchAdd(list);

        if (CollectionUtils.isEmpty(urlmap)){
            return;
        }

        authUserPropertiesExtDao.deleteByAllUserIdsKey(urlmap.keySet(), QW_BD_QR);
        List<AuthUserPropertiesExt> authUserPropertiesExts = urlmap.keySet().stream().map(
                it->{
                    AuthUserPropertiesExt authUserPropertiesExt = new AuthUserPropertiesExt();
                    authUserPropertiesExt.setUserId(it);
                    authUserPropertiesExt.setPropValue(urlmap.get(it));
                    authUserPropertiesExt.setPropKey(QW_BD_QR);
                    authUserPropertiesExt.setCreateTime(new Date());
                    authUserPropertiesExt.setUpdateTime(new Date());
                    return authUserPropertiesExt;
                }
        ).collect(Collectors.toList());
        authUserPropertiesExtDao.batchAdd(authUserPropertiesExts);
    }



    @SneakyThrows
    public void updateRedisToken(String redisKey, EnterpriseWechatResp enterpriseWechatResp)  {// 2需要重试的方法
        Callable<String> callable = () -> {
            String redisLockValue = String.valueOf(System.currentTimeMillis() + 500);
            String lockKey = redisKey + "_lock";
            boolean lock = redisLockUtils.getLock(lockKey, redisLockValue);
            if (lock) {
                redisTemplate.opsForValue().set(redisKey, enterpriseWechatResp.getAccess_token(), enterpriseWechatResp.getExpires_in(), TimeUnit.SECONDS);
                return enterpriseWechatResp.access_token;
            }
            return null;
        };
        callable.call();
    }



        //分批查询非大客户的用户信息
    private void initUserId() {
        int pageIndex = 0;
        int pageSize = 100;
        while (true) {
            List<EnterpriseWechatUserRelation> enterpriseWechatUserRelations = enterpriseWechatUserRelationDao.selectByPage(pageIndex * pageSize, pageSize);
            if (!CollectionUtils.isEmpty(enterpriseWechatUserRelations)){
                List<Long> userIds = enterpriseWechatUserRelations.stream().map(EnterpriseWechatUserRelation::getAuthId).collect(Collectors.toList());
                List<AuthUserAuth> authUserAuths = authUserAuthDao.selectByUserIdsType(userIds, AuthTypeEnum.ENTERPRISE_WE_CHAT.getType());
                Set<Long> collect = authUserAuths.stream().map(AuthUserAuth::getUserId).collect(Collectors.toSet());
                List<EnterpriseWechatUserRelation> needBatchAdd = enterpriseWechatUserRelations.stream().filter(it->!collect.contains(it.getAuthId())
                ).collect(Collectors.toList());
                SpringContextUtil.getApplicationContext().getBean(EnterpriseWechatService.class).batchUpdateUserId(needBatchAdd);

            }

            if (enterpriseWechatUserRelations.size() < pageSize) {
                break;
            }
            pageIndex++;
        }
    }



    private void updateToken(String redisKey, Supplier<EnterpriseWechatResp> getToken) {
        //调用api查询新token并更新
        EnterpriseWechatResp wechantResp = getToken.get();
        if (wechantResp == null) {
            return;

        }
        updateRedisToken(redisKey,wechantResp);
    }

    @Data
    private class EnterpriseWechatResp {
        /**
         * token过期时间
         */
        private Integer expires_in;

        /**
         * token
         */
        private String access_token;
        /**
         * code码
         */
        private Integer errcode;
        /**
         * error message
         */
        private String errmsg;

        private String userid;
    }


    public boolean verifyPhone(String mPhone) {
        Pattern p;
        Matcher m;
        boolean b;
        // 以1开头,十一位
        p = Pattern.compile(VERIFY_PHONE);
        m = p.matcher(mPhone);
        b = m.matches();
        return b;
    }

}
