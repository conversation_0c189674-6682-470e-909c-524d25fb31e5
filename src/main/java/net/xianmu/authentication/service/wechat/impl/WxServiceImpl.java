package net.xianmu.authentication.service.wechat.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.dto.wx.WXPhoneResultDTO;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.WeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;
import net.xianmu.authentication.client.input.wechat.WechatCareQrInput;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.authentication.common.config.XianMuWxConfig;
import net.xianmu.authentication.common.util.SpringContextUtil;
import net.xianmu.authentication.common.util.WxUtils;
import net.xianmu.authentication.mapper.auth.AuthWechatRelationMapper;
import net.xianmu.authentication.model.entity.AuthWechatRelation;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.common.enums.base.auth.WechatMiniProgramChannelEnum;
import net.xianmu.common.enums.base.auth.WxOfficialAccountsChannelEnum;
import net.xianmu.common.exception.BizException;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.client.methods.RequestBuilder;
import org.apache.http.entity.StringEntity;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.net.URI;
import java.nio.charset.Charset;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.TENANT_ID;

@Service
@Slf4j
public class WxServiceImpl implements WxService {
    private static final String TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s";

    public static final String JS_API_TICKET_API = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?access_token=%s&type=jsapi";

    /**
     *    .setUri(BASE_URI + "/sns/component/jscode2session")
     *                 .addParameter("appid",appid)
     *                 .addParameter("js_code",js_code)
     *                 .addParameter("grant_type","authorization_code")
     *                 .addParameter("component_appid",component_appid)
     *                 .addParameter("component_access_token",component_access_token)
     */
    protected static final String JSCODE2SESSION_URI = "https://api.weixin.qq.com/sns/component/jscode2session?" +
            "appid=%s&js_code=%s&grant_type=authorization_code&component_appid=%s&component_access_token=%s";

    protected static final String WX_PHONE_URI = "https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token=%s";
    private static final String OPEN_ID_LIST_URL = "https://api.weixin.qq.com/cgi-bin/user/get?access_token=%s";
    private static final String USER_LIST_URL = "https://api.weixin.qq.com/cgi-bin/user/info/batchget?access_token=%s";

    private static final String GET_QR_URL = " https://api.weixin.qq.com/cgi-bin/qrcode/create?access_token=%s";

//    private static final String SRM_APPID = "wxe974267a2af8c927";
//    private static final String SRM_APPSECRET = "5b1d731e26f5d942de354d8d0f719ce2";


    private static final String COSFO_APPID = "wxda8819c87e1e69c8";
    private static final String COSFO_APPSECRET = "f4effe0655d1fbf8b0d45b90234c5ee9";
    /**
     * 微信token
     */
    public static final String MALL_WECHAT_ACCESS_TOKEN="MALL_WECHAT_ACCESS_TOKEN";

    /**
     * 微信小程序token
     */
    public static final String MALL_WECHAT_LITE_ACCESS_TOKEN="MALL_LITE_ACCESS_TOKEN";
    /**
     * 微信公众号ticket
     */
    public static final String MALL_WECHAT_JSAPI_TICKET = "MALL_WECHAT_JSAPI_TICKET";

    private static final String PRE_QR = "https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=%s";
    //https://mp.weixin.qq.com/cgi-bin/showqrcode?ticket=
    @Resource(name = "redisTemplate")
    private RedisTemplate redisTemplate;
    @Resource
    private AuthWechatRelationMapper authWechatRelationMapper;
    @Resource
    XianMuWxConfig xianMuWxConfig;
    @Resource
    private WxUtils wxUtils;
    /**
     * 目标商品 SPU ID（快速验证手机号组件用量）
     */
    private static final String TARGET_SPU_ID = "********";
    /**
     * 微信返回的错误码字段
     */
    private static final String ERR_CODE_KEY = "errcode";
    /**
     * 无效 accessToken 的错误码
     */
    private static final long INVALID_TOKEN_ERR_CODE = 40001L;

    @Override
    public void accessTokenTask() {

        accessTokenTask(WxOfficialAccountsChannelEnum.FTGYL.channelCode, COSFO_APPID, COSFO_APPSECRET);
    }

    @Override
    public void srmAccessTokenTask() {
        accessTokenTask(AuthTypeEnum.OFFICIAL_WE_CHAT, WeChatTokenTypeEnum.SRM_ACCESS_TOKEN, xianMuWxConfig.getSrmWxOaAppId(), xianMuWxConfig.getSrmWxOaSecret ());

    }

    @Override
    public void mallAccessTokenTask() {
        //微信公众号
        accessTokenTask(WxOfficialAccountsChannelEnum.XM_MALL.channelCode, xianMuWxConfig.getXmMallWxOaAppId(), xianMuWxConfig.getXmMallWxOaSecret());
        //微信小程序
        accessTokenTask(WechatMiniProgramChannelEnum.XM_MALL_MP.channelCode, xianMuWxConfig.getXmMallWxAppId(), xianMuWxConfig.getXmMallWxSecret());
        //微信小程序ticket
        updateMpTicket(WxOfficialAccountsChannelEnum.XM_MALL.channelCode , getAccessToken(WxOfficialAccountsChannelEnum.XM_MALL.channelCode));
    }

    @Override
    public void popMallAccessTokenTask() {
        //微信公众号
        accessTokenTask(WxOfficialAccountsChannelEnum.POP_MALL.channelCode, xianMuWxConfig.getPopMallWxOaAppId(), xianMuWxConfig.getPopMallWxOaSecret());
        //微信小程序
        accessTokenTask(WechatMiniProgramChannelEnum.POP_MALL_MP.channelCode, xianMuWxConfig.getPopMallWxAppId(), xianMuWxConfig.getPopMallWxSecret());
        //微信小程序ticket
        updateMpTicket(WxOfficialAccountsChannelEnum.POP_MALL.channelCode , getAccessToken(WxOfficialAccountsChannelEnum.POP_MALL.channelCode));
    }

    @Override
    public void srmTicketUpdateTask() {
        //微信公众号
        updateMpTicket(WxOfficialAccountsChannelEnum.XMSRM.channelCode , getAccessToken(WeChatTokenTypeEnum.SRM_ACCESS_TOKEN, AuthTypeEnum.OFFICIAL_WE_CHAT));
    }

    /**
     * 更新微信access_token
     */
    public void accessTokenTask(String channel, String appid, String appsecret) {
        String redisKey = String.format(AuthGlobal.WECHAT_REDIS, channel.toLowerCase(), channel.toLowerCase());
        updateToken(redisKey, channel, () -> {
            String url = String.format(TOKEN_URL, appid, appsecret);
            String resp = HttpUtil.get(url, 2000);
            if (StringUtils.isEmpty(resp)) {
                log.error(channel.toLowerCase() + "微信token 请求错误", new RuntimeException(channel.toLowerCase()+ "token 请求错误"));
                return null;
            }
            WechatToken wechatToken = JSONUtil.toBean(resp, WechatToken.class);
            if (StringUtils.isEmpty(wechatToken.getAccess_token())) {
                throw new BizException(resp);
            }
            log.info("redisKey{}获取到access_token {}",redisKey,wechatToken.access_token);
            return wechatToken;
        });
    }

    public void updateMpTicket(String channel, String accessToken) {
        String redisKey = String.format(AuthGlobal.WECHAT_TICKET_REDIS, channel.toLowerCase());
        updateToken(redisKey, channel, () -> {
            String url = String.format(JS_API_TICKET_API, accessToken);
            String resp = HttpUtil.get(url, 2000);
            if (StringUtils.isEmpty(resp)) {
                log.error(channel.toLowerCase() + "微信ticket 请求错误", new RuntimeException(channel.toLowerCase()+ "微信ticket 请求错误"));
                return null;
            }
            WechatToken wechatToken = JSONUtil.toBean(resp, WechatToken.class);
            if (StringUtils.isEmpty(wechatToken.getTicket())) {
                throw new BizException(resp);
            }
            log.info("redisKey{} ticket {}",redisKey,wechatToken.getTicket());
            return wechatToken;
        });
    }


    /**
     * 更新微信access_token
     */
    public void accessTokenTask(AuthTypeEnum authTypeEnum, WeChatTokenTypeEnum weChatTokenTypeEnum, String appid, String appsecret) {
        String redisKey = String.format(AuthGlobal.WECHAT_REDIS, authTypeEnum.getType(), weChatTokenTypeEnum.name().toLowerCase());
        updateToken(redisKey, weChatTokenTypeEnum.name(), () -> {
            String url = String.format(TOKEN_URL, appid, appsecret);
            String resp = HttpUtil.get(url, 2000);
            if (StringUtils.isEmpty(resp)) {
                log.error(weChatTokenTypeEnum.name() + "微信token 请求错误", new RuntimeException(weChatTokenTypeEnum.name() + "token 请求错误"));
                return null;
            }
            WechatToken wechatToken = JSONUtil.toBean(resp, WechatToken.class);
            if (StringUtils.isEmpty(wechatToken.getAccess_token())) {
                throw new BizException(resp);
            }
            log.info("redisKey{}获取到access_token {}",redisKey,wechatToken.access_token);
            return wechatToken;
        });
    }

    private void updateToken(String redisKey, String channel, Supplier<WechatToken> getToken) {
        // 如果失活的话自动刷新accessToken
        boolean activated = isAccessTokenInactivated(redisKey, channel);
        if (!activated) {
            log.error("channelCode:{}的accessToken已经失活，即将重刷", channel, new BizException("accessToken已经失活，即将重刷，请关注"));
        }

        //没失活的情况下 判断redis key过期时间、30分钟以上不调用api
        long expire = redisTemplate.getExpire(redisKey, TimeUnit.SECONDS);
        if (activated && expire > 30 * 60L) {
            log.info("redisKey{}未过期, expire:{}s",redisKey, expire);
            return;
        }
        //调用api查询新token并更新
        WechatToken token = getToken.get();
        if (token == null) {
            log.error("微信ticket/token 请求错误", new RuntimeException("微信ticket/token 请求错误"));
            return;
        }
        if (StringUtils.isEmpty(token.getTicket()) && StringUtils.isEmpty(token.getAccess_token())){
            log.error("ticket/token 请求错误", new RuntimeException("ticket/token 请求错误"));
            return;
        }
        String value = StringUtils.isEmpty(token.getAccess_token()) ? token.getTicket() :token.getAccess_token();
        redisTemplate.opsForValue().set(redisKey, value, token.getExpires_in() - 30, TimeUnit.SECONDS);
        if (!activated) {
            log.error("channelCode:{}的accessToken失活重刷成功", channel, new BizException("accessToken失活重刷成功"));
        }
    }

    private boolean isAccessTokenInactivated(String redisKey, String channelCode) {
        // 只针对鲜沐商城小程序处理
        if (!WechatMiniProgramChannelEnum.XM_MALL_MP.channelCode.equals(channelCode)) {
            return true;
        }

        // 从 Redis 获取 accessToken
        String accessToken = getAccessTokenFromRedis(redisKey);
        if (StringUtils.isEmpty(accessToken)) {
            return false;
        }

        // 调用微信接口检查 accessToken 状态
        return isTokenValid(accessToken);
    }

    /**
     * 从 Redis 获取 accessToken
     *
     * @param redisKey Redis 键
     * @return accessToken 或 null
     */
    private String getAccessTokenFromRedis(String redisKey) {
        Object accessTokenObj = redisTemplate.opsForValue().get(redisKey);
        return accessTokenObj != null ? accessTokenObj.toString() : null;
    }

    /**
     * 检查 accessToken 是否有效
     *
     * @param accessToken 微信 accessToken
     * @return true 表示有效，false 表示无效
     */
    private boolean isTokenValid(String accessToken) {
        String response = wxUtils.getRecentAverageUsage(accessToken, TARGET_SPU_ID);
        JSONObject jsonObject = JSONUtil.parseObj(response);
        Long errCode = jsonObject.getLong(ERR_CODE_KEY);
        // 当 errCode 为 40001 时，token 无效
        return !Long.valueOf(INVALID_TOKEN_ERR_CODE).equals(errCode);
    }

    /**
     *
     * @param weChatTokenTypeEnum 见枚举
     * @param authTypeEnum 见枚举
     * @return token
     */
    @Override
    public String getAccessToken(WeChatTokenTypeEnum weChatTokenTypeEnum, AuthTypeEnum authTypeEnum) {
        String redisKey = String.format(AuthGlobal.WECHAT_REDIS, AuthTypeEnum.OFFICIAL_WE_CHAT.getType(), WeChatTokenTypeEnum.SRM_ACCESS_TOKEN.name().toLowerCase());
        return (String) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public String getAccessToken(String channel) {
        // 兼容商城的逻辑 XMSC  XM_MALL 为空走原来auth
        String redisKey = String.format(AuthGlobal.WECHAT_REDIS, channel.toLowerCase(), channel.toLowerCase());

        if (Objects.equals(channel,WxOfficialAccountsChannelEnum.XM_MALL.channelCode)
                || Objects.equals(channel, WechatMiniProgramChannelEnum.XM_MALL_MP.channelCode)){
            Object o = redisTemplate.opsForValue().get(redisKey);
            if (o != null){
              return o.toString();
            }
            //小程序
            if (Objects.equals(channel, WechatMiniProgramChannelEnum.XM_MALL_MP.channelCode)){
                return (String) redisTemplate.opsForValue().get(getOldTokenKey(MALL_WECHAT_LITE_ACCESS_TOKEN));
            }
            //公众号
            return (String) redisTemplate.opsForValue().get(getOldTokenKey(MALL_WECHAT_ACCESS_TOKEN));
        }

        return (String) redisTemplate.opsForValue().get(redisKey);
    }

    @Override
    public String getTicket(String channel) {
        String redisKey = String.format(AuthGlobal.WECHAT_TICKET_REDIS, channel.toLowerCase(), channel.toLowerCase());
        if (Objects.equals(channel,WxOfficialAccountsChannelEnum.XM_MALL.channelCode)){
            Object o = redisTemplate.opsForValue().get(redisKey);
            if (o != null){
                return o.toString();
            }
            //公众号ticket
            return (String) redisTemplate.opsForValue().get(getOldTokenKey(MALL_WECHAT_JSAPI_TICKET));
        }
        return (String) redisTemplate.opsForValue().get(redisKey);
    }

    private String getOldTokenKey(String oldKey){
        return oldKey+ ":" + SpringContextUtil.getActiveProFile();
    }

    @Override
    /**
     * 增加关系
     */
    public void addRelation() {
        addRelation(WeChatTokenTypeEnum.SRM_ACCESS_TOKEN, AuthTypeEnum.OFFICIAL_WE_CHAT, SystemOriginEnum.SRM);
    }

    @Override
    public AuthQueryWechatInfoDTO Jscode2sessionResult(@Validated AuthQueryWechatInfoInput authQueryWechatInfoInput) {
        if (!Objects.equals(authQueryWechatInfoInput.getType(),AuthTypeEnum.WEI_CHAT)){
            throw new DefaultServiceException("暂不支持该来源");
        }
        authQueryWechatInfoInput.getAccessToken();
        authQueryWechatInfoInput.getAppId();
        return null;
    }

    @Override
    /**
     * 获取手机号
     *
     * @param authQueryWechatInfoInput
     * @return 手机号信息
     */
    public WXPhoneResultDTO queryWxPhone(AuthQueryWechatInfoInput authQueryWechatInfoInput) {
            if (StringUtils.isEmpty(authQueryWechatInfoInput.getAccessToken()) || StringUtils.isEmpty(authQueryWechatInfoInput.getCode())){
                throw new BizException("参数异常");

            }
            String postJsonData = String.format("{\"code\":\"%1$s\"}",
                    authQueryWechatInfoInput.getCode());
            String url = String.format(WX_PHONE_URI,authQueryWechatInfoInput.getAccessToken());
            String result = HttpUtil.post(url,postJsonData ,2000);
            log.info("getUserPhoneNumber 请求 url {} result  {} ",url, result);
            if (StringUtils.isEmpty(result)){
                throw new BizException("请求异常");
            }

            return JSONUtil.toBean(result, WXPhoneResultDTO.class);
        }

    /**
     * 微信获取关注qr
     * @param wechatCareQrInput 获取参数
     * @return qr
     */
    @Override
    public String getWxCareQr(WechatCareQrInput wechatCareQrInput) {
        //check
        checkWxCareQrInput(wechatCareQrInput);

        //获取accesstoken
        String accessToken = getAccessToken(wechatCareQrInput.getChannelCode());

        //生成临时码
        String ticket = getTicket(accessToken, wechatCareQrInput.getChannelCode(), wechatCareQrInput.getPhone());
        return String.format(PRE_QR, ticket);
    }

    private String getTicket(String accessToken, String channelCode, String phone) {
        String url = String.format(GET_QR_URL, accessToken);
        String body = String.format("{\"expire_seconds\": 600, \"action_name\": \"QR_STR_SCENE\", \"action_info\": {\"scene\": {\"scene_str\":\"%s\"}}}",
                getSceneStr(phone));
        log.info("wx getTicket req  url {} body {}", url, body);
        String resp = HttpUtil.post(url, JSONUtil.toJsonStr(body), 2000);
        log.info("wx getTicket resp {}", resp);
        if (StringUtils.isEmpty(resp)) {
            throw new BizException("微信请求异常,稍后重试");
        }
        WxQr dto = convertWxQr(resp);
        if (!StringUtils.isEmpty(dto.getTicket())) {
            return dto.getTicket();
        }
        throw new BizException("微信请求异常,稍后重试");
    }

    private String getSceneStr(String phone) {
        return  phone ;
    }



    private void checkWxCareQrInput(WechatCareQrInput wechatCareQrInput) {
        if (wechatCareQrInput.getTenantId() == null) {
            throw new BizException("租户ID不能为空");
        }
        if (StringUtils.isEmpty(wechatCareQrInput.getPhone())) {
            throw new BizException("手机号不能为空");
        }
        if (StringUtils.isEmpty(wechatCareQrInput.getChannelCode())
                || WxOfficialAccountsChannelEnum.getByChannelCode(wechatCareQrInput.getChannelCode()) == null) {
            throw new BizException("错误的渠道来源");
        }
        if (wechatCareQrInput.getSystemOriginEnum() == null || wechatCareQrInput.getAuthTypeEnum() == null) {
            throw new BizException("系统参数或者类型不能为空");
        }
    }

    /**
     *
     * @param weChatTokenTypeEnum  微信类型
     * @param authTypeEnum  auth_type类型
     * @param systemOriginEnum 系统
     */
    public void addRelation(WeChatTokenTypeEnum weChatTokenTypeEnum, AuthTypeEnum authTypeEnum, SystemOriginEnum systemOriginEnum) {
        String accessToken = getAccessToken(weChatTokenTypeEnum, authTypeEnum);
        //获取有所的openID
        WxOpenidList openList = getOpenList(accessToken, null);
        if (CollectionUtils.isEmpty(openList.data)) {
            return;
        }
        List<String> strings = openList.data.get("openid");
        List<List<String>> split = CollectionUtil.split(strings, 100);
        for (List<String> openids : split) {
            //批量根据openId获取unionId
            addList(systemOriginEnum, weChatTokenTypeEnum, authTypeEnum, openids);
        }
    }

    public List<AuthWechatRelation> addList(SystemOriginEnum systemOriginEnum, WeChatTokenTypeEnum weChatTokenTypeEnum, AuthTypeEnum authTypeEnum, List<String> openids){
        List<OffcieUserVO> unionidsByOpenList = getUnionidsByOpenList(getAccessToken(weChatTokenTypeEnum, authTypeEnum), openids);
        List<AuthWechatRelation> convert = convert(unionidsByOpenList, systemOriginEnum, authTypeEnum);
        //db操作
        return addList(convert, systemOriginEnum, authTypeEnum);
    }

    /**
     * db的操作 先删除后添加 小事务
     * @param list
     * @param systemOriginEnum 来源
     * @param authTypeEnum 类型
     */
    @Transactional
    public List<AuthWechatRelation>  addList(List<AuthWechatRelation> list, SystemOriginEnum systemOriginEnum, AuthTypeEnum authTypeEnum) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<String> openIds = list.stream().map(AuthWechatRelation::getOpenid).collect(Collectors.toList());
        //删除也有的
        authWechatRelationMapper.deleteList(systemOriginEnum.getType(), authTypeEnum.getType(), TENANT_ID, openIds);

        authWechatRelationMapper.batchAdd(list);
        return list;
    }

    private List<AuthWechatRelation> convert(List<OffcieUserVO> unionidsByOpenList, SystemOriginEnum systemOriginEnum,
                                             AuthTypeEnum authTypeEnum) {
        return unionidsByOpenList.stream().map(it -> {
            AuthWechatRelation authWechatRelation = new AuthWechatRelation();
            authWechatRelation.setOpenid(it.getOpenid());
            authWechatRelation.setUnionid(it.getUnionid());
            authWechatRelation.setAuthType(authTypeEnum.getType().byteValue());
            authWechatRelation.setTenantId(TENANT_ID);
            authWechatRelation.setSystemOrigin(systemOriginEnum.getType().byteValue());
            return authWechatRelation;
        }).collect(Collectors.toList());


    }

    /**
     * @param token
     * @param openId
     * @return
     */
    public List<OffcieUserVO> getUnionidsByOpenList(String token, List<String> openId) {
        String url = String.format(USER_LIST_URL, token);
        Map<String, Object> body = new HashMap<>();
        List<Map<String, Object>> bodyDetail = new ArrayList<>(openId.size());
        openId.forEach(
                it -> {
                    Map<String, Object> vo = new HashMap<>();
                    vo.put("openid", it);
                    vo.put("lang", "zh_CN");
                    bodyDetail.add(vo);
                }
        );
        body.put("user_list", bodyDetail);
        String resp = HttpUtil.post(url, JSONUtil.toJsonStr(body), 2000);
        log.info("access_token请求 user_list 接口 body {}",JSONUtil.toJsonStr(body));
        if (StringUtils.isEmpty(resp)) {
            log.error("微信调用接口失败", new RuntimeException(resp));
            return new ArrayList<>();
        }
        log.info("access_token请求 user_list 接口 resp {}",JSONUtil.toJsonStr(resp));
        JSONObject jsonObject = JSONUtil.parseObj(resp);
        if (jsonObject.get("user_info_list") == null) {
            log.warn("微信调用接口失败", new RuntimeException(resp));
            return new ArrayList<>();
        }
        return JSONUtil.toList(jsonObject.get("user_info_list").toString(), OffcieUserVO.class).
                stream().filter(it -> !StringUtils.isEmpty(it.getUnionid())).collect(Collectors.toList());
    }

    private WxOpenidList getOpenList(String accessToken, String lastOpenId) {
        String url = String.format(OPEN_ID_LIST_URL, accessToken);
        if (!StringUtils.isEmpty(lastOpenId)) {
            url = url + "&next_openid" + lastOpenId;
        }
        String result = HttpUtil.get(url, 2000);
        log.info("wx getOpenList  result{}", result);
        if (StringUtils.isEmpty(result)) {
            log.error("微信获取openIdList请求错误", new RuntimeException(result));
            return new WxOpenidList(0, 0, null, null);
        }
        return JSONUtil.toBean(result, WxOpenidList.class);
    }


    /**
     * 用户登录--通过第三方平台获取小程序登录session_key
     * 接口响应的是text/plain格式，框架无法自动解析成对象
     * @since 2.8.9
     * @param appid appid
     * @param js_code js_code
     * @param component_appid component_appid
     * @param component_access_token component_access_token
     * @return result
     */
     public  AuthQueryWechatInfoDTO componentJscode2session(String appid,String js_code,String component_appid,String component_access_token){
        String url = String.format(JSCODE2SESSION_URI,appid,js_code,component_appid,component_access_token);
        String result = HttpUtil.get(url, 2000);
        log.info("componentJscode2session 请求 url {} result  {} ",url, result);
        if (StringUtils.isEmpty(result)){
            throw new BizException("请求异常");
        }
        return convertAuthQueryWechatInfoDTO(result);
    }




    private  AuthQueryWechatInfoDTO convertAuthQueryWechatInfoDTO(String result) {
        if (result == null){
            return null;
        }
        JSONObject jsonObject = JSONUtil.parseObj(result);
        AuthQueryWechatInfoDTO dto = new AuthQueryWechatInfoDTO();
        dto.setOpenid(jsonObject.getStr("openid"));
        dto.setUnionid("unionid");
        dto.setSessionKey("session_key");
        return dto;
    }

    private  WxQr convertWxQr(String result) {
        if (result == null){
            return null;
        }
        JSONObject jsonObject = JSONUtil.parseObj(result);
        WxQr wxQr = new WxQr(jsonObject.getStr("ticket"),jsonObject.getStr("url"),jsonObject.getLong("expire_seconds"));
        return wxQr;
    }




    @Data
    @AllArgsConstructor
    private class WxOpenidList {
        private Integer total;

        private Integer count;

        private Map<String, List<String>> data;

        private String next_openid;


    }


    @Data
    @AllArgsConstructor
    private class WechatToken {
        /**
         * token过期时间
         */
        private Integer expires_in;

        /**
         * token
         */
        private String access_token;

        private String ticket;
    }

    @Data
    @AllArgsConstructor
    private class OffcieUserVO {
        /**
         * unionid
         */
        private String unionid;

        /**
         * token
         */
        private String openid;
    }

    @Data
    @AllArgsConstructor
    private class WxQr {
        /**
         * ticket
         */
        private String ticket;

        /**
         * url
         */
        private String url;

        /**
         * expire
         */
        private Long expire_seconds;
    }
}
