package net.xianmu.authentication.service.wechat.qiye;

import net.xianmu.authentication.client.enums.EnterpriseWeChatTokenTypeEnum;
import net.xianmu.authentication.model.DTO.AuthUserAuthThirdPartyDTO;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.authentication.model.entity.EnterpriseWechatUserRelation;

import java.util.List;

public interface EnterpriseWechatService {
    /**
     * 更新企业微信的access_token
     */
    void accessTokenTask();

    String getAccessToken(EnterpriseWeChatTokenTypeEnum enterpriseWeChatTokenTypeEnum,Boolean update);


    /**
     * 关联企业微信的user_id
     */
    void updateUserId();

    /**
     * 批量更新用户飞书userId
     * @param authUserBaseList 用户信息
     */
    void batchUpdateUserId(List<EnterpriseWechatUserRelation> authUserBaseList);
}
