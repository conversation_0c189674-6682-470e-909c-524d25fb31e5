package net.xianmu.authentication.service.wechat;


import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.dto.wx.WXPhoneResultDTO;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.WeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;
import net.xianmu.authentication.client.input.wechat.WechatCareQrInput;

public interface WxService {
    /**
     * 更新企业微信的access_token
     */
    void accessTokenTask();

    /**
     * 更新企业微信的access_token
     */
    void srmAccessTokenTask();

    void mallAccessTokenTask();

    void popMallAccessTokenTask();

    void srmTicketUpdateTask();

    /**
     * 获取access_token
     *
     * @param weChatTokenTypeEnum
     * @return
     */
    String getAccessToken(WeChatTokenTypeEnum weChatTokenTypeEnum, AuthTypeEnum authTypeEnum);


    String getAccessToken(String channel);

    String getTicket(String channel);


    /**
     * 新增关系表
     */
    void addRelation();


    AuthQueryWechatInfoDTO Jscode2sessionResult(AuthQueryWechatInfoInput authQueryWechatInfoInput);


    WXPhoneResultDTO queryWxPhone(AuthQueryWechatInfoInput authQueryWechatInfoInput);

    String getWxCareQr(WechatCareQrInput wechatCareQrInput);


}
