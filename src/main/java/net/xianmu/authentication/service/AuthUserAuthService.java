package net.xianmu.authentication.service;

import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.input.wechat.WechatCareQrInput;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.model.DTO.AuthUserAuthThirdPartyDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/17  13:55
 */
public interface AuthUserAuthService {
    /**
     * 新增或更新三方平台授权信息
     * @param authType {@link  AuthTypeEnum}
     * @param userAuthList 批量数据更新｜新增
     */
    void batchUpsertUserAuth(List<AuthUserAuthThirdPartyDTO> userAuthList);

    List<AuthUserAuthResp> queryUserRespByPhones(AuthUserAuthQueryInput authUserAuthQueryInput);

    Boolean bindWechatCare(WechatCareQrInput wechantCareInput);

    Boolean closeWechatCare(WechatCareQrInput wechantCareInput);
}
