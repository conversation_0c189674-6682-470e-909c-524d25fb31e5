package net.xianmu.authentication.service;

import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.user.AuthUserLastUpdatePwdTimeResp;
import net.xianmu.authentication.client.input.user.AuthUserBaseQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.input.user.BaseUserUpdateInput;
import net.xianmu.authentication.client.resp.AuthUserBaseResp;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;

import java.util.List;

public interface AuthUserBaseService {
    Boolean updateUserBase(SystemOriginEnum systemOriginEnum, BaseUserUpdateInput baseUserUpdateInput);

    AuthUserBaseResp queryBizUserId(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput);

    AuthUserBaseResp queryRealName(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput);

    AuthUserBaseResp queryPassword(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput);


    Boolean updateAllPwd(Long userBaseId, String pwd);

    /**
     * 批量查询用户基础数据
     *
     * @param phoneList 手机号
     * @return 用户基础数据
     */
    List<AuthUserBase> batchQueryUserBaseByPhone(List<String> phoneList);

    /**
     * 分页查询用户数据
     *
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @return 用户数据
     */
    List<AuthUserPhoneDTO> selectAuthUserBaseWithOrigin(SystemOriginEnum originEnum, int pageIndex, int pageSize);


    /**
     * 分页查询用户数据
     *
     * @param pageIndex 页码
     * @param pageSize  分页大小
     * @return 用户数据
     */
    List<AuthUserPhoneDTO> selectWithOriginWithOutBigCustomer(SystemOriginEnum originEnum, int pageIndex, int pageSize);

    /**
     * 查询最后更新密码的时间
     *
     * @param systemOriginEnum 系统来源标识
     * @param phone            手机号码
     * @return AuthUserLastUpdatePwdTimeResp对象，表示查询结果
     */
    AuthUserLastUpdatePwdTimeResp queryLastUpdatePwdTime(SystemOriginEnum systemOriginEnum, String phone);



    /**
     * 批量查询用户基础数据
     *
     * @param phoneList 手机号
     * @return 用户基础数据
     */
    List<AuthUserBase> queryAuthUserBase(AuthUserBaseQueryInput authUserBaseQueryInput);
}
