package net.xianmu.authentication.service;


import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.common.user.UserBase;

public interface AuthUserServiceV2 {

    /**
     *
     * @param systemOriginEnum 来源
     * @param userBase  用户信息
     * @param baseUserExtend 扩展信息
     * @return
     */
    UserBase createUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend);

}
