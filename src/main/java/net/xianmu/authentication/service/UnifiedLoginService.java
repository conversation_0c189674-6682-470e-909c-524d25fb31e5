package net.xianmu.authentication.service;

import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.model.BO.LoginBO;
import net.xianmu.authentication.model.BO.AuthLoginBO;
import net.xianmu.authentication.model.BO.LogoutBO;
import net.xianmu.authentication.model.DTO.AuthLoginRespDTO;
import net.xianmu.authentication.model.VO.LoginVO;

/**
 * Description:统一登录接口层
 * date: 2022/6/10 16:55
 *
 * <AUTHOR>
 */
public interface UnifiedLoginService {

    /**
     * 账密登陆入口
     * @param loginBO 授权绑定业务实体
     * @return 登录结果
     */
    AuthLoginDto login(LoginBO loginBO);

    /**
     * 小程序授权登录
     * @param authLoginBO 授权登录业务实体
     * @return 授权登录结果
     */
    AuthLoginDto authLogin(AuthLoginBO authLoginBO);

    /**
     * 小程序退出登录
     * @param logoutBO 授权退出业务实体
     * @return 退出登录结果
     */
    void logout(LogoutBO logoutBO);
}
