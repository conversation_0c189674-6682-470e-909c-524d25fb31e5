package net.xianmu.authentication.service;

import net.summerfarm.common.AjaxResult;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.input.UpdateAuthUserVO;
import net.xianmu.authentication.client.input.login.AuthMockLoginInput;
import net.xianmu.authentication.common.enmu.LoginTypeEnum;
import net.xianmu.authentication.model.VO.AuthLoginVO;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.result.CommonResult;

public interface AuthLoginService {
    AjaxResult<AuthLoginDto> login(AuthLoginVO authLoginVO);

    AuthLoginDto mockLogin(AuthUserBase userBase, AuthUser user, SystemOriginEnum originEnum, LoginTypeEnum loginType);

    AjaxResult loginByToken(AuthLoginVO authLoginVO);

    AjaxResult personalInfo();

    AjaxResult updateAuthUserByPhone(UpdateAuthUserVO authLoginVO);

    AjaxResult authLogin(Integer type, String code);

    //    https://open.feishu.cn/document/server-docs/contact-v3/user/get
    CommonResult<AuthLoginDto> loginWithFeishuUnionId(String unionId);

    AjaxResult authLogin(Integer type, String code, Integer systemOrigin, String channel);


    AjaxResult authBind(Integer type, String username, String password, String uuid);

    AjaxResult authLogout(Integer type);


    Boolean checkSafeLogin(Integer originEnum, String phone);


    void pwdErrorLoginHandler(Integer originEnum, String phone);


    void deleteBindRedis(Integer originEnum, String phone);

    AuthLoginDto crossLogin(Integer systemOriginEnum, AuthMockLoginInput authMockLoginInput);
}
