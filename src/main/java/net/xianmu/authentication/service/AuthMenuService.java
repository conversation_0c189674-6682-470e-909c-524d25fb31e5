package net.xianmu.authentication.service;


import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.dto.AuthMenuPurviewDto;
import net.xianmu.authentication.client.input.PurviewWeighVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.RolePreviewInput;

import java.util.List;

public interface AuthMenuService {
    /**
     * @param systemOriginEnum 系统来源
     * @param userId           用户id
     * @return
     */
    List<AuthMenuPurview> getAuthMenuPurviews(SystemOriginEnum systemOriginEnum, Long tenantId, Long userId);

    /**
     * @param systemOriginEnum 系统来源
     * @return
     */
    List<AuthMenuPurview> getAuthMenuPurviews(SystemOriginEnum systemOriginEnum);


    /**
     * 根据租户ID获取授权菜单权限
     * @param systemOriginEnum 系统来源枚举
     * @param tenantId 租户ID
     * @return AuthMenuPurview列表
     */
    List<AuthMenuPurview> getAuthTenantMenuPurview(SystemOriginEnum systemOriginEnum, Long tenantId);




    /**
     * @param systemOriginEnum 系统来源
     * @param userId           用户id
     * @param authMenuPurview  菜单信息
     * @return
     */
    int addPurviews(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthMenuPurview authMenuPurview);

    /**
     * 修改菜单
     *
     * @param systemOriginEnum 系统来源
     * @param userId           用户id
     * @param authMenuPurview  菜单信息
     * @return
     */
    int updatePurviews(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthMenuPurview authMenuPurview);

    /**
     * 删除菜单
     *
     * @param systemOriginEnum 系统来源
     * @param userId           用户id
     * @param purviewId        菜单Id
     * @return
     */
    int deletePurviews(SystemOriginEnum systemOriginEnum, Long userId, Long purviewId);

    /**
     * 修改菜单顺序
     */
    int updateMenusWeigh(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, List<PurviewWeighVO> purviewWeighVOS);

    /**
     * 根据菜单名称和角色名称来新增权限
     * @param systemOriginEnum
     * @param rolePreviewInputs
     * @return
     */
    int createRolePurviews(SystemOriginEnum systemOriginEnum, List<RolePreviewInput> rolePreviewInputs);

    List<AuthMenuPurviewDto> getMenusTreeByMenuIds(SystemOriginEnum systemOriginEnum, Long tenantId, List<Long> menuIds);
}
