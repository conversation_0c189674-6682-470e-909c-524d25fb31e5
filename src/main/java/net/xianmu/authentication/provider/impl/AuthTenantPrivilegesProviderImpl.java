package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.client.provider.AuthTenantPrivilegesProvider;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@DubboService
@Component
@Slf4j
public class AuthTenantPrivilegesProviderImpl implements AuthTenantPrivilegesProvider {
    @Resource
    AuthTenantPrivilegesService authTenantPrivilegesService;

    @Override
    public DubboResponse<Boolean> addTenantPrivileges(AuthTenantPrivilegesInput authTenantPrivilegesInput) {
        return DubboResponse.getOK(authTenantPrivilegesService.addTenantPrivileges(authTenantPrivilegesInput));
    }


}
