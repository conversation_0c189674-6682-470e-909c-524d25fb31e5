package net.xianmu.authentication.provider.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.AuthUserAuthBinlogInput;
import net.xianmu.authentication.client.input.binlog.AuthUserExtBinlogInput;
import net.xianmu.authentication.client.provider.AuthUserExtendProvider;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.Date;

@DubboService
@Component
@Slf4j
public class AuthUserExtendProviderImpl implements AuthUserExtendProvider {

    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthUserAuthDao authUserAuthDao;
    @Resource
    AuthUserPropertiesExtDao authUserPropertiesExtDao;
    @Override
    public DubboResponse<Boolean> updateOldUserAuth(SystemOriginEnum systemOriginEnum, AuthUserAuthBinlogInput old, AuthUserAuthBinlogInput authUserAuthBinlogInput1) {
        AuthUser authUser = getAuthUser(systemOriginEnum, old.getBizUserId());
        if (authUser == null){
            log.warn("找不到用户数据 {}",old.getBizUserId());
            return DubboResponse.getOK(false);
        }
        Integer authType = old.getAuthType();
        String authId = old.getAuthId();
        AuthUserAuth authUserAuth = authUserAuthDao.selectByUserIdOpenIdType(authUser.getId(), authId, authType);
        if (authUserAuth == null) {
            //新的插入
            authUserAuth = new AuthUserAuth();
            authUserAuth.setAuthId(authUserAuthBinlogInput1.getAuthId());
            authUserAuth.setUserId(authUser.getId());
            authUserAuth.setThirdPartyId(authUserAuthBinlogInput1.getAuthId());
            authUserAuth.setAuthType(authType.byteValue());
            authUserAuth.setCreateTime(new Date());
            authUserAuth.setUpdateTime(new Date());
            authUserAuthDao.insertSelective(authUserAuth);
            return DubboResponse.getOK(true);
        }
        if (!StringUtils.isEmpty(authUserAuthBinlogInput1.getAuthId())) {
            authUserAuth.setThirdPartyId(authUserAuthBinlogInput1.getAuthId());
            authUserAuth.setAuthId(authUserAuthBinlogInput1.getAuthId());
            authUserAuthDao.updateByPrimaryKeySelective(authUserAuth);
        }
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> updateOldAuthEXt(SystemOriginEnum systemOriginEnum, AuthUserExtBinlogInput old, AuthUserExtBinlogInput authUserExtBinlogInput1) {
        AuthUser authUser = getAuthUser(systemOriginEnum, old.getBizUserId());
        if (authUser == null){
            log.warn("找不到用户数据 {}",old.getBizUserId());
            return DubboResponse.getOK(false);
        }
        AuthUserPropertiesExt authUserPropertiesExt = authUserPropertiesExtDao.selectUserIdKeyValue(authUser.getId(), old.getPropKey(), old.getPropValue());
        if (authUserPropertiesExt == null){
            authUserPropertiesExt = new AuthUserPropertiesExt();
            authUserPropertiesExt.setUserId(authUser.getId());
            authUserPropertiesExt.setPropKey(StringUtils.isEmpty(authUserExtBinlogInput1.getPropKey())?old.getPropKey():authUserExtBinlogInput1.getPropKey());
            authUserPropertiesExt.setPropValue(authUserExtBinlogInput1.getPropValue());
            authUserPropertiesExt.setCreateTime(new Date());
            authUserPropertiesExt.setUpdateTime(new Date());
            authUserPropertiesExtDao.insertSelective(authUserPropertiesExt);
          return DubboResponse.getOK(true);
        }
        if (!StringUtils.isEmpty(authUserExtBinlogInput1.getPropKey())){
            authUserPropertiesExt.setPropKey(authUserExtBinlogInput1.getPropKey());
        }
        if (!StringUtils.isEmpty(authUserExtBinlogInput1.getPropValue())){
            authUserPropertiesExt.setPropValue(authUserExtBinlogInput1.getPropValue());
        }
        authUserPropertiesExt.setUpdateTime(new Date());
        authUserPropertiesExtDao.updateByPrimaryKeySelective(authUserPropertiesExt);
        return DubboResponse.getOK(true);
    }

    @Override
    @Transactional
    public DubboResponse<Boolean> updatePasswordExt(SystemOriginEnum systemOriginEnum, AuthUserExtBinlogInput authUserExtBinlogInput) {
        AuthUser authUser = getAuthUser(systemOriginEnum, authUserExtBinlogInput.getBizUserId());
        if (authUser == null){
            log.warn("找不到用户数据 {}", JSONUtil.toJsonStr(authUserExtBinlogInput));
            return DubboResponse.getOK(false);
        }
        authUserPropertiesExtDao.deleteByAllUserIdsKey(Collections.singletonList(authUser.getId()), authUserExtBinlogInput.getPropKey());
        AuthUserPropertiesExt authUserPropertiesExt = new AuthUserPropertiesExt();
        authUserPropertiesExt.setUserId(authUser.getId());
        authUserPropertiesExt.setPropKey(authUserExtBinlogInput.getPropKey());
        authUserPropertiesExt.setPropValue(authUserExtBinlogInput.getPropValue());
        authUserPropertiesExt.setCreateTime(new Date());
        authUserPropertiesExt.setUpdateTime(new Date());
        authUserPropertiesExtDao.insertSelective(authUserPropertiesExt);
        return DubboResponse.getOK(true);
    }


    @Override
    public DubboResponse<Boolean> binlogAddAuthUserAuth(SystemOriginEnum systemOriginEnum, AuthUserAuthBinlogInput authUserAuthBinlogInput) {
        AuthUser authUser = getAuthUser(systemOriginEnum, authUserAuthBinlogInput.getBizUserId());
        Integer authType = authUserAuthBinlogInput.getAuthType();
        String authId = authUserAuthBinlogInput.getAuthId();
        if (authUser == null){
            log.warn("找不到用户数据 {}",authUserAuthBinlogInput.getBizUserId());
            return DubboResponse.getOK(false);
        }
        AuthUserAuth authUserAuth = authUserAuthDao.selectByUserIdOpenIdType(authUser.getId(), authId, authType);
        if (authUserAuth == null) {
            authUserAuth = new AuthUserAuth();
            authUserAuth.setAuthId(authId);
            authUserAuth.setUserId(authUser.getId());
            authUserAuth.setThirdPartyId(authId);
            authUserAuth.setAuthType(authType.byteValue());
            authUserAuth.setCreateTime(new Date());
            authUserAuth.setUpdateTime(new Date());
            authUserAuthDao.insertSelective(authUserAuth);
        }
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> binlogAddAuthEXt(SystemOriginEnum systemOriginEnum, AuthUserExtBinlogInput authUserExtBinlogInput) {
        AuthUser authUser = getAuthUser(systemOriginEnum, authUserExtBinlogInput.getBizUserId());
        if (authUser == null){
            log.warn("找不到用户数据 {}",authUserExtBinlogInput.getBizUserId());
            return DubboResponse.getOK(false);
        }
        AuthUserPropertiesExt authUserPropertiesExt = authUserPropertiesExtDao.selectUserIdKeyValue(authUser.getId(), authUserExtBinlogInput.getPropKey(), authUserExtBinlogInput.getPropValue());
        if (authUserPropertiesExt == null) {
            authUserPropertiesExt = new AuthUserPropertiesExt();
            authUserPropertiesExt.setUserId(authUser.getId());
            authUserPropertiesExt.setPropKey(authUserExtBinlogInput.getPropKey());
            authUserPropertiesExt.setPropValue(authUserExtBinlogInput.getPropValue());
            authUserPropertiesExt.setCreateTime(new Date());
            authUserPropertiesExt.setUpdateTime(new Date());
            authUserPropertiesExtDao.insertSelective(authUserPropertiesExt);
        }
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> binlogdeleteAuthUserAuth(SystemOriginEnum systemOriginEnum, AuthUserAuthBinlogInput authUserAuthBinlogInput) {
        AuthUser authUser = getAuthUser(systemOriginEnum, authUserAuthBinlogInput.getBizUserId());
        if (authUser == null){
            log.warn("找不到用户数据 {}",authUserAuthBinlogInput.getBizUserId());
            return DubboResponse.getOK(false);
        }
        Integer authType = authUserAuthBinlogInput.getAuthType();
        String authId = authUserAuthBinlogInput.getAuthId();
        AuthUserAuth authUserAuth = authUserAuthDao.selectByUserIdOpenIdType(authUser.getId(), authId, authType);
        if (authUserAuth == null) {
            return DubboResponse.getOK(true);
        }
        authUserAuthDao.deleteByPrimaryKey(authUserAuth.getId());
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> binlogdeleteAuthExt(SystemOriginEnum systemOriginEnum, AuthUserExtBinlogInput authUserExtBinlogInput) {
        AuthUser authUser = getAuthUser(systemOriginEnum, authUserExtBinlogInput.getBizUserId());
        if (authUser == null){
            log.warn("找不到用户数据 {}",authUserExtBinlogInput.getBizUserId());
            return DubboResponse.getOK(false);
        }
        AuthUserPropertiesExt authUserPropertiesExt = authUserPropertiesExtDao.selectUserIdKeyValue(authUser.getId(), authUserExtBinlogInput.getPropKey(), authUserExtBinlogInput.getPropValue());
        if (authUserPropertiesExt == null) {
            return DubboResponse.getOK(true);
        }
        authUserPropertiesExtDao.deleteByPrimaryKey(authUserPropertiesExt.getId());
        return DubboResponse.getOK(true);
    }


    private AuthUser getAuthUser(SystemOriginEnum systemOriginEnum, Long bizUserId) {
        AuthUser authUser = authUserDao.selectByBizUserId(systemOriginEnum.getType(), bizUserId);
        return authUser;
    }
}
