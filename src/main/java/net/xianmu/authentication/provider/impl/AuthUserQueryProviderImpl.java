package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.user.AuthUserResp;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.provider.AuthUserQueryProvider;
import net.xianmu.authentication.common.convert.AuthUserRespConvert;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.mapper.auth.AuthUserRoleDao;
import net.xianmu.authentication.model.BO.AuthUserBO;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@DubboService
@Component
@Slf4j
public class AuthUserQueryProviderImpl implements AuthUserQueryProvider {
    @Resource
    private AuthUserDao authUserDao;
    @Resource
    private AuthUserAuthDao authUserAuthDao;
    @Resource
    private AuthUserBaseDao authUserBaseDao;
    @Resource
    private AuthUserRoleDao authUserRoleDao;


    @Override
    public DubboResponse<List<AuthUserResp>> queryAuthUserList(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {
        if (systemOriginEnum == null) {
            throw new DefaultServiceException("参数错误 systemOriginEnum 不能为空");
        }
        if (authUserQueryInput.getTenantId() == null) {
            throw new DefaultServiceException("参数错误 tenantId 不能为空");
        }
        if (authUserQueryInput.getBizId() != null) {
            List<Long> bizIds = authUserQueryInput.getBizIds();
            List<Long> allIds = new ArrayList<>(bizIds.size() + 1);
            allIds.addAll(bizIds);
            allIds.add(authUserQueryInput.getBizId());
            authUserQueryInput.setBizIds(allIds);
        }
        List<AuthUser> authUsers = queryAuthUsers(systemOriginEnum, authUserQueryInput);

        return DubboResponse.getOK(convert(authUsers));
    }

    @Override
    public DubboResponse<List<AuthUserResp>> queryByPhones(List<String> phones) {
        if (CollectionUtils.isEmpty(phones)){
            throw new DefaultServiceException("参数错误不能为空");
        }
        if (phones.size()>500){
            throw new DefaultServiceException("最多查询500个");
        }
        List<AuthUser> authUsers = authUserDao.selectByPhone(phones);
        return DubboResponse.getOK(convert(authUsers));
    }

    @Override
    public DubboResponse<List<AuthUserResp>> queryUserByRoleIds(SystemOriginEnum systemOriginEnum, Long tenantId, List<Long> roleIds) {
        if (systemOriginEnum == null){
            throw new BizException("参数错误 systemOriginEnum 不能为空");
        }
        if (tenantId == null) {
            throw new BizException("参数错误 tenantId 不能为空");
        }
        if (CollectionUtils.isEmpty(roleIds)) {
            throw new BizException("参数错误 roleIds 不能为空");
        }
        List<AuthUserBO> authUsers = authUserDao.selectAuthUserBySystemTenantIdRoleIds(systemOriginEnum.getType(), tenantId, roleIds);
        if (CollectionUtils.isEmpty(authUsers)){
            return DubboResponse.getOK();
        }
        List<AuthUserResp> result = authUsers.stream().map(AuthUserRespConvert::toAuthUserResp).collect(Collectors.toList());
        return DubboResponse.getOK(result);
    }

    /**
     * 查询auth_user信息
     * @param systemOriginEnum 来源
     * @param authUserQueryInput 查询条件
     * @return
     */
    private List<AuthUser> queryAuthUsers(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {

        if (!CollectionUtils.isEmpty(authUserQueryInput.getBizIds())) {
            return authUserDao.selectBySourceTenantIdBizIds(systemOriginEnum.getType(), authUserQueryInput.getTenantId(), authUserQueryInput.getBizIds());
        }
        if (!StringUtils.isEmpty(authUserQueryInput.getPhone())) {
            return authUserDao.selectBySourceTenantIdPhone(systemOriginEnum.getType(), authUserQueryInput.getTenantId(), authUserQueryInput.getPhone());
        }
        if (!CollectionUtils.isEmpty(authUserQueryInput.getAuthUserIds())){
            return authUserDao.selectByUserIdAndOrigin(systemOriginEnum.getType(), authUserQueryInput.getTenantId(), authUserQueryInput.getAuthUserIds(), null );
        }

        return new ArrayList<>();
    }

    /**
     * 类型转换
     * @param authUsers
     * @return
     */
    private List<AuthUserResp> convert(List<AuthUser> authUsers) {
        if (CollectionUtils.isEmpty(authUsers)) {
            return new ArrayList<>();
        }
        List<Long> ids = authUsers.stream().map(AuthUser::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        List<Long> baseUserIds = authUsers.stream().map(AuthUser::getUserBaseId).distinct().collect(Collectors.toList());

        List<AuthUserResp> authUserResps = AuthUserRespConvert.toAuthUserRespList(authUsers);
        Map<Long, AuthUserAuth> wxBaseMap = new HashMap<>(authUserResps.size());
        Map<Long, AuthUserAuth> mpWxBaseMap = new HashMap<>(authUserResps.size());
        Map<Long, AuthUserBase> authUserBaseMap = new HashMap<>(authUserResps.size());

        List<AuthUserAuth> mpWechatAuth = authUserAuthDao.selectByUserIdsType(ids, AuthTypeEnum.WEI_CHAT.getType());
        if (!CollectionUtils.isEmpty(mpWechatAuth)) {
            mpWxBaseMap.putAll(mpWechatAuth.stream().collect(Collectors.toMap(AuthUserAuth::getUserId, Function.identity())));
        }
        List<AuthUserAuth> wechatAuth = authUserAuthDao.selectByUserIdsType(ids, AuthTypeEnum.OFFICIAL_WE_CHAT.getType());
        if (!CollectionUtils.isEmpty(wechatAuth)) {
            wxBaseMap.putAll(wechatAuth.stream().collect(Collectors.toMap(AuthUserAuth::getUserId, Function.identity())));
        }
        if (!CollectionUtils.isEmpty(baseUserIds)) {
            List<AuthUserBase> authUserBases = authUserBaseDao.selectByIds(baseUserIds);
            authUserBaseMap.putAll(authUserBases.stream().collect(Collectors.toMap(AuthUserBase::getId, Function.identity())));
        }
        authUserResps.forEach(
                it -> {
                    AuthUserAuth wx = wxBaseMap.get(it.getId());
                    if (wx != null) {
                        it.setOpenid(wx.getAuthId());
                        if (!StringUtils.isEmpty(wx.getThirdPartyId())) {
                            it.setUnionid(wx.getThirdPartyId());
                        }
                    }
                    AuthUserAuth mpwx = mpWxBaseMap.get(it.getId());
                    if (mpwx != null) {
                        it.setMpOpenid(mpwx.getAuthId());
                        if (!StringUtils.isEmpty(mpwx.getThirdPartyId())) {
                            it.setUnionid(mpwx.getThirdPartyId());
                        }
                    }
                    if (authUserBaseMap.get(it.getUserBaseId()) != null) {
                        it.setPhone(authUserBaseMap.get(it.getUserBaseId()).getPhone());
                        it.setName(authUserBaseMap.get(it.getUserBaseId()).getNickname());
                        it.setUsername(authUserBaseMap.get(it.getUserBaseId()).getUsername());
                    }
                }
        );
        return authUserResps;
    }
}
