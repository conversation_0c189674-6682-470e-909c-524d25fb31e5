package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.login.AuthQueryWechatInfoDTO;
import net.xianmu.authentication.client.dto.wx.WXPhoneResultDTO;
import net.xianmu.authentication.client.input.login.AuthClientLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthClientMallLoginProviderInput;
import net.xianmu.authentication.client.input.login.AuthQueryWechatInfoInput;
import net.xianmu.authentication.client.provider.AuthClientLoginProvider;
import net.xianmu.authentication.service.AuthClientLoginService;
import net.xianmu.authentication.service.wechat.impl.WxServiceImpl;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

@DubboService
@Component
@Slf4j
public class AuthClientLoginProviderImpl implements AuthClientLoginProvider {
    @Resource
    private WxServiceImpl wxService;
    @Resource
    private AuthClientLoginService authClientLoginService;
    @Resource(name = "authRedisTemplate")
    RedisTemplate redisTemplate;
    @Override
    public DubboResponse<AuthLoginDto> authClientLogin(AuthClientLoginProviderInput authClientLoginProviderInput) {
        return DubboResponse.getOK(authClientLoginService.authClientLogin(authClientLoginProviderInput));
    }

    @Override
    public DubboResponse<AuthQueryWechatInfoDTO> authQueryWechatInfo(AuthQueryWechatInfoInput authQueryWechatInfoInput) {
        AuthQueryWechatInfoDTO dto = wxService.componentJscode2session(authQueryWechatInfoInput.getAppId(), authQueryWechatInfoInput.getCode(),
                authQueryWechatInfoInput.getMyAppId(), authQueryWechatInfoInput.getAccessToken());
        return DubboResponse.getOK(dto);
    }

    @Override
    public DubboResponse<Boolean> loginOut(AuthClientLoginProviderInput authClientLoginProviderInput) {
        String token = authClientLoginProviderInput.getToken();
        if (StringUtils.isEmpty(token)){
            throw new DefaultServiceException("token 不能为空");
        }
        redisTemplate.delete(token);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<WXPhoneResultDTO> authQueryWechatPhone(AuthQueryWechatInfoInput authQueryWechatInfoInput) {
        WXPhoneResultDTO wxPhoneResultDTO = wxService.queryWxPhone(authQueryWechatInfoInput);
        return DubboResponse.getOK(wxPhoneResultDTO);
    }

    @Override
    public DubboResponse<AuthLoginDto> authLogin(AuthClientMallLoginProviderInput authClientMallLoginProviderInput) {
        return DubboResponse.getOK(authClientLoginService.authLogin(authClientMallLoginProviderInput));
    }

    @Override
    public DubboResponse<AuthQueryWechatInfoDTO> authMallQueryWechatInfo(AuthQueryWechatInfoInput authQueryWechatInfoInput) {
        return DubboResponse.getOK(authClientLoginService.authMallQueryWechatInfo(authQueryWechatInfoInput));
    }

    @Override
    public DubboResponse<AuthQueryWechatInfoDTO> authPopMallQueryWechatInfo(AuthQueryWechatInfoInput authQueryWechatInfoInput) {
        return DubboResponse.getOK(authClientLoginService.authPopMallQueryWechatInfo(authQueryWechatInfoInput));
    }
}
