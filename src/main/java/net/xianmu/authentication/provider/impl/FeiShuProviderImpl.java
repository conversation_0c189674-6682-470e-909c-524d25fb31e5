package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.FeiShuTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.FeiShuProvider;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.common.enums.base.feishu.FeiShuTokenCacheKey;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.i18n.result.util.I18nDubboResponseUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/7/17  11:39
 */
@DubboService
@Component
@Slf4j
public class FeiShuProviderImpl implements FeiShuProvider {
    @Resource(name = "redisTemplate")
    private RedisTemplate redisTemplate;

    @Override
    public DubboResponse<String> queryFeiShuToken(SystemOriginEnum systemOriginEnum, Long tenantId, FeiShuTokenTypeEnum feiShuTokenTypeEnum) {
        if (Objects.isNull(tenantId) || Objects.equals(tenantId, AuthGlobal.TENANT_ID)) {
            String redisKey;
            if (feiShuTokenTypeEnum.equals(FeiShuTokenTypeEnum.TENANT_ACCESS_TOKEN)) {
                redisKey = FeiShuTokenCacheKey.TENANT_ACCESS_TOKEN;
            } else if (feiShuTokenTypeEnum.equals(FeiShuTokenTypeEnum.APP_ACCESS_TOKEN)) {
                redisKey = FeiShuTokenCacheKey.APP_ACCESS_TOKEN;
            } else {
                return I18nDubboResponseUtil.getDefaultError("token类型参数错误");
            }

            if (Boolean.TRUE.equals(redisTemplate.hasKey(redisKey))){
                String token = (String) redisTemplate.opsForValue().get(redisKey);
                return DubboResponse.getOK(token);
            } else {
                log.error("飞书token异常，key：{}", redisKey, new RuntimeException());
                return I18nDubboResponseUtil.getDefaultError("token异常");
            }
        }
        return I18nDubboResponseUtil.getDefaultError("该租户未对接飞书token，请确认参数或接入该租户");
    }
}
