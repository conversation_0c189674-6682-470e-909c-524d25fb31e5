package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.authentication.client.provider.AuthUserCommandV2Provider;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.authentication.service.AuthUserService;
import net.xianmu.authentication.service.AuthUserServiceV2;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/4/17 15:32
 */

@DubboService
@Component
@Slf4j
public class AuthUserCommandV2ProviderImpl implements AuthUserCommandV2Provider {

     @Resource
     private AuthUserServiceV2 authUserServiceV2;

    @Override
    public DubboResponse<UserBase> createUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {
        UserBase user = authUserServiceV2.createUser(systemOriginEnum, userBase, baseUserExtend);
        return DubboResponse.getOK(user);
    }
}
