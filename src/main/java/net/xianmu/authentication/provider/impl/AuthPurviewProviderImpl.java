package net.xianmu.authentication.provider.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthMenuPurview;
import net.xianmu.authentication.client.dto.AuthMenuPurviewDto;
import net.xianmu.authentication.client.input.PurviewWeighVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.AuthPurviewProvider;
import net.xianmu.authentication.common.util.MenuTreeUtils;
import net.xianmu.authentication.service.impl.AuthMenuServiceImpl;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@DubboService
@Component
@Slf4j
public class AuthPurviewProviderImpl implements AuthPurviewProvider {
    @Resource
    AuthMenuServiceImpl authMenuService;


    @Override // ok
    public DubboResponse<List<AuthMenuPurview>> getAuthMenuPurviews(SystemOriginEnum systemOriginEnum, Long tenantId, Long userId) {
        List<AuthMenuPurview> authMenuPurviews = authMenuService.getAuthMenuPurviews(systemOriginEnum, tenantId, userId);
        return DubboResponse.getOK(authMenuPurviews);
    }

    @Override //ok
    public DubboResponse<List<AuthMenuPurview>> getAuthMenuPurviews(SystemOriginEnum systemOriginEnum) {
        List<AuthMenuPurview> authMenuPurviews = authMenuService.getAuthMenuPurviews(systemOriginEnum);
        return DubboResponse.getOK(authMenuPurviews);
    }

    @Override  // --
    public DubboResponse<List<AuthMenuPurviewDto>> getAuthMenuPurviewDto(SystemOriginEnum systemOriginEnum) {
        List<AuthMenuPurview> menuPreviews = authMenuService.getAuthMenuPurviews(systemOriginEnum);
        return DubboResponse.getOK(MenuTreeUtils.convertTree(menuPreviews));
    }

    @Override //ok
    public DubboResponse<Boolean> addPurviews(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthMenuPurview authMenuPurview) {
        log.info("addPurviews param SystemOriginEnum{}  AuthMenuPurview {}", systemOriginEnum.getType(), JSONUtil.toJsonStr(authMenuPurview));
        int i = authMenuService.addPurviews(systemOriginEnum, userId, tenantId, authMenuPurview);
        return DubboResponse.getOK(true);
    }

    @Override  //ok
    public DubboResponse<Boolean> updatePurviews(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthMenuPurview authMenuPurview) {
        log.info("updatePurviews param SystemOriginEnum{}  AuthMenuPurview {}", systemOriginEnum == null ? "" : systemOriginEnum.getType(), JSONUtil.toJsonStr(authMenuPurview));
        int i = authMenuService.updatePurviews(systemOriginEnum, userId, tenantId, authMenuPurview);
        return DubboResponse.getOK(true);
    }

    @Override //ok
    public DubboResponse<Boolean> deletePurviews(SystemOriginEnum systemOriginEnum, Long userId, Long purviewId) {
        log.info("deletePurviews param SystemOriginEnum{}  id {}", systemOriginEnum == null ? "" : systemOriginEnum.getType(), purviewId);
        authMenuService.deletePurviews(systemOriginEnum, userId, purviewId);
        return DubboResponse.getOK(true);
    }

    @Override //ok
    public DubboResponse<Boolean> updateMenusWeigh(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, List<PurviewWeighVO> list) {
        authMenuService.updateMenusWeigh(systemOriginEnum, userId, tenantId, list);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<List<AuthMenuPurviewDto>> getAuthTenantMenuPurviewDto(SystemOriginEnum systemOriginEnum, Long tenantId) {
        List<AuthMenuPurview> authTenantMenuPurview = authMenuService.getAuthTenantMenuPurview(systemOriginEnum, tenantId);
        return DubboResponse.getOK(MenuTreeUtils.convertTree(authTenantMenuPurview));
    }

    @Override
    public DubboResponse<List<AuthMenuPurview>> getAuthTenantMenuPurview(SystemOriginEnum systemOriginEnum, Long tenantId) {
        return DubboResponse.getOK(authMenuService.getAuthTenantMenuPurview(systemOriginEnum, tenantId));
    }

    @Override
    public DubboResponse<List<AuthMenuPurviewDto>> getMenusTreeByMenuIds(SystemOriginEnum systemOriginEnum, Long tenantId, List<Long> menuIds) {
        List<AuthMenuPurviewDto> outs = authMenuService.getMenusTreeByMenuIds(systemOriginEnum, tenantId, menuIds);
        return DubboResponse.getOK(outs);
    }
}
