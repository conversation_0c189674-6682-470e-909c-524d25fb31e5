package net.xianmu.authentication.provider.impl;


import cn.hutool.core.collection.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.input.permission.PermissionQueryVO;
import net.xianmu.authentication.client.provider.PermissionQueryProvider;
import net.xianmu.authentication.client.resp.AuthDatePermissionResp;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.authentication.model.VO.QueryDatePermissionVO;
import net.xianmu.authentication.model.resp.DatePermissionResp;
import net.xianmu.authentication.service.permission.AuthUserPermissionService;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@DubboService
@Component
@Slf4j
public class AuthUserPermissionProviderImpl implements PermissionQueryProvider {
    @Resource
    AuthUserPermissionService authUserPermissionService;

    @Override
    public DubboResponse<List<AuthDatePermissionResp>> queryUserPermission(PermissionQueryVO permissionQueryVO) {
        UserBase userBase = new UserBase();
        userBase.setId(permissionQueryVO.getAuthUserId());
        QueryDatePermissionVO queryDatePermissionVO = new QueryDatePermissionVO();
        queryDatePermissionVO.setType(permissionQueryVO.getType());
        List<DatePermissionResp> datePermissionResps = authUserPermissionService.queryDatePermissionResp(userBase, queryDatePermissionVO);
        long count = datePermissionResps.stream().filter(it -> it.getPermissionValue().equals(AuthGlobal.ALL)).count();
        if (count > 0) {
            datePermissionResps = datePermissionResps.stream().filter(it -> it.getPermissionValue().equals(AuthGlobal.ALL)).collect(Collectors.toList());
        }
        if (CollectionUtil.isEmpty(datePermissionResps)) {
            return DubboResponse.getOK(new ArrayList<>());
        }
        return DubboResponse.getOK(convert(datePermissionResps));
    }

    private List<AuthDatePermissionResp> convert(List<DatePermissionResp> datePermissionResps) {
        return datePermissionResps.stream().map(
                it -> {
                    AuthDatePermissionResp resp = new AuthDatePermissionResp();
                    resp.setPermissionName(it.getPermissionName());
                    resp.setPermissionValue(it.getPermissionValue());
                    resp.setType(it.getType());
                    return resp;
                }
        ).collect(Collectors.toList());
    }
}
