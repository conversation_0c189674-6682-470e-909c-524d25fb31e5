package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.*;
import net.xianmu.authentication.client.provider.AuthUserBinlogProvider;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.xianmu.authentication.service.impl.AuthMenuServiceImpl;
import net.xianmu.authentication.service.impl.AuthRoleServiceImpl;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.authentication.service.impl.WechatServiceImpl;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import net.xianmu.i18n.result.util.I18nDubboResponseUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


@DubboService
@Component
@Slf4j
public class AuthUserBinlogProviderImpl implements AuthUserBinlogProvider {

    @Resource
    AuthUserServiceImpl authUserService;

    @Resource
    AuthRoleServiceImpl authRoleService;

    @Resource
    AuthMenuServiceImpl authMenuService;
    @Resource
    WechatServiceImpl  wechatService;
    @Resource
    AuthUserAuthDao authUserAuthDao;
    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthUserBaseDao authUserBaseDao;
    @Resource
    AuthUserPropertiesExtDao authUserPropertiesExtDao;

    @Override
    public DubboResponse<UserBase> queryUserBase(SystemOriginEnum systemOriginEnum, String userName, Long tenantId) {
        UserBase userBase = authUserService.queryUserBase(systemOriginEnum, userName, tenantId);
        return DubboResponse.getOK(userBase);
    }

    @Override
    public DubboResponse<UserBase> binlogCreateUser(SystemOriginEnum systemOriginEnum, UserBase baseUser) {
        return authUserService.binlogCreateUser(systemOriginEnum, baseUser);
    }

    @Override
    public DubboResponse<Boolean> createUserRole(SystemOriginEnum systemOriginEnum, List<UserRoleInput> userRoleInputs) {
        authRoleService.createUserRole(systemOriginEnum, userRoleInputs);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> createRolePurviews(SystemOriginEnum systemOriginEnum, List<RolePreviewInput> rolePreviewInputs) {
        authMenuService.createRolePurviews(systemOriginEnum, rolePreviewInputs);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> createExtends(SystemOriginEnum systemOriginEnum, List<UserPropertiesExtInput> userPropertiesExtInputs) {
        return wechatService.createExtends(systemOriginEnum, userPropertiesExtInputs);
    }

    @Override
    public DubboResponse<Boolean> createOrUpdateExtends(AuthUserPropertiesExt authUserPropertiesExt) {
        List<AuthUserPropertiesExt> authUserPropertiesExts = authUserPropertiesExtDao.selectValue(authUserPropertiesExt.getUserId(), authUserPropertiesExt.getPropKey());
        if (CollectionUtils.isEmpty(authUserPropertiesExts)){
            authUserPropertiesExt.setCreateTime(new Date());
            authUserPropertiesExtDao.insertSelective(authUserPropertiesExt);
            return DubboResponse.getOK(true);
        }
        authUserPropertiesExtDao.updateValue(authUserPropertiesExt.getUserId(),authUserPropertiesExt.getPropKey(),authUserPropertiesExt.getPropValue());
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> deleteExtends(Long authUserId, String key, String value) {
        authUserPropertiesExtDao.deleteByUserIdKeyValue(authUserId, key, value);
        return DubboResponse.getOK(true);
    }

    @Transactional
    public DubboResponse<Boolean> deletBinLogAdminAuth(Long authUserId, Integer type) {
        authUserAuthDao.deleteByAuthIdType(authUserId, type);
        return DubboResponse.getOK(true);
    }


    @Transactional
    @Override
    public DubboResponse<Boolean> addorUpdateAuth(Long authUserId, Integer type, String openid, String unionId) {
        authUserAuthDao.deleteByAuthIdType(authUserId, type);
        AuthUserAuth authUserAuth = new AuthUserAuth();
        authUserAuth.setAuthId(openid);
        authUserAuth.setUserId(authUserId);
        authUserAuth.setAuthType(type.byteValue());
        authUserAuth.setThirdPartyId(unionId);
        authUserAuthDao.insertSelective(authUserAuth);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> updateBizUserIdByAuthId(UpdateAuthBizUserInput updateAuthBizUserInput) {
        if (updateAuthBizUserInput.getAuthId() == null || updateAuthBizUserInput.getAuthId() < 0
                || updateAuthBizUserInput.getBizUserId() == null) {
            return I18nDubboResponseUtil.getDefaultError("参数错误");
        }
        AuthUser authUser = authUserDao.selectByPrimaryKey(updateAuthBizUserInput.getAuthId());
        if (authUser == null) {
            return I18nDubboResponseUtil.getDefaultError("该用户不存在");
        }
        AuthUser updateAuthUser = new AuthUser();
        updateAuthUser.setId(authUser.getId());
        updateAuthUser.setBizUserId(updateAuthBizUserInput.getBizUserId());
        authUserDao.updateByPrimaryKeySelective(updateAuthUser);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> updateBizUserIdByAuthId(SystemOriginEnum systemOriginEnum, UpdateAuthBizUserInput updateAuthBizUserInput) {
        return updateBizUserIdByAuthId(updateAuthBizUserInput);
    }

    @Override
    @Transactional
    public DubboResponse<Boolean> updatePhone(SystemOriginEnum systemOriginEnum, UpdatePhoneInput updatePhoneInput) {
        String newPhone = updatePhoneInput.getNewPhone();
        String oldPhone = updatePhoneInput.getOldPhone();
        AuthUserBase authUserBase = authUserBaseDao.selectByNameOrigin(oldPhone);
        if (authUserBase == null){
            log.warn("老的账号根据手机号找不到账号 {}",oldPhone);
            return DubboResponse.getOK(true);
        }
        AuthUserBase newUserBase = authUserBaseDao.selectByNameOrigin(newPhone);
        if (newUserBase != null) {
            //删除自己的userBase 禁用手机号
            // authUserBaseDao.deleteByPrimaryKey(authUserBase.getId());
            //将auth_user 的 base_user_id 修改为新的
            authUserDao.updateBaseUserId(authUserBase.getId(), newUserBase.getId());
            return DubboResponse.getOK(true);
        }
        authUserBase.setUsername(newPhone);
        authUserBase.setPhone(newPhone);
        authUserBaseDao.updateByPrimaryKeySelective(authUserBase);
        return DubboResponse.getOK(true);
    }


}
