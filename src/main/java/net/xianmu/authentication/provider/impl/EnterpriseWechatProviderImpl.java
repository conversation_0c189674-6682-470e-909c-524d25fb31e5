package net.xianmu.authentication.provider.impl;


import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.EnterpriseWeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.EnterpriseWechatProvider;
import net.xianmu.authentication.service.wechat.qiye.EnterpriseWechatService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@DubboService
@Component
@Slf4j
public class EnterpriseWechatProviderImpl implements EnterpriseWechatProvider {


    @Resource
    EnterpriseWechatService enterpriseWechatService;


    @Override
    public DubboResponse<String> queryEnterpriseWeChatToken(SystemOriginEnum systemOriginEnum, Long tenantId, EnterpriseWeChatTokenTypeEnum enterpriseWeChatTokenTypeEnum, Boolean update) {
        String accessToken = enterpriseWechatService.getAccessToken(enterpriseWeChatTokenTypeEnum, update);
        return DubboResponse.getOK(accessToken);
    }
}
