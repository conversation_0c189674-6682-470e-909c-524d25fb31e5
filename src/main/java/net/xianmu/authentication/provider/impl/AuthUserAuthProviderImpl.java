package net.xianmu.authentication.provider.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.util.PageInfoHelper;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.binlog.AuthThirdPartyInput;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.provider.AuthUserAuthProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.client.resp.UserAuthBaseResp;
import net.xianmu.authentication.client.resp.UserBaseThirdPartyResp;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.authentication.common.util.PageInfoConverter;
import net.xianmu.authentication.mapper.auth.AuthUserAuthDao;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao;
import net.xianmu.authentication.model.BO.AuthUserAuthBO;
import net.xianmu.authentication.model.DTO.AuthUserAuthDTO;
import net.xianmu.authentication.model.DTO.AuthUserAuthThirdPartyDTO;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.authentication.model.DTO.UserBaseThirdPartyDTO;
import net.xianmu.authentication.model.DTO.convert.UserBaseThirdConvert;
import net.xianmu.authentication.service.WechatService;
import net.xianmu.authentication.service.feishu.FeiShuService;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.i18n.result.util.I18nDubboResponseUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.QW_BD_QR;

@DubboService
@Component
@Slf4j
public class  AuthUserAuthProviderImpl implements AuthUserAuthProvider {
    @Resource
    private WechatService wechatService;
    @Resource
    private AuthUserAuthDao authUserAuthDao;
    @Resource
    private FeiShuService feiShuService;
    @Resource
    private AuthUserBaseDao authUserBaseDao;
    @Resource
    private AuthUserDao authUserDao;
    @Resource
    private AuthUserPropertiesExtDao authUserPropertiesExtDao;

    @Override
    public DubboResponse<AuthUserAuthResp> queryUserAuth(SystemOriginEnum systemOriginEnum, Long userBaseId, Integer type) {
        return DubboResponse.getOK(wechatService.queryUserAuth(systemOriginEnum, userBaseId, type));
    }

    @Override
    public DubboResponse<List<AuthUserAuthResp>> queryUsersAuth(SystemOriginEnum systemOriginEnum, List<Long> userBaseIds, Integer type) {
        List<AuthUserAuthResp> resp = wechatService.queryUsersAuth(systemOriginEnum, userBaseIds, type);
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<AuthUserAuthResp> queryUserAuthByUserId(SystemOriginEnum systemOriginEnum, String thirdId, Integer type) {
        AuthUserAuthResp resp = wechatService.queryUserAuthByUserId(systemOriginEnum, thirdId, type);
        return DubboResponse.getOK(resp);
    }

    @Override
    public DubboResponse<List<UserAuthBaseResp>> queryThirdPartyBatch(SystemOriginEnum systemOriginEnum, AuthTypeEnum authType, AuthThirdPartyInput thirdPartyInput) {
        //参数校验
        if (thirdPartyInput.getTenantId() == null) {
            thirdPartyInput.setTenantId(AuthGlobal.TENANT_ID);
        }

        if (thirdPartyInput.getAccountType() == null || CollectionUtils.isEmpty(thirdPartyInput.getAccountList())) {
            return I18nDubboResponseUtil.getDefaultError("参数异常");
        }

        List<UserAuthBaseResp> baseRespList = getUserAuthBaseRespList(systemOriginEnum, authType, thirdPartyInput);

        return DubboResponse.getOK(baseRespList);
    }

    @Override
    public DubboResponse<PageInfo<UserBaseThirdPartyResp>> queryPageUserBase(AuthUserAuthQueryInput authUserAuthQueryInput) {
        if (authUserAuthQueryInput.getPageSize() == null || authUserAuthQueryInput.getPageNum()== null ||authUserAuthQueryInput.getPageSize()>500){
            return I18nDubboResponseUtil.getDefaultError("分页参数错误");
        }
        if (authUserAuthQueryInput.getSystemOriginEnum() == null || authUserAuthQueryInput.getTenantId() ==null ||
                authUserAuthQueryInput.getAuthType() == null){
            return I18nDubboResponseUtil.getDefaultError("参数错误");
        }
        PageHelper.startPage(authUserAuthQueryInput.getPageNum(), authUserAuthQueryInput.getPageSize());
        List<UserBaseThirdPartyDTO> authUserAuths = authUserAuthDao.selectByThirdPartyPage(authUserAuthQueryInput.getSystemOriginEnum().getType(),authUserAuthQueryInput.getTenantId(), authUserAuthQueryInput.getAuthType().getType());
        PageInfo<UserBaseThirdPartyDTO> pageInfo = PageInfoHelper.createPageInfo(authUserAuths);
        return DubboResponse.getOK(PageInfoConverter.toPageResp(pageInfo, UserBaseThirdConvert::toUserBaseThirdPartyResp));

    }

    @Override
    public DubboResponse<List<UserBaseThirdPartyResp>> queryUserBaseByThirdBatch(SystemOriginEnum systemOriginEnum, AuthTypeEnum authType,
                                                                                 Long tenantId, List<String> thirdPartyList) {
        if (CollectionUtils.isEmpty(thirdPartyList)) {
            return I18nDubboResponseUtil.getDefaultError("参数异常");
        }

        //查询三方信息
        List<AuthUserAuth> authList = authUserAuthDao.selectByThirdPartyId(authType.getType(), thirdPartyList);
        if (CollectionUtils.isEmpty(authList)) {
            return DubboResponse.getOK();
        }

        //查询bizUserId
        if (tenantId == null){
            tenantId = AuthGlobal.TENANT_ID;
        }
        List<Long> userIdList = authList.stream().map(AuthUserAuth::getUserId).collect(Collectors.toList());
        List<AuthUser> authUserList = authUserDao.selectByUserIdAndOrigin(systemOriginEnum.getType(), tenantId, userIdList, null);

        List<Long> baseUserIdList = new ArrayList<>();
        Map<Long, AuthUser> authUserMap = authUserList.stream()
                .peek(el -> baseUserIdList.add(el.getUserBaseId()))
                .collect(Collectors.toMap(AuthUser::getId, el -> el));

        //查询账号基础信息
        List<AuthUserBase> userBaseList = authUserBaseDao.selectByIds(baseUserIdList);
        Map<Long, AuthUserBase> userBaseMap = userBaseList.stream().collect(Collectors.toMap(AuthUserBase::getId, el -> el));

        //组装结果数据
        List<UserBaseThirdPartyResp> res = authList.stream().map(el -> {
            UserBaseThirdPartyResp resp = new UserBaseThirdPartyResp();
            resp.setBaseUserId(el.getUserId());
            resp.setAuthId(el.getAuthId());
            resp.setAuthType(el.getAuthType());
            resp.setThirdPartyId(el.getThirdPartyId());

            if (authUserMap.containsKey(el.getUserId())) {
                AuthUser authUser = authUserMap.get(el.getUserId());
                resp.setBizUserId(authUser.getBizUserId());

                if (userBaseMap.containsKey(authUser.getUserBaseId())) {
                    AuthUserBase userBase = userBaseMap.get(authUser.getUserBaseId());
                    resp.setNickName(userBase.getNickname());
                    resp.setPhone(userBase.getPhone());
                }
            }
            return resp;
        }).collect(Collectors.toList());

        return DubboResponse.getOK(res);
    }

    @Override
    public DubboResponse<List<AuthUserAuthResp>> queryAuthUserAuthByInput(AuthUserAuthQueryInput authUserAuthQueryInput) {
        List<Long> bizUserIds = authUserAuthQueryInput.getBizUserIds();
        if (CollectionUtils.isEmpty(bizUserIds)){
            return I18nDubboResponseUtil.getDefaultError("bizUserIds 不能为空");
        }
        Long tenantId = authUserAuthQueryInput.getTenantId();
        if (tenantId == null){
            return I18nDubboResponseUtil.getDefaultError("tenantId 不能为空");
        }
        SystemOriginEnum systemOriginEnum = authUserAuthQueryInput.getSystemOriginEnum();
        if (systemOriginEnum == null){
            return I18nDubboResponseUtil.getDefaultError("系统来源不能为空");
        }

        AuthTypeEnum authType = authUserAuthQueryInput.getAuthType();
        if (authType == null){
            return I18nDubboResponseUtil.getDefaultError("AuthTypeEnum 不能为空");
        }
        // bizId 转userId
        List<AuthUserAuthBO> authUserAuths = authUserAuthDao.selectByBizIdSourceTenantIdType(systemOriginEnum.getType(), tenantId, bizUserIds, authType.getType());

        // 转换补充qr
        return DubboResponse.getOK(convertAuthResp(authUserAuths, authType));
    }

    @Override
    public DubboResponse<List<AuthUserAuthResp>> queryAuthUserAuthByAuthId(AuthUserAuthQueryInput authUserAuthQueryInput) {
        SystemOriginEnum systemOriginEnum = authUserAuthQueryInput.getSystemOriginEnum();
        if (systemOriginEnum == null){
            return I18nDubboResponseUtil.getDefaultError("系统来源不能为空");
        }
        AuthTypeEnum authType = authUserAuthQueryInput.getAuthType();
        if (authType == null){
            return I18nDubboResponseUtil.getDefaultError("AuthTypeEnum 不能为空");
        }
        if (StringUtils.isEmpty(authUserAuthQueryInput.getOpenId()) &&
                StringUtils.isEmpty(authUserAuthQueryInput.getMpOpenId()) &&StringUtils.isEmpty(authUserAuthQueryInput.getUnionid())){
            return I18nDubboResponseUtil.getDefaultError("openid/unionId/mpOpenId不能为空");
        }
        List<AuthUserAuthBO>  list  = new ArrayList<>();
        if (!StringUtils.isEmpty(authUserAuthQueryInput.getOpenId()) || !StringUtils.isEmpty(authUserAuthQueryInput.getMpOpenId())){
            //根据type去
            String openid = StringUtils.isEmpty(authUserAuthQueryInput.getOpenId())?authUserAuthQueryInput.getMpOpenId():authUserAuthQueryInput.getOpenId();
            list.addAll(authUserAuthDao.selectBOByOpenIdType(systemOriginEnum.getType(),authType.getType(), openid));
        }else {
            list.addAll(authUserAuthDao.selectBOByThirdIdType(systemOriginEnum.getType(),authType.getType(), authUserAuthQueryInput.getUnionid()));
        }
        List<AuthUserAuthResp> authUserAuthResps = list.stream().map(
                it -> {
                    AuthUserAuthResp authUserAuthResp = new AuthUserAuthResp();
                    authUserAuthResp.setAuthType(it.getAuthType().intValue());
                    authUserAuthResp.setBizUserId(it.getBizUserId());
                    authUserAuthResp.setAuthId(it.getAuthId());
                    authUserAuthResp.setThirdPartyId(it.getThirdPartyId());
                    return authUserAuthResp;
                    // authUserAuthResp.s
                }
        ).collect(Collectors.toList());
        return DubboResponse.getOK(authUserAuthResps);
    }


    private List<AuthUserAuthResp> convertAuthResp(List<AuthUserAuthBO> authUserAuths, AuthTypeEnum authType) {
        if (CollectionUtils.isEmpty(authUserAuths)){
            return new ArrayList<>();
        }

        Map<Long, String> qrMap = new HashMap<>(authUserAuths.size());
        if (Objects.equals(authType.getType(), AuthTypeEnum.ENTERPRISE_WE_CHAT.getType())) {
            List<Long> userIds = authUserAuths.stream().map(AuthUserAuthBO::getUserId).collect(Collectors.toList());
            List<AuthUserPropertiesExt> authUserPropertiesExts = authUserPropertiesExtDao.selectByUserIdsAndKey(userIds, QW_BD_QR);
            Map<Long, String> collect = authUserPropertiesExts.stream().collect(Collectors.toMap(AuthUserPropertiesExt::getUserId, AuthUserPropertiesExt::getPropValue, (o1, o2) -> o1));
            qrMap.putAll(collect);
        }
        return authUserAuths.stream().map(
                it -> {
                    AuthUserAuthResp resp = new AuthUserAuthResp();
                    resp.setAuthType(Integer.valueOf(it.getAuthType()));
                    resp.setAuthId(it.getAuthId());
                    resp.setThirdPartyId(it.getThirdPartyId());
                    resp.setUserId(it.getUserId());
                    resp.setBizUserId(it.getBizUserId());
                    resp.setQr(qrMap.get(it.getUserId()));
                    return resp;
                }
        ).collect(Collectors.toList());

    }


    /**
     * 查询thirdPartId，auth是飞书时先查库、库里没有的数据调用飞书api补充
     * @param systemOriginEnum 系统来源
     * @param authType 认证类型
     * @param thirdPartyInput 参数
     * @return rpc响应
     */
    private List<UserAuthBaseResp> getUserAuthBaseRespList(SystemOriginEnum systemOriginEnum, AuthTypeEnum authType, AuthThirdPartyInput thirdPartyInput) {
        //根据不同参数查询数据库
        List<AuthUserAuthDTO> dtoList = authUserAuthDao.batchQueryByType(systemOriginEnum.getType(), authType.getType(),
                thirdPartyInput.getTenantId(),thirdPartyInput.getAccountType().getAccountType(), thirdPartyInput.getAccountList());

        //未查到飞书userId的数据再调用一次api
        boolean isFeiShu = authType.equals(AuthTypeEnum.FEI_SHU);

        //处理数据
        Map<String, AuthUserAuthDTO> queryMap = new HashMap<>();
        Map<String, AuthUserAuthDTO> noUserIdMap = new HashMap<>();
        for (AuthUserAuthDTO dto : dtoList) {
            String key = null;
            switch (thirdPartyInput.getAccountType()) {
                case BASE_USER_ID:
                    key = String.valueOf(dto.getBaseUserId());
                    break;
                case BIZ_USER_ID:
                    key = String.valueOf(dto.getBizUserId());
                    break;
                case PHONE:
                    key = dto.getPhone();
                    break;
                default:
                    break;
            }

            if (isFeiShu && dto.getThirdPartyId() == null) {
                noUserIdMap.put(key, dto);
                continue;
            }

            queryMap.put(key, dto);
        }

        if (!CollectionUtils.isEmpty(noUserIdMap)) {
            Map<String, AuthUserAuthDTO> temMap = queryFeiShuUserId(noUserIdMap);
            queryMap.putAll(temMap);
        }

        //构造返回数据、带上查询account参数、以供调用方匹配数据
        List<UserAuthBaseResp> baseRespList = new ArrayList<>();
        for (String account : thirdPartyInput.getAccountList()) {
            UserAuthBaseResp resp = new UserAuthBaseResp();
            resp.setQueryAccount(account);

            if (!queryMap.containsKey(account)) {
                continue;
            }

            AuthUserAuthDTO authUserAuthDTO = queryMap.get(account);
            resp.setNickName(authUserAuthDTO.getNickname());
            resp.setThirdPartyId(authUserAuthDTO.getThirdPartyId());

            baseRespList.add(resp);
        }

        return baseRespList;
    }

    /**
     * 处理未查到飞书userId的数据
     * @param paramMap 参数
     * @return userId
     */
    private Map<String, AuthUserAuthDTO> queryFeiShuUserId(Map<String, AuthUserAuthDTO> paramMap) {

        //记录baseUserId和dto映射关系，更新飞书api返回的userId
        Map<Long, AuthUserAuthDTO> temp = new HashMap<>();

        List<AuthUserPhoneDTO> baseList = new ArrayList<>();
        for (AuthUserAuthDTO val : paramMap.values()) {
            AuthUserPhoneDTO phoneDTO = new AuthUserPhoneDTO();
            phoneDTO.setUserBaseId(val.getBaseUserId());
            phoneDTO.setPhone(val.getPhone());
            phoneDTO.setId(val.getAuthUserId());
            baseList.add(phoneDTO);

            temp.put(val.getBaseUserId(), val);
        }

        List<AuthUserAuthThirdPartyDTO> auths = feiShuService.batchUpdateUserId(baseList);
        auths.forEach(el -> {
            AuthUserAuthDTO authUserAuthDTO = temp.get(el.getBaseUserId());
            authUserAuthDTO.setThirdPartyId(el.getThirdPartyId());
        });

        return paramMap;
    }
}
