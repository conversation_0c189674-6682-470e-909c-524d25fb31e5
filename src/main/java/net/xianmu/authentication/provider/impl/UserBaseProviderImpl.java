package net.xianmu.authentication.provider.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.WeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.AuthRoleQueryVO;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.authentication.client.input.PurviewWeighVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.authentication.client.provider.AuthUserProvider;
import net.xianmu.authentication.common.contexts.Global;
import net.xianmu.authentication.common.enmu.LoginTypeEnum;
import net.xianmu.authentication.common.util.MenuTreeUtils;
import net.xianmu.authentication.mapper.auth.*;
import net.xianmu.authentication.model.entity.AuthWechatRelation;
import net.xianmu.authentication.service.AuthUserService;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.authentication.model.VO.AuthLoginVO;
import net.xianmu.authentication.service.impl.AuthLoginServiceImpl;
import net.xianmu.authentication.service.impl.AuthMenuServiceImpl;
import net.xianmu.authentication.service.impl.AuthRoleServiceImpl;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.authentication.service.wechat.impl.WxServiceImpl;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.util.StrUtil.isNotBlank;
import static net.xianmu.authentication.common.AuthGlobal.TENANT_ID;

@DubboService
@Component
@Slf4j
public class UserBaseProviderImpl implements AuthUserProvider {
    @Resource
    AuthUserServiceImpl authUserService;
    @Resource
    AuthRoleServiceImpl authRoleService;
    @Resource
    AuthMenuServiceImpl authMenuService;
    @Resource
    AuthUserPropertiesExtDao authUserPropertiesExtDao;
    @Resource
    private AuthLoginServiceImpl loginService;
    @Resource
    private AuthUserBaseDao authUserBaseDao;
    @Resource
    private AuthUserDao authUserDao;
    @Resource(name = "authRedisTemplate")
    RedisTemplate redisTemplate;
    @Resource
    AuthWechatRelationMapper authWechatRelationMapper;
    @Resource
    private AuthUserAuthDao authUserAuthDao;
    @Resource
    private WxServiceImpl wxService;

    @Override //ok
    public DubboResponse<UserBase> createUser(SystemOriginEnum systemOriginEnum, UserBase userBase) {
        log.info("createUser param SystemOriginEnum{}  UserBase{}", JSONUtil.toJsonStr(systemOriginEnum.getType()), JSONUtil.toJsonStr(userBase));
        UserBase user = authUserService.createUser(systemOriginEnum, userBase, false);
        return DubboResponse.getOK(user);
    }

    @Override
    public DubboResponse<UserBase> createUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {
        UserBase user = authUserService.createUser(systemOriginEnum, userBase, baseUserExtend);
        return DubboResponse.getOK(user);
    }

    @Override
    public DubboResponse<ShiroUser> shiroUser(String token) {
        Object o = redisTemplate.opsForValue().get(token);
        if (o == null) {
            throw new BizException("token 已失效");
        }
        ShiroUser shiroUser = JSONUtil.toBean(o.toString(), ShiroUser.class);
        return DubboResponse.getOK(shiroUser);
    }

    @Override //ok
    public DubboResponse<UserBase> createUser(SystemOriginEnum systemOriginEnum, UserBase userBase, Boolean oldUser) {
        UserBase user = authUserService.createUser(systemOriginEnum, userBase, oldUser);
        log.info("createUser param SystemOriginEnum{}  UserBase{}", JSONUtil.toJsonStr(systemOriginEnum.getType()), JSONUtil.toJsonStr(userBase));
        return DubboResponse.getOK(user);
    }

    @Override
    public DubboResponse<AuthLoginDto> loginByPhone(SystemOriginEnum systemOriginEnum, Long tenantId, String phone) {
        log.info("登陆参数 手机免密登陆  systemOriginEnum:{} tenantId:{}  phone {}", systemOriginEnum.getType(), tenantId, phone);
        AuthLoginVO authLoginVO = new AuthLoginVO();
        authLoginVO.setTenantId(tenantId);
        authLoginVO.setPhone(phone);
        authLoginVO.setType(LoginTypeEnum.PHONE_PWD.name);
        authLoginVO.setOrigin(systemOriginEnum.getType());
        authLoginVO.setSuperMan(true);
        authLoginVO.setPassword("password");
        return DubboResponse.getOK(loginService.login(authLoginVO).getData());
    }

    @Override
    public DubboResponse<AuthLoginDto> loginByUsername(SystemOriginEnum systemOriginEnum, Long tenantId, String username) {
        log.info("登陆参数 用户名免密登陆  systemOriginEnum:{} tenantId:{}  username {}", systemOriginEnum.getType(), tenantId, username);
        AuthLoginVO authLoginVO = new AuthLoginVO();
        authLoginVO.setTenantId(tenantId);
        authLoginVO.setUsername(username);
        authLoginVO.setType(LoginTypeEnum.NAME_PWD.name);
        authLoginVO.setOrigin(systemOriginEnum.getType());
        authLoginVO.setSuperMan(true);
        authLoginVO.setPassword("password");
        return DubboResponse.getOK(loginService.login(authLoginVO).getData());
    }

    @Override//OK
    public DubboResponse<AuthLoginDto> loginBypwd(SystemOriginEnum systemOriginEnum, Long tenantId, String phone, String pwd) {
        AuthLoginVO authLoginVO = new AuthLoginVO();
        authLoginVO.setTenantId(tenantId);
        authLoginVO.setPassword(pwd);
        authLoginVO.setPhone(phone);
        authLoginVO.setType(LoginTypeEnum.PHONE_PWD.name);
        authLoginVO.setOrigin(systemOriginEnum.getType());
        return DubboResponse.getOK(loginService.login(authLoginVO).getData());
    }

    @Override //ok
    public DubboResponse<Boolean> checkPhonePassword(SystemOriginEnum systemOriginEnum, String phone, String pwd) {
        return DubboResponse.getOK(authUserService.checkPhonePassword(systemOriginEnum, phone, pwd));
    }

    @Override //ok
    public DubboResponse<Boolean> checkUserNamePassword(SystemOriginEnum systemOriginEnum, String userName, String pwd) {
        return DubboResponse.getOK(authUserService.checkUserNamePassword(systemOriginEnum, userName, pwd));
    }

    @Override //ok
    public DubboResponse<UserBase> updateUser(SystemOriginEnum systemOriginEnum, UserBase userBase) {
        UserBase user = authUserService.updateUser(systemOriginEnum, userBase);
        return DubboResponse.getOK(user);
    }

    @Override
    public DubboResponse<UserBase> updatePasswordByPhone(SystemOriginEnum systemOriginEnum, String phone, String password) {
        log.info("updatePasswordByPhone param SystemOriginEnum{}  phone{}", JSONUtil.toJsonStr(systemOriginEnum.getType()), phone);
        throw new BizException("废弃接口调用 updatePasswordByPhone");
    }

    @Override
    public DubboResponse<String> queryDingTalkUserId(SystemOriginEnum systemOriginEnum, Long userId) {
        List<AuthUserPropertiesExt> userPropertiesExts = authUserPropertiesExtDao.selectValue(userId, Global.DING_TALK_USERID);
        String result = CollectionUtils.isEmpty(userPropertiesExts) ? null : userPropertiesExts.get(0).getPropValue();
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<List<String>> queryWechatOpenIdByUserId(SystemOriginEnum systemOriginEnum, Long bizId) {
        AuthUser authUser = authUserDao.selectByBizUserId(systemOriginEnum.getType(), bizId);
        if (authUser == null){
            log.error("用户数据异常 systemOriginEnum{} bizId{}", systemOriginEnum.getType(), bizId);
            throw new BizException("用户数据异常");
        }
        List<AuthUserPropertiesExt> propertiesValues = authUserPropertiesExtDao.selectValue(authUser.getId(), Global.WECHAT_OFFICIAL_PUSH_INFO);
        List<String> openIds = propertiesValues.stream().filter(e -> StrUtil.isNotBlank(e.getPropValue())).map(AuthUserPropertiesExt::getWxOpenId).collect(Collectors.toList());
        List<String> collect = openIds.stream().filter(StrUtil::isNotBlank).collect(Collectors.toList());
        return DubboResponse.getOK(collect);
    }

    @Override
    public DubboResponse<PageInfo<AuthRoleDTO>> roleList(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleQueryVO authRoleQueryVO) {
        PageInfo<AuthRoleDTO> authRolePageInfo = authRoleService.roleList(systemOriginEnum, userId, tenantId, authRoleQueryVO);
        return DubboResponse.getOK(authRolePageInfo);
    }

    @Override
    public DubboResponse<AuthRoleDetailsDTO> roleDetail(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, Long roleId) {
        AuthRoleDetailsDTO authRoleDetailsDTO = authRoleService.roleDetail(systemOriginEnum, userId, tenantId, roleId);
        return DubboResponse.getOK(authRoleDetailsDTO);
    }

    @Override //ok
    public DubboResponse<Boolean> updateRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleUpdateVO authRoleUpdateVO) {
        log.info("updateRole param SystemOriginEnum{} userId{} tenantId{}  role{}", JSONUtil.toJsonStr(systemOriginEnum.getType()), userId, tenantId, JSONUtil.toJsonStr(authRoleUpdateVO));
        int i = authRoleService.updateRole(systemOriginEnum, userId, tenantId, authRoleUpdateVO);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<AuthRole> addRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleUpdateVO authRoleUpdateVO) {
        log.info("addRole param SystemOriginEnum{}  role{}", JSONUtil.toJsonStr(systemOriginEnum.getType()), authRoleUpdateVO);
        AuthRole authRole = authRoleService.addRole(systemOriginEnum, userId, tenantId, authRoleUpdateVO);
        return DubboResponse.getOK(authRole);
    }

    @Override  //ok
    public DubboResponse<Boolean> deleteRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, Long roleId) {
        log.info("deleteRole param SystemOriginEnum{}  roleId{}", systemOriginEnum.getType(), roleId);
        int i = authRoleService.deleteRole(systemOriginEnum, userId, tenantId, roleId);
        return DubboResponse.getOK(true);
    }

    @Override // ok
    public DubboResponse<List<AuthMenuPurview>> getAuthMenuPurviews(SystemOriginEnum systemOriginEnum, Long tenantId, Long userId) {
        List<AuthMenuPurview> authMenuPurviews = authMenuService.getAuthMenuPurviews(systemOriginEnum, tenantId, userId);
        return DubboResponse.getOK(authMenuPurviews);
    }

    @Override //ok
    public DubboResponse<List<AuthMenuPurview>> getAuthMenuPurviews(SystemOriginEnum systemOriginEnum) {
        List<AuthMenuPurview> authMenuPurviews = authMenuService.getAuthMenuPurviews(systemOriginEnum);
        return DubboResponse.getOK(authMenuPurviews);
    }

    @Override  // --
    public DubboResponse<List<AuthMenuPurviewDto>> getAuthMenuPurviewDto(SystemOriginEnum systemOriginEnum) {
        List<AuthMenuPurview> menuPreviews = authMenuService.getAuthMenuPurviews(systemOriginEnum);
        return DubboResponse.getOK(MenuTreeUtils.convertTree(menuPreviews));
    }

    @Override //ok
    public DubboResponse<Boolean> addPurviews(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthMenuPurview authMenuPurview) {
        log.info("addPurviews param SystemOriginEnum{}  AuthMenuPurview {}", systemOriginEnum.getType(), JSONUtil.toJsonStr(authMenuPurview));
        int i = authMenuService.addPurviews(systemOriginEnum, userId, tenantId, authMenuPurview);
        return DubboResponse.getOK(true);
    }

    @Override  //ok
    public DubboResponse<Boolean> updatePurviews(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthMenuPurview authMenuPurview) {
        log.info("updatePurviews param SystemOriginEnum{}  AuthMenuPurview {}", systemOriginEnum == null ? "" : systemOriginEnum.getType(), JSONUtil.toJsonStr(authMenuPurview));
        int i = authMenuService.updatePurviews(systemOriginEnum, userId, tenantId, authMenuPurview);
        return DubboResponse.getOK(true);
    }

    @Override //ok
    public DubboResponse<Boolean> deletePurviews(SystemOriginEnum systemOriginEnum, Long userId, Long purviewId) {
        log.info("deletePurviews param SystemOriginEnum{}  id {}", systemOriginEnum == null ? "" : systemOriginEnum.getType(), purviewId);
        authMenuService.deletePurviews(systemOriginEnum, userId, purviewId);
        return DubboResponse.getOK(true);
    }

    @Override //ok
    public DubboResponse<Boolean> updateMenusWeigh(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, List<PurviewWeighVO> list) {
        authMenuService.updateMenusWeigh(systemOriginEnum, userId, tenantId, list);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> createUserExtProperties(Long userId, String propKey, String propValue) {
        AuthUserPropertiesExt record = new AuthUserPropertiesExt();
        authUserPropertiesExtDao.insertSelective(record);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> updateWechatOpenId(String openId, String unionId) {
        if (StrUtil.isBlank(openId)) {
            throw new DefaultServiceException("openId不能为空");
        }
        wxService.addList(SystemOriginEnum.SRM,
                WeChatTokenTypeEnum.SRM_ACCESS_TOKEN, AuthTypeEnum.OFFICIAL_WE_CHAT,Collections.singletonList(openId));
        return DubboResponse.getOK(true);
    }

    private List<String> queryWechatOpenIdByBizUserId(AuthUserPropertiesExt authUserPropertiesExt ){
        if (authUserPropertiesExt != null) {
            String wxOpenId;
            String wxUnionid;
            try {
                wxOpenId = authUserPropertiesExt.getWxOpenId();
            } catch (Exception e) {
                wxOpenId = null;
            }
            if (wxOpenId != null) {
                return Collections.singletonList(wxOpenId);
            }
            try {
                wxUnionid = authUserPropertiesExt.getWxUnionId();
            } catch (Exception e) {
                wxUnionid = null;
            }
            if (wxUnionid != null) {
                String resultOpenId = authWechatRelationMapper.selectBySourceTenantIdAuthTypeUnionid(SystemOriginEnum.SRM.getType(), TENANT_ID, AuthTypeEnum.OFFICIAL_WE_CHAT.getType(), wxUnionid);
                if (!StringUtils.isEmpty(resultOpenId)) {
                    return Collections.singletonList(resultOpenId);
                }
            }
        }
       return new ArrayList<>();
    }
    @Override
    public DubboResponse<List<String>> queryWechatOpenIdByBizUserId(SystemOriginEnum systemOriginEnum, Long bizId) {
        AuthUser authUser = authUserDao.selectByBizUserId(systemOriginEnum.getType(), bizId);
        if (authUser == null) {
            throw new BizException("用户数据错误");
        }
        List<AuthUserPropertiesExt> propertiesValues = authUserPropertiesExtDao.selectValue(authUser.getId(), "WECHAT_OFFICIAL_PUSH_INFO");
        if (!CollectionUtils.isEmpty(propertiesValues)){
            AuthUserPropertiesExt authUserPropertiesExt = propertiesValues.size() == 1 ? propertiesValues.get(0) : propertiesValues.stream().filter(it -> it.getUpdateTime() != null).max(Comparator.comparing(AuthUserPropertiesExt::getUpdateTime)).orElse(null);
            List<String> openids = queryWechatOpenIdByBizUserId(authUserPropertiesExt);
            if (!CollectionUtils.isEmpty(openids)){
                return DubboResponse.getOK(openids);
            }
        }
        String unionid = null;
        String openId = null;
        //获取微信小程序的 auth信息
        Optional<AuthUserAuth> authUserAuth = authUserAuthDao.selectUserAuthRecord(AuthTypeEnum.WEI_CHAT.getType(), authUser.getId()).stream().max(Comparator.comparing(AuthUserAuth::getUpdateTime));

        if (authUserAuth.isPresent() && !StringUtils.isEmpty(authUserAuth.get().getThirdPartyId())) {
            //只有untionId
            unionid = authUserAuth.get().getThirdPartyId();
            openId = authUserAuth.get().getAuthId();
            if (Objects.equals(unionid, openId)) {
                unionid = null;
            }
        }
        if (StringUtils.isEmpty(unionid)){
            return DubboResponse.getOK(new ArrayList<>());
        }
        //根据unionid 去查询openId
        String resultOpenId = authWechatRelationMapper.selectBySourceTenantIdAuthTypeUnionid(SystemOriginEnum.SRM.getType(), TENANT_ID, AuthTypeEnum.OFFICIAL_WE_CHAT.getType(), unionid);
        if (!StringUtils.isEmpty(resultOpenId)){
            return DubboResponse.getOK(Collections.singletonList(resultOpenId));
        }
        if (!StringUtils.isEmpty(openId)) {
          //再次做下懒加载
            List<AuthWechatRelation> authWechatRelations = wxService.addList(systemOriginEnum, 
                    WeChatTokenTypeEnum.SRM_ACCESS_TOKEN, AuthTypeEnum.OFFICIAL_WE_CHAT,Collections.singletonList(openId));
            if (!CollectionUtils.isEmpty(authWechatRelations)){
                return DubboResponse.getOK(Collections.singletonList(authWechatRelations.get(0).getOpenid()));
            }
        }
        return DubboResponse.getOK(Collections.EMPTY_LIST);
    }

    @Override
    public DubboResponse<Boolean> toPayAttention(String unionId) {
        if (StrUtil.isBlank(unionId)) {
            throw new DefaultServiceException("unionId不能为空");
        } else {
            AuthUserPropertiesExt userPropertiesExt = authUserPropertiesExtDao.selectByValue("WECHAT_OFFICIAL_PUSH_INFO", unionId);
            if (userPropertiesExt == null) {
                throw new DefaultServiceException("该账号扩展属性异常");
            }
            return DubboResponse.getOK(userPropertiesExt.ifPayAttention());
        }
    }

    @Override
    public DubboResponse<List<Long>> getUserIdListByRoleId(Long roleId) {
        log.info("根据角色id查询用户信息 getUserIdListByRoleId {}", roleId);
        if (roleId == null || roleId < 0) {
            return DubboResponse.getOK(new ArrayList<>());
        }
        List<Long> roleIds = authRoleService.getUserIdListByRoleId(Collections.singletonList(roleId));
        return DubboResponse.getOK(roleIds);
    }

    @Override
    public DubboResponse<List<Long>> getUserIdListByRoleIds(List<Long> roleIds) {
        log.info("根据角色roleIds查询用户信息 getUserIdListByRoleIds {}", roleIds);
        if (CollectionUtils.isEmpty(roleIds)) {
            return DubboResponse.getOK(new ArrayList<>());
        }
        List<Long> authIds = authRoleService.getUserIdListByRoleId(roleIds);
        return DubboResponse.getOK(authIds);
    }

    @Override
    public DubboResponse<List<AuthUserRoleDto>> getUserRoleByUserList(List<Long> userIdList) {
        log.info("根据用户id查询角色信息 getUserRoleByUserList {}", JSONUtil.toJsonStr(userIdList));
        if (CollectionUtils.isEmpty(userIdList)) {
            return DubboResponse.getOK(new ArrayList<>());
        }
        List<AuthUserRoleDto> outs = authRoleService.getUserRoleByUserList(userIdList);
        return DubboResponse.getOK(outs);
    }
    @Override
    /**
     * 接口不适用saas
     */
    public DubboResponse<AuthUser> geAuthBySystemOriginEnumAserBaseId(SystemOriginEnum systemOriginEnum, Long baseUserId) {
        AuthUser authUser = authUserService.getAuthUserByBaseUserIdSystem(baseUserId, systemOriginEnum.getType());
        return DubboResponse.getOK(authUser);
    }

    @Override
    public DubboResponse<List<Long>> getUserBaseIdsBySourceRoleIds(SystemOriginEnum systemOriginEnum, List<Long> roleIds) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return DubboResponse.getOK(new ArrayList<>());
        }
        List<Long> userBaseIds = authUserBaseDao.selectUserBaseIdsBySourceRoleIds(systemOriginEnum.getType(), roleIds);
        return DubboResponse.getOK(userBaseIds);
    }

}
