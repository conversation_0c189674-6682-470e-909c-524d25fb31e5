package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthLoginDto;
import net.xianmu.authentication.client.dto.login.AuthUserCommonLoginVO;
import net.xianmu.authentication.client.provider.AuthUserLoginV2Provider;
import net.xianmu.authentication.common.convert.UserLoginConverter;
import net.xianmu.authentication.model.VO.AuthLoginVO;
import net.xianmu.authentication.service.AuthLoginService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/4/23 15:18
 */


@DubboService
@Component
@Slf4j
public class AuthUserLoginV2ProviderImpl implements AuthUserLoginV2Provider {

    @Resource
    private AuthLoginService loginService;

    @Override
    public DubboResponse<AuthLoginDto> commonLogin(AuthUserCommonLoginVO authUserCommonLoginVO) {
        AuthLoginVO authLoginVO = UserLoginConverter.toAuthLoginVO(authUserCommonLoginVO);
        return DubboResponse.getOK(loginService.login(authLoginVO).getData());
    }
}
