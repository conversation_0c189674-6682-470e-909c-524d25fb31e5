package net.xianmu.authentication.provider.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthRoleDTO;
import net.xianmu.authentication.client.dto.AuthRoleDetailsDTO;
import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.client.input.AuthRoleQueryVO;
import net.xianmu.authentication.client.input.AuthRoleUpdateVO;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.provider.AuthRoleProvider;
import net.xianmu.authentication.mapper.auth.AuthRoleDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.service.impl.AuthRoleServiceImpl;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@DubboService
@Component
@Slf4j
public class AuthRoleProviderImpl implements AuthRoleProvider {
    @Resource
    AuthRoleServiceImpl authRoleService;
    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthRoleDao authRoleDao;


    @Override
    public DubboResponse<PageInfo<AuthRoleDTO>> roleList(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleQueryVO authRoleQueryVO) {
        PageInfo<AuthRoleDTO> authRolePageInfo = authRoleService.roleList(systemOriginEnum, userId, tenantId, authRoleQueryVO);
        return DubboResponse.getOK(authRolePageInfo);
    }


    @Override
    public DubboResponse<AuthRoleDetailsDTO> roleDetail(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, Long roleId) {
        AuthRoleDetailsDTO authRoleDetailsDTO = authRoleService.roleDetail(systemOriginEnum, userId, tenantId, roleId);
        return DubboResponse.getOK(authRoleDetailsDTO);
    }

    @Override //ok
    public DubboResponse<Boolean> updateRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleUpdateVO authRoleUpdateVO) {
        log.info("updateRole param SystemOriginEnum{} userId{} tenantId{}  role{}", JSONUtil.toJsonStr(systemOriginEnum.getType()), userId, tenantId, JSONUtil.toJsonStr(authRoleUpdateVO));
        int i = authRoleService.updateRole(systemOriginEnum, userId, tenantId, authRoleUpdateVO);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<AuthRole> addRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, AuthRoleUpdateVO authRoleUpdateVO) {
        log.info("addRole param SystemOriginEnum{}  role{}", JSONUtil.toJsonStr(systemOriginEnum.getType()), authRoleUpdateVO);
        AuthRole authRole = authRoleService.addRole(systemOriginEnum, userId, tenantId, authRoleUpdateVO);
        return DubboResponse.getOK(authRole);
    }

    @Override  //ok
    public DubboResponse<Boolean> deleteRole(SystemOriginEnum systemOriginEnum, Long userId, Long tenantId, Long roleId) {
        log.info("deleteRole param SystemOriginEnum{}  roleId{}",systemOriginEnum.getType(), roleId);
        int i = authRoleService.deleteRole(systemOriginEnum, userId, tenantId, roleId);
        return DubboResponse.getOK(true);
    }

    @Override
    public  DubboResponse<List<AuthRole>> getRoleByOriginBaseUserIdTenantId(SystemOriginEnum systemOriginEnum, Long baseUserId, Long tenantId){
        Long baseTenantId = tenantId == null ? 0 : tenantId;
        AuthUser  authUser = authUserDao.selectByUserBaseId(baseUserId).stream().filter(
                it -> it.getSystemOrigin().equals(systemOriginEnum.getType()) && it.getTenantId().equals(baseTenantId)
        ).findFirst().orElse(null);
        if(authUser ==  null){
            return DubboResponse.getOK();
        }
       return DubboResponse.getOK(authRoleDao.selectByUserId(authUser.getId()));
    }
}
