package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.dto.user.AuthUserLastUpdatePwdTimeResp;
import net.xianmu.authentication.client.input.user.AuthUserBaseQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserQueryInput;
import net.xianmu.authentication.client.input.user.BaseUserUpdateInput;
import net.xianmu.authentication.client.provider.AuthBaseUserProvider;
import net.xianmu.authentication.client.resp.AuthUserBaseResp;
import net.xianmu.authentication.service.AuthUserBaseService;
import net.xianmu.common.enums.base.auth.SystemOriginEnum;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

@DubboService
@Component
@Slf4j
public class AuthBaseUserProviderImpl implements AuthBaseUserProvider {
    @Resource
    AuthUserBaseService authUserBaseService;

    @Override
    public DubboResponse<Boolean> updateUserBase(SystemOriginEnum systemOriginEnum, BaseUserUpdateInput baseUserUpdateInput) {
        return DubboResponse.getOK(authUserBaseService.updateUserBase(systemOriginEnum, baseUserUpdateInput));
    }

    @Override
    public DubboResponse<AuthUserBaseResp> queryBizUserId(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {
        AuthUserBaseResp authUserBaseResp = authUserBaseService.queryBizUserId(systemOriginEnum, authUserQueryInput);
        return DubboResponse.getOK(authUserBaseResp);
    }

    @Override
    public DubboResponse<AuthUserBaseResp> queryRealName(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {
        AuthUserBaseResp authUserBaseResp = authUserBaseService.queryRealName(systemOriginEnum, authUserQueryInput);
        return DubboResponse.getOK(authUserBaseResp);
    }

    @Override
    public DubboResponse<AuthUserBaseResp> queryPassword(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {
        AuthUserBaseResp authUserBaseResp = authUserBaseService.queryPassword(systemOriginEnum, authUserQueryInput);
        return DubboResponse.getOK(authUserBaseResp);
    }

    @Override
    public DubboResponse<AuthUserLastUpdatePwdTimeResp> queryLastUpdatePwdTime(SystemOriginEnum systemOriginEnum, AuthUserQueryInput authUserQueryInput) {
        if (systemOriginEnum == null || StringUtils.isEmpty(authUserQueryInput.getPhone())){
            throw new BizException("参数错误");
        }
        return DubboResponse.getOK(authUserBaseService.queryLastUpdatePwdTime(systemOriginEnum, authUserQueryInput.getPhone()));
    }

    @Override
    public DubboResponse<List<AuthUserBase>>queryAuthUserBase(AuthUserBaseQueryInput authUserQueryInput) {
        return DubboResponse.getOK(authUserBaseService.queryAuthUserBase(authUserQueryInput));
    }
}
