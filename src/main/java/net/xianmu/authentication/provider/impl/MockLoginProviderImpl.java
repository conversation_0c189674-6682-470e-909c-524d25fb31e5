package net.xianmu.authentication.provider.impl;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.login.AuthMockLoginInput;
import net.xianmu.authentication.client.provider.MockLoginProvider;
import net.xianmu.authentication.client.utils.RedisKeyUtils;
import net.xianmu.authentication.common.enmu.LoginTypeEnum;
import net.xianmu.authentication.mapper.auth.AuthRoleDao;
import net.xianmu.authentication.mapper.auth.AuthUserBaseDao;
import net.xianmu.authentication.mapper.auth.AuthUserDao;
import net.xianmu.authentication.model.DTO.ShiroUserExtendDto;
import net.xianmu.authentication.service.AuthLoginService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;



/**
 * <AUTHOR>
 * @date 2023/4/18  16:33
 */
@DubboService
@Component
@Slf4j
public class MockLoginProviderImpl implements MockLoginProvider {
    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthUserBaseDao authUserBaseDao;
    @Resource
    AuthLoginService authLoginService;
    @Resource
    AuthRoleDao authRoleDao;

    @Resource(name = "authRedisTemplate")
    RedisTemplate authRedisTemplate;
    @Override
    public DubboResponse<String> mockLogin(SystemOriginEnum systemOriginEnum, Long bizUserId) {
        AuthUser authUser = authUserDao.selectByBizUserId(systemOriginEnum.getType(), bizUserId);
        if (authUser == null) {
            throw new DefaultServiceException("账号不存在/密码错误");
        }
        AuthUserBase userBase = authUserBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (userBase == null) {
            throw new DefaultServiceException("账号不存在/密码错误");
        }

        net.xianmu.common.enums.base.auth.SystemOriginEnum originByType = net.xianmu.common.enums.base.auth.SystemOriginEnum.getSystemOriginByType(systemOriginEnum.getType());
        AuthLoginDto authLoginDto = authLoginService.mockLogin(userBase, authUser, originByType, LoginTypeEnum.PHONE_PWD);

        String token = authLoginDto.getToken();
        if (token.contains("__")) {
            token = token.split("__")[1];
        }

        return DubboResponse.getOK(token);
    }

    @Override
    public DubboResponse<AuthLoginDto> crossLogin(SystemOriginEnum systemOriginEnum, AuthMockLoginInput authMockLoginInput) {
        if (!systemOriginEnum.getType().equals(SystemOriginEnum.COSFO_OMS.getType())){
            throw new BizException("暂时不支持系统来源");
        }
        if (StringUtils.isEmpty(authMockLoginInput.getAuthUserId())){
            throw new BizException("当前登陆人的authUserId不能为空");
        }
        if (authMockLoginInput.getToTenantId() == null){
            throw new BizException("要切换的租户ID不能为空");
        }


       return DubboResponse.getOK(authLoginService.crossLogin(systemOriginEnum.getType(), authMockLoginInput));
    }
}
