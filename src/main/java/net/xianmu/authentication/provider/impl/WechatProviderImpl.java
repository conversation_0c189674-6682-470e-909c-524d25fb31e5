package net.xianmu.authentication.provider.impl;


import lombok.extern.slf4j.Slf4j;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.enums.WeChatTokenTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.input.wechat.WechatCareQrInput;
import net.xianmu.authentication.client.provider.WechatProvider;
import net.xianmu.authentication.client.resp.AuthUserAuthResp;
import net.xianmu.authentication.service.AuthUserAuthService;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

@DubboService
@Component
@Slf4j
public class WechatProviderImpl implements WechatProvider {

    @Resource
    WxService wxService;
    @Resource
    AuthUserAuthService authUserAuthService;


    @Override
    public DubboResponse<String> queryEnterpriseWeChatToken(SystemOriginEnum systemOriginEnum, Long aLong, WeChatTokenTypeEnum weChatTokenTypeEnum, AuthTypeEnum authTypeEnum) {
        return  DubboResponse.getOK(wxService.getAccessToken(weChatTokenTypeEnum, authTypeEnum));
    }

    @Override
    public DubboResponse<String> queryWeChatToken(String channel) {
        return  DubboResponse.getOK(wxService.getAccessToken(channel));
    }

    @Override
    public DubboResponse<String> queryWeChatTicket(String channel) {
        return DubboResponse.getOK(wxService.getTicket(channel));
    }

    @Override
    public DubboResponse<String> getWxCareQr(WechatCareQrInput wechatCareQrInput) {
        return DubboResponse.getOK(wxService.getWxCareQr(wechatCareQrInput));
    }

    @Override
    public DubboResponse<List<AuthUserAuthResp>> queryUserRespByPhones(AuthUserAuthQueryInput authUserAuthQueryInput) {
        List<AuthUserAuthResp> result  = authUserAuthService.queryUserRespByPhones(authUserAuthQueryInput);
        return DubboResponse.getOK(result);
    }

    @Override
    public DubboResponse<Boolean> bindWechatCare(WechatCareQrInput wechantCareInput) {
        return DubboResponse.getOK(authUserAuthService.bindWechatCare(wechantCareInput));
    }

    @Override
    public DubboResponse<Boolean> closeWechatCare(WechatCareQrInput wechantCareInput) {
        return DubboResponse.getOK(authUserAuthService.closeWechatCare(wechantCareInput));
    }
}
