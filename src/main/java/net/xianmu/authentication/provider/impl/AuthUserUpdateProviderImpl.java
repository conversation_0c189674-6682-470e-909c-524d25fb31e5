package net.xianmu.authentication.provider.impl;

import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserPasswordUpdateInput;
import net.xianmu.authentication.client.input.user.AuthUserUpdateInput;
import net.xianmu.authentication.client.provider.AuthUserUpdateProvider;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.common.result.DubboResponse;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@DubboService
@Component
@Slf4j
public class AuthUserUpdateProviderImpl implements AuthUserUpdateProvider {
    @Resource
    AuthUserServiceImpl authUserService;
    @Override
    public DubboResponse<Boolean> updateAuthUser(SystemOriginEnum systemOriginEnum, AuthUserUpdateInput authUserUpdateInput) {
        if (systemOriginEnum ==null || authUserUpdateInput == null || authUserUpdateInput.getBizId()==null) {
            throw new DefaultServiceException("参数错误");
        }

        authUserService.updateAuthUser(systemOriginEnum, authUserUpdateInput);
        return DubboResponse.getOK(true);
    }

    @Override
    public DubboResponse<Boolean> updateAuthUserPassword(AuthUserPasswordUpdateInput authUserPasswordUpdateInput) {
        authUserService.updateAuthUserPassword(authUserPasswordUpdateInput);
        return DubboResponse.getOK(true);
    }
}
