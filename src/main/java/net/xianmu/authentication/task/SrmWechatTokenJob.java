package net.xianmu.authentication.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/8/10  16:44
 */
@Component
public class SrmWechatTokenJob extends XianMuJavaProcessorV2 {
    @Resource
    private WxService wxService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        wxService.srmAccessTokenTask();

        return new ProcessResult(true);
    }
}
