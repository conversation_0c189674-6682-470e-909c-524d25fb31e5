package net.xianmu.authentication.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.authentication.service.feishu.impl.FeiShuServiceImpl;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/11  16:44
 */
@Component
public class FeiShuTokenJob extends XianMuJavaProcessorV2 {
    @Resource
    private FeiShuServiceImpl feiShuService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        feiShuService.accessTokenTask();

        return new ProcessResult(true);
    }
}
