package net.xianmu.authentication.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

@Component
public class MallWechatTokenJob extends XianMuJavaProcessorV2 {
    @Resource
    WxService wxService;
    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        wxService.mallAccessTokenTask();
        return new  ProcessResult(true);
    }
}
