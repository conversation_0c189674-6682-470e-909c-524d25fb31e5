package net.xianmu.authentication.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.authentication.service.wechat.WxService;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @Description
 * @Date 2025/2/26 16:38
 * @<AUTHOR>
 */
@Component
public class SrmTicketUpdateJob extends XianMuJavaProcessorV2 {

    @Resource
    private WxService wxService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {
        wxService.srmTicketUpdateTask();
        return new  ProcessResult(true);
    }
}
