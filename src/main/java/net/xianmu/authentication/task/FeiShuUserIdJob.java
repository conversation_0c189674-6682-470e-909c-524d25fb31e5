package net.xianmu.authentication.task;

import com.alibaba.schedulerx.worker.processor.ProcessResult;
import javax.annotation.Resource;
import net.xianmu.authentication.service.feishu.impl.FeiShuServiceImpl;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2023/7/11  16:44
 */
@Component
public class FeiShuUserIdJob extends XianMuJavaProcessorV2 {
    @Resource
    private FeiShuServiceImpl feiShuService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        feiShuService.updateFeiShuUserIdTask();

        return new ProcessResult(true);
    }
}
