package net.xianmu.authentication.task;

import com.alibaba.schedulerx.worker.domain.JobContext;
import com.alibaba.schedulerx.worker.processor.ProcessResult;
import net.xianmu.authentication.service.wechat.qiye.EnterpriseWechatService;
import net.xianmu.task.process.XianMuJavaProcessor;
import net.xianmu.task.process.XianMuJavaProcessorV2;
import net.xianmu.task.vo.input.XmJobInput;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/7/11  16:44
 */
@Component
public class WechatEnterpriseThirdIdJob extends XianMuJavaProcessorV2 {
    @Resource
    private EnterpriseWechatService enterpriseWechatService;

    @Override
    public ProcessResult processResult(XmJobInput context) throws Exception {

        enterpriseWechatService.updateUserId();

        return new ProcessResult(true);
    }
}
