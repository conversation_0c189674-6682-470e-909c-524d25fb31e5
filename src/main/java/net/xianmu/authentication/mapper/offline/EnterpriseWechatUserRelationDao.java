package net.xianmu.authentication.mapper.offline;

import net.xianmu.authentication.model.entity.EnterpriseWechatUserRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface EnterpriseWechatUserRelationDao {
    int deleteByPrimaryKey(Long id);

    int insert(EnterpriseWechatUserRelation record);

    int insertSelective(EnterpriseWechatUserRelation record);

    EnterpriseWechatUserRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(EnterpriseWechatUserRelation record);

    int updateByPrimaryKey(EnterpriseWechatUserRelation record);

    List<EnterpriseWechatUserRelation> selectByPage(@Param("offset") int offset, @Param("pageSize") int pageSize);

}