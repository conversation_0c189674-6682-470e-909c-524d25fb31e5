package net.xianmu.authentication.mapper.auth;


import net.xianmu.authentication.client.dto.AuthRole;
import net.xianmu.authentication.client.dto.AuthRoleDTO;
import net.xianmu.authentication.client.input.AuthRoleQueryVO;
import net.xianmu.authentication.model.DTO.AuthUserRoleIdDto;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface AuthRoleDao {
    int deleteByPrimaryKey(Long id);

    int insert(AuthRole record);

    int insertSelective(AuthRole record);

    AuthRole selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthRole record);

    int updateByPrimaryKey(AuthRole record);

    List<AuthRoleDTO> selectAuthRoleQueryVO(AuthRoleQueryVO roleQueryVO);

    List<AuthRole> selectByUserId(Long userId);

    List<AuthUserRoleIdDto> selectByUserIds(@Param("userIds")List<Long> userIds);

    List<AuthRole> selectRoleIds(@Param("systemOrigin")Integer systemOrigin);

    AuthRole selectBySourceTenantSuperRole(@Param("systemOrigin")Integer systemOrigin, @Param("tenantId")Long tenantId);

    AuthRole selectByOriginTenantIdRoleName(@Param("systemOrigin")Integer systemOrigin, @Param("tenantId")Long tenantId,
                                            @Param("roleName")String roleName);

    List<AuthRole> selectByIds(@Param("roleIds")List<Integer> roleIds);

    List<Integer> selectRoleIdBySourceAndTenantId(@Param("systemOrigin")Integer systemOrigin, @Param("tenantId") Long tenantId);

    Long selectAuthIdByRoleId(@Param("systemOrigin")Integer systemOrigin, @Param("id")Long id);
}