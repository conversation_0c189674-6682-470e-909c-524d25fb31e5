package net.xianmu.authentication.mapper.auth;

import net.xianmu.authentication.model.BO.AuthTenantPrivilegesBo;
import net.xianmu.authentication.model.entity.AuthTenantPrivileges;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface AuthTenantPrivilegesDao {
    int deleteByPrimaryKey(Long id);

    int insert(AuthTenantPrivileges record);

    int insertSelective(AuthTenantPrivileges record);

    AuthTenantPrivileges selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthTenantPrivileges record);

    int updateByPrimaryKey(AuthTenantPrivileges record);


    int deleteByTenantId(@Param("tenantId") Long tenantId);

    int insertOrUpdateBatch(@Param("list") List<AuthTenantPrivilegesBo> records);

    List<AuthTenantPrivileges> selectByTenantId(@Param("tenantId") Long tenantId);

}