package net.xianmu.authentication.mapper.auth;


import net.xianmu.authentication.client.dto.AuthRolePurview;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthRolePurviewDao {
    int deleteByPrimaryKey(Integer id);

    int insert(AuthRolePurview record);

    int insertSelective(AuthRolePurview record);

    AuthRolePurview selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AuthRolePurview record);

    int updateByPrimaryKey(AuthRolePurview record);

    int deleteByRoleId(@Param("roleId") Long roleId,@Param("tenantId") Long tenantId);

    int batchAdd(@Param("list") List<AuthRolePurview> list);

    List<Long> selectRoleIdByPurviewId(Integer purviewId);
}