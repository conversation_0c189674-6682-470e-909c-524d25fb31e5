package net.xianmu.authentication.mapper.auth;


import net.xianmu.authentication.client.dto.AuthMenuPurview;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

public interface AuthMenuPurviewDao {
    int deleteByPrimaryKey(Long id);

    int insert(AuthMenuPurview record);

    int insertSelective(AuthMenuPurview record);

    AuthMenuPurview selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthMenuPurview record);

    int updateByPrimaryKey(AuthMenuPurview record);

    List<AuthMenuPurview> selectByIds(@Param("ids") List<Long> ids);

    List<AuthMenuPurview> selectRoleMenus(@Param("systemOrigin")Integer systemOrigin, @Param("tenantId")Long tenantId, @Param("roleId")Long roleId);

    List<AuthMenuPurview> selectMenus(Integer systemOrigin);

    AuthMenuPurview selectByName(@Param("systemOrigin")Integer systemOrigin,@Param("menuName") String menuName);

    AuthMenuPurview selectByUrl(@Param("systemOrigin")Integer systemOrigin,@Param("url") String url);


    List<AuthMenuPurview> selectByRolIds(@Param("systemOrigin")Integer systemOrigin, @Param("roleIds")Set<Long> roleIds,@Param("tenantId")Long tenantId);

    int countMenusByPid(Long purviewId);


    int selectMaxWeightBySystemOriginEnumParentId(@Param("systemOrigin") Integer systemOrigin, @Param("parentId")Integer parentId);

    List<AuthMenuPurview> selectBySourceParentId(@Param("systemOrigin") Integer systemOrigin, @Param("parentId") Integer parentId, @Param("weight") Integer weight);

    AuthMenuPurview selectBySourceNameUrls(@Param("systemOrigin") Integer systemOrigin, @Param("menus") String menus, @Param("urls") String urls);
}