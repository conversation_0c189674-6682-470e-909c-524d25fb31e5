package net.xianmu.authentication.mapper.auth;



import net.xianmu.authentication.client.dto.AuthUser;
import net.xianmu.authentication.model.BO.AuthUserBO;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

public interface AuthUserDao {
    int deleteByPrimaryKey(Long id);

    int insert(AuthUser record);

    int insertSelective(AuthUser record);

    AuthUser selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthUser record);

    List<AuthUser> selectByUserBaseId(Long userBaseId);

    List<AuthUser> selectByUserIds(@Param("ids")  List<Long> ids);

    AuthUser selectByBizUserId(@Param("systemOriginEnum")Integer systemOriginEnum, @Param("bizUserId") Long bizUserId);

    int updateBaseUserId(@Param("oldBaseUserId")Long oldBaseUserId,@Param("newBaseUserId") Long newBaseUserId);

    int updateBaseUserIdById(@Param("id")Long id,@Param("baseUserId") Long baseUserId);

    List<AuthUser> selectByUserIdAndOrigin(@Param("systemOrigin") Integer systemOrigin,
                                           @Param("tenantId") Long tenantId,
                                           @Param("idList") List<Long> idList,
                                           @Param("baseUserIdList") List<Long> baseUserIdList);


    AuthUser selectByBizUserIdTenantId(@Param("systemOriginEnum")Integer systemOriginEnum,
                                       @Param("bizUserId") Long bizUserId,
                                       @Param("tenantId") Long tenantId);

    int updateLastLoginTime(@Param("id") Long id);


    int updateLastLoginTimeByIdTime(@Param("id") Long id, @Param("lastLoginTime") Date lastLoginTime);

    int updateStatusById(@Param("id")Long id,@Param("status") Integer status);



    int updateAuditStatusById(@Param("id") Long id, @Param("auditStatus") Integer auditStatus);

    List<AuthUser> selectBySourceTenantIdBizIds(@Param("systemOrigin") Integer systemOrigin,
                                                 @Param("tenantId") Long tenantId,
                                                 @Param("bizIds") List<Long> bizIds);

    List<AuthUser> selectBySourceTenantIdPhone(@Param("systemOrigin") Integer systemOrigin,
                                               @Param("tenantId") Long tenantId,
                                               @Param("phone") String phone);



    void updatePwdBySystemOriginEnumBaseId(@Param("systemOrigin") Integer systemOrigin,
                                           @Param("userBaseId") Long userBaseId,@Param("password") String password);

    List<AuthUserBO> selectUserIdPhoneBySourcePhone(@Param("systemOrigin") Integer systemOrigin,
                                                    @Param("phones") List<String> phones);


    List<AuthUser> selectByPhone(@Param("phones") List<String> phones);


    List<AuthUserBO>selectAuthUserBySystemTenantIdRoleIds(@Param("systemOrigin") Integer systemOrigin, @Param("tenantId") Long tenantId,
                                                        @Param("roleIds") List<Long> roleIds);


    List<AuthUser> selectByUserBaseIdAndOrigin(Long userBaseId, @Param("systemOrigin") Integer systemOrigin);


    void updatePwdByIds(@Param("ids") List<Long> ids ,@Param("password") String password);

}