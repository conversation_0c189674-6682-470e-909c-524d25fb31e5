package net.xianmu.authentication.mapper.auth;

import net.xianmu.authentication.model.entity.AuthWechatRelation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthWechatRelationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(AuthWechatRelation record);

    int insertSelective(AuthWechatRelation record);

    AuthWechatRelation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthWechatRelation record);

    int updateByPrimaryKey(AuthWechatRelation record);

    void batchAdd(@Param("list") List<AuthWechatRelation> authWechatRelations);


    void deleteList(@Param("systemOrigin")Integer systemOrigin,@Param("authType") Integer authType,@Param("tenantId")  Long tenantId,@Param("openIds") List<String> openIds);

    String selectBySourceTenantIdAuthTypeUnionid(@Param("systemOrigin")Integer systemOrigin,@Param("tenantId")  Long tenantId, @Param("authType") Integer authType, @Param("unionid")  String unionid);

    AuthWechatRelation selectBySourceTenantIdAuthTypeOpenid(@Param("systemOrigin")Integer systemOrigin,@Param("authType") Integer authType,@Param("tenantId")  Long tenantId,@Param("openId") String openid);

}