package net.xianmu.authentication.mapper.auth;


import net.xianmu.authentication.client.dto.AuthUserRole;
import net.xianmu.authentication.model.DTO.RoleUserCountDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthUserRoleDao {
    int deleteByPrimaryKey(Integer id);

    int insert(AuthUserRole record);

    int insertSelective(AuthUserRole record);

    AuthUserRole selectByPrimaryKey(Integer id);

    int updateByPrimaryKeySelective(AuthUserRole record);

    int updateByPrimaryKey(AuthUserRole record);

    List<Integer> selectRoleIdByUserId(Long userId);

    int batchAdd(@Param("list") List<AuthUserRole> list);

    List<Integer> selectRoleIdByUserIds(@Param("list") List<Long> userIds);


    int deleteByRoleId(Long roleId);

    List<RoleUserCountDTO>  countUserByUserId(@Param("roleIds")List<Long> roleIds);


    int deleteByUserIdRoleId(Long userId);

    List<Long>  selectUserIdListByRoleId(@Param("roleIds")List<Long> roleIds);

    Long countUserIdListByRoleId(Long roleId);

}