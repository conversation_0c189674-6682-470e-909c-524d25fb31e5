package net.xianmu.authentication.mapper.auth;

import net.xianmu.authentication.model.entity.BusinessDataPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface BusinessDataPermissionDao {
    int deleteByPrimaryKey(Long id);

    int insert(BusinessDataPermission record);

    int insertSelective(BusinessDataPermission record);

    BusinessDataPermission selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(BusinessDataPermission record);

    int updateByPrimaryKey(BusinessDataPermission record);

    List<BusinessDataPermission> getBusinessDataPermissionByType(@Param("permissionType") Integer type);

    BusinessDataPermission selectByValueType(@Param("permissionValue")String permissionValue, @Param("permissionType")Integer permissionType);
}