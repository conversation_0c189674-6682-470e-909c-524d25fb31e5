package net.xianmu.authentication.mapper.auth;



import net.xianmu.authentication.client.dto.AuthUserAuth;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.model.BO.AuthUserAuthBO;
import net.xianmu.authentication.model.DTO.AuthUserAuthDTO;
import net.xianmu.authentication.model.DTO.UserBaseThirdPartyDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthUserAuthDao {
    int deleteByPrimaryKey(Long id);

    int insert(AuthUserAuth record);

    int insertSelective(AuthUserAuth record);

    AuthUserAuth selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthUserAuth record);

    int updateByPrimaryKey(AuthUserAuth record);

    int deleteByAuthIdType(@Param("userId") Long userId,@Param("authType") Integer type);

    int deleteByAuthIdsType(@Param("userId") List<Long> userId,@Param("authType") Integer type);

    List<AuthUserAuthBO> selectBOByOpenIdType(@Param("systemOriginEnum")Integer systemOriginEnum,
                                              @Param("authType")Integer type, @Param("openid") String openid);

    List<AuthUserAuthBO> selectBOByThirdIdType(@Param("systemOriginEnum")Integer systemOriginEnum,
                                               @Param("authType") Integer type, @Param("unionId") String unionId);

    List<AuthUserAuth> selectByOpenIdType(@Param("authType")Integer type, @Param("openid") String openid);

    List<AuthUserAuth> selectUserAuthRecord(@Param("authType") Integer type, @Param("userId") Long userId);

    List<AuthUserAuth> selectByThirdIdType(@Param("authType") Integer type, @Param("unionId")String unionId);

    List<AuthUserAuthBO> selectBySystemOriginEnumUserBaseIds(@Param("systemOriginEnum")Integer systemOriginEnum,
                                                             @Param("userBaseIds")List<Long> userBaseIds, @Param("authType")Integer authType);

    AuthUserAuth selectByUserIdOpenIdType( @Param("userId") Long userId,  @Param("openid") String openid,@Param("authType")Integer type);

    /**
     * 批量查询账号信息
     * @param accountType 账号类型 {@link net.xianmu.authentication.client.input.binlog.AuthThirdPartyInput.AccountTypeEnum}
     * @param accountList 账号数据
     * @return AuthUserAuthDTO
     */
    List<AuthUserAuthDTO> batchQueryByType(@Param("originType") Integer systemOriginType,
                                           @Param("authType") Integer authType,
                                           @Param("tenantId") Long tenantId,
                                           @Param("accountType") Integer accountType,
                                           @Param("accountList") List<String> accountList);

    /**
     * 使用三方id查询
     * @param authType 三方类型
     * @param thirdPartyList 三方id
     * @return AuthUserAuth
     */
    List<AuthUserAuth> selectByThirdPartyId(@Param("authType") Integer authType, @Param("thirdPartyList") List<String> thirdPartyList);

    /**
     * userIds 查询三方id
     * @param userIds auth_user_id
     * @param type 三方类型
     * @return AuthUserAuth
     */
    List<AuthUserAuth>  selectByUserIdsType(@Param("userIds")List<Long> userIds,@Param("authType") Integer type);

    int batchAdd(@Param("list") List<AuthUserAuth> list);

    List<UserBaseThirdPartyDTO> selectByThirdPartyPage(@Param("systemOriginEnum")Integer systemOriginEnum,
                                                       @Param("tenantId")Long tenantId, @Param("authType")Integer authType);

    void updateTime(@Param("userId")Long userId ,@Param("authType") Integer authType, @Param("openid") String openid);

    List<AuthUserAuthBO>  selectByBizIdSourceTenantIdType(@Param("systemOriginEnum")Integer systemOriginEnum, @Param("tenantId") Long tenantId,
                                                        @Param("bizUserIds") List<Long> bizUserIds,@Param("authType") Integer authType);
}