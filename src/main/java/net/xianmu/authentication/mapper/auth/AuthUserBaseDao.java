package net.xianmu.authentication.mapper.auth;



import net.xianmu.authentication.client.dto.AuthUserBase;
import net.xianmu.authentication.client.input.user.AuthUserBaseQueryInput;
import net.xianmu.authentication.model.DTO.AuthUserPhoneDTO;
import net.xianmu.authentication.model.VO.AuthUserVo;
import net.xianmu.authentication.model.input.AuthUserQueryInput;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthUserBaseDao {
    int deleteByPrimaryKey(Long id);

    int insert(AuthUserBase record);

    int insertSelective(AuthUserBase record);

    AuthUserBase selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthUserBase record);

    int updateByPrimaryKey(AuthUserBase record);

    AuthUserBase selectByUserBase(AuthUserBase queryUserBase);

    AuthUserBase selectByNameOrigin(String username);


    List<AuthUserBase> selectByIds(@Param("ids")List<Long> ids);

    List<Long> selectUserBaseIdsBySourceRoleIds(@Param("systemOrigin") Integer systemOrigin,@Param("roleIds")  List<Long> roleIds);


    List<AuthUserVo> selectAuthUserBySourceRoleIdsAndName(AuthUserQueryInput input);

    /**
     * 根据手机号批量查询
     * @param phoneList 手机号
     * @return userBase
     */
    List<AuthUserBase> selectByPhoneList(@Param("phoneList") List<String> phoneList);

    /**
     * 分页查询用户数据
     * @param systemOrigin {@link net.xianmu.authentication.client.input.SystemOriginEnum}
     * @param offset 起始位置
     * @param pageSize 分页大小
     * @return 用户数据
     */
    List<AuthUserPhoneDTO> selectWithOrigin(@Param("systemOrigin") Integer systemOrigin, @Param("offset") int offset, @Param("pageSize") int pageSize);

    /**
     * 分页查询用户数据
     * @param systemOrigin {@link net.xianmu.authentication.client.input.SystemOriginEnum}
     * @param offset 起始位置
     * @param pageSize 分页大小
     * @return 用户数据
     */
    List<AuthUserPhoneDTO> selectWithOriginWithOutBigCustomer(@Param("systemOrigin") Integer systemOrigin, @Param("offset") int offset, @Param("pageSize") int pageSize);


    AuthUserBase selectByPhone(@Param("phone") String phone);


    AuthUserBase selectByEmail(@Param("email") String email);


    /**
     * 根据手机号批量查询
     * @param phoneList 手机号
     * @return userBase
     */
    List<AuthUserBase> queryAuthUserBase(AuthUserBaseQueryInput authUserBaseQueryInput);


}