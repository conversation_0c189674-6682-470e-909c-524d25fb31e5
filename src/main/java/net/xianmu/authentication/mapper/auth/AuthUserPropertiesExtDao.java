package net.xianmu.authentication.mapper.auth;



import net.xianmu.authentication.client.dto.AuthUserPropertiesExt;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

public interface AuthUserPropertiesExtDao {
    int deleteByPrimaryKey(Long id);

    int insert(AuthUserPropertiesExt record);

    int insertSelective(AuthUserPropertiesExt record);

    AuthUserPropertiesExt selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthUserPropertiesExt record);

    int updateByPrimaryKey(AuthUserPropertiesExt record);


    AuthUserPropertiesExt selectByLikeValue(@Param("propKey") String propKey, @Param("propValue") String propValue);

    AuthUserPropertiesExt selectByValue(@Param("propKey") String propKey, @Param("propValue") String propValue);
    List<AuthUserPropertiesExt> selectALLByValue(@Param("propKey") String propKey, @Param("propValue") String propValue);

    List<AuthUserPropertiesExt> selectValue(@Param("userId") Long userId, @Param("propKey") String propKey);

    int updateValue(@Param("userId") Long userId, @Param("propKey") String propKey, @Param("propValue") String propValue);

    int deleteByUserIdKeys(@Param("userId")Long userId,@Param("propKeys") List<String> propKeys);

    int deleteByUserIdKeyValue(@Param("userId") Long authUserId, @Param("propKey") String key, @Param("propValue") String value);

    int deleteByAllUserIdsKey(@Param("userIds") Collection<Long> userIds, @Param("propKey") String key);

    int batchAdd(@Param("list") List<AuthUserPropertiesExt> authUserPropertiesExts);


    AuthUserPropertiesExt selectUserIdKeyValue(@Param("userId") Long userId, @Param("propKey") String propKey, @Param("propValue") String propValue);



    int updateLastTime(@Param("userId") Long userId, @Param("propKey") String propKey, @Param("propValue") String propValue);

    List<AuthUserPropertiesExt> selectByUserIdsAndKey(@Param("userIds") List<Long> userIds,@Param("propKey")  String propKey);

    List<AuthUserPropertiesExt> selectALLByKeyValue(@Param("propKey") String propKey, @Param("propValue") String propValue);

}
