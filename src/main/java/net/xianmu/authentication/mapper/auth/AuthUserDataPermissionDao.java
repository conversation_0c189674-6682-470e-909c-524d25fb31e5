package net.xianmu.authentication.mapper.auth;

import net.xianmu.authentication.model.entity.AuthUserDataPermission;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AuthUserDataPermissionDao {
    int deleteByPrimaryKey(Long id);

    int insert(AuthUserDataPermission record);

    int insertSelective(AuthUserDataPermission record);

    AuthUserDataPermission selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(AuthUserDataPermission record);


    int deleteByUserIdType(@Param("userId") Long userId, @Param("permissionType")Integer type);

    int batchAdd(@Param("list") List<AuthUserDataPermission> collect);

    List<AuthUserDataPermission> selectByUserIdType(@Param("userId") Long userId,@Param("permissionType")  Integer type);
}