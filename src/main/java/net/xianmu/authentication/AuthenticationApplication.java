package net.xianmu.authentication;

import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.alibaba.nacos.api.config.ConfigType;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import net.summerfarm.common.util.Prompt;
import net.xianmu.authentication.common.DbTableDml;
import net.xianmu.authentication.common.factory.DbTableDmlFactory;
import net.xianmu.authentication.service.impl.DatePermissionDmlImpl;
import org.apache.dubbo.config.spring.context.annotation.DubboComponentScan;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.http.MediaType;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static net.xianmu.authentication.common.AuthGlobal.WAREHOUSE_LOGISTICS_CENTER_TABLE;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 * @Description
 * @createTime 2022年04月23日
 */
@SpringBootApplication(scanBasePackages = {"net.xianmu.authentication"},exclude = {
        DataSourceAutoConfiguration.class,
        DataSourceTransactionManagerAutoConfiguration.class,
})
@EnableAsync
@EnableScheduling
@DubboComponentScan(basePackages = "net.xianmu.authentication.**.provider")
@NacosPropertySource(dataId = "${spring.application.name}", type = ConfigType.PROPERTIES, autoRefreshed = true)
public class AuthenticationApplication{

    public static void main(String[] args) {
        SpringApplication.run(AuthenticationApplication.class, args);
        //加载通用返回信息
        Prompt.processProperties();
    }

    /**
     * 引入Fastjson解析json，不使用默认的jackson
     * @return
     */
    @Bean
    public HttpMessageConverters customConverters() {
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        // 创建配置类
        FastJsonConfig config = new FastJsonConfig();
        config.setSerializerFeatures(
                SerializerFeature.DisableCircularReferenceDetect
        );
        //此处是全局处理方式
        config.setCharset(StandardCharsets.UTF_8);
        fastConverter.setFastJsonConfig(config);
        List<MediaType> supportedMediaTypes = new ArrayList<>();
        supportedMediaTypes.add(MediaType.ALL);
        fastConverter.setSupportedMediaTypes(supportedMediaTypes);
        //支持text 转string
        StringHttpMessageConverter stringHttpMessageConverter = new StringHttpMessageConverter();
        return new HttpMessageConverters(fastConverter, stringHttpMessageConverter);
    }

    @Bean("dbTableDmlFactory")
    public DbTableDmlFactory dbTableDmlFactory(DatePermissionDmlImpl datePermissionDml) {
        DbTableDmlFactory tableDmlFactory = new DbTableDmlFactory();
        Map<String, DbTableDml> dbTableDmlHashMap = new ConcurrentHashMap<>(64);

        dbTableDmlHashMap.put(WAREHOUSE_LOGISTICS_CENTER_TABLE, datePermissionDml);

        tableDmlFactory.setDbTableDmlMap(dbTableDmlHashMap);
        return tableDmlFactory;
    }


}
