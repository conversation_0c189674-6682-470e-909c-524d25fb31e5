
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: *********************************************************************************************
  username: dev
spring:
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 0
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  http:
    encoding:
      charset: UTF-8
  application:
    id: authentication
    name: authentication
  datasource:
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 30000
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: 0fba89cd-351e-4e9f-86dc-4b4fbc06170e
    groupId: xianmu-authentication
    appKey: /YEt19aBOnnz4acvPJrf+Q==
dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C
  accessKeySecret: ******************************
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_manage
    secret-key: ''
    sendMsgTimeout: 10000
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.summerfarm.net
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://devh5.summerfarm.net

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: fac8164c-1da8-43d2-bf49-e187bda7fcb4
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000

server:
  port: 80
  servlet:
    context-path: /authentication
log-path:  ${APP_LOG_DIR:../log}

logging:
  level:
    root:  info
    org.springframework:  INFO
    org.mybatis:  INFO
    com.summerfarm: INFO
  pattern:
    console: "%d - %msg%n"

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

xm:
  log:
    enable: true
    resp: true
  mall-wechat:
    app:
      id: wx32a0e329197b752b
      secret: e3d4592bb9f437e682334efd37928ee8
    mp-app:
      id: wx674b60a859676717
      secret: 250fd1499c812616fe4b0caab9dffb0f
    pop-app:
      id: wx7b96cacba5e0e9e7
      secret: fad5bc94ebb8e92014225982588e5fc9
    pop-mp-app:
      id: wxefc29048b84a19cb
      secret: 7f2a43f3a9975be1c6f66683c9ca2b03
  sentinel:
    nacos:
      serverAddr: test-nacos.summerfarm.net:11000
      groupId: sentinel
      namespace: e5ca5c64-a551-4889-b5a1-7bf9c90b4752
  srm-wechat:
    app:
      id: wx96bc79fbea153923
      secret: 089d521244d2b743b8ab7a313af24c9b
    miniapp:
      id: wx847e41a8231b974c
      secret: c3aa15e91047ce49251bf48d582981fc

  i18n:
    enable: true
    datasource:
      url: **************************************************************************************************************
      username: test
      password: xianmu619
      driver-class-name: com.mysql.cj.jdbc.Driver
# 配置中心
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 19b82444-16f9-4d22-a522-b7ac6495c954

