
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: *********************************************************************************************
  username: dev3
spring:
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 3
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 2
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  http:
    encoding:
      charset: UTF-8
  application:
    id: authentication
    name: authentication
  datasource:
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 30000
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: ********-784e-4b79-a13e-8954271dee95
    groupId: xianmu-authentication
    appKey: yYWaNKUt8aFe0nwyNYDS3Q==
dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C
  accessKeySecret: ******************************
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9876
  producer:
    access-key: ''
    group: GID_manage
    secret-key: ''
    sendMsgTimeout: 10000
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.summerfarm.net
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://devh5.summerfarm.net

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: c26bc4c2-bd51-4aae-a170-1f04b9c52987
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000

server:
  port: 80
  servlet:
    context-path: /authentication
log-path:  ${APP_LOG_DIR:../log}

logging:
  level:
    root:  info
    org.springframework:  INFO
    org.mybatis:  INFO
    com.summerfarm: INFO
  pattern:
    console: "%d - %msg%n"

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql

xm:
  log:
    enable: true
    resp: true
  mall-wechat:
    app:
      id: wx86d6db2ec4b9e9cc
      secret: 070812b6ce547bacaa402d91245ddc80
    mp-app:
      id: wx0234b1d4eb212e12
      secret: 7779dbf6349ca85212b05435dfc38716
  sentinel:
    nacos:
      serverAddr: test-nacos.summerfarm.net:11000
      groupId: sentinel
      namespace: e5ca5c64-a551-4889-b5a1-7bf9c90b4752
# 配置中心
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: bd543515-d448-423f-baec-ec069c924c12
