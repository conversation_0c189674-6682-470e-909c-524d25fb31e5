
mybatis:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
mysql:
  asyncInit: true
  dbType: com.alibaba.druid.pool.DruidDataSource
  driverClassName: com.mysql.jdbc.Driver
  initialSize: 1
  maxActive: 20
  maxWait: 6000
  minIdle: 1
  offline:
    password: xianmu619
    url: **************************************************************************************************
    username: test
  password: xianmu619
  testWhileIdle: true
  url: *********************************************************************************************
  username: dev2
spring:
  redis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 1
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  authRedis:
    host: test-redis.summerfarm.net
    port: 6379
    password: xianmu619
    timeout: 5000 # 连接超时时间（毫秒）
    database: 1
    jedis:
      pool:
        max-active: 200 # 连接池最大连接数（使用负值表示没有限制）
        max-idle: 20 # 连接池中的最大空闲连接
        min-idle: 10 # 连接池中的最小空闲连接
        max-wait: 5000 # 连接池最大阻塞等待时间（使用负值表示没有限制）
  http:
    encoding:
      charset: UTF-8
  application:
    id: authentication
    name: authentication
  datasource:
    tomcat:
      initial-size: 0
      max-active: 20
      max-idle: 20
      min-idle: 1
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 30000
  schedulerx2:
    endpoint: acm.aliyun.com
    namespace: d470595b-6520-4833-a158-239340a08eb2
    groupId: xianmu-authentication
    appKey: buZBctjmSwXR3oKWypSWXQ==

dms:
  accessKeyId: LTAI5tPZ4eRj2vMx8tfZY49C
  accessKeySecret: ******************************
es:
  port: 80
  url: dev.es.summerfarm.net
  user-name: elastic
  user-pwd: Xianmu619
rocketmq:
  consumer:
    access-key: ''
    secret-key: ''
  name-server: test-mq-nameserver.summerfarm.net:9877
  producer:
    access-key: ''
    group: GID_manage
    secret-key: ''
    sendMsgTimeout: 10000
stmp:
  account: <EMAIL>
  auth: true
  defaultEncoding: utf-8
  host: smtp.summerfarm.net
  password: Xianmu619
  port: 465
  socketFactory:
    class: javax.net.ssl.SSLSocketFactory
xianmu:
  mall:
    domain: https://devh5.summerfarm.net

dubbo:
  application:
    name: ${spring.application.name}
    id: ${spring.application.name}
  registry:
    protocol: nacos
    #    address: nacos://************:11000
    address: nacos://test-nacos.summerfarm.net:11000
    #    address: nacos://*********:11000
    parameters:
      namespace: a9f94e14-0f25-4567-a038-b32e83829046
  protocol:
    id: dubbo
    name: dubbo
    port: 20880
  provider:
    version: 1.0.0
    group: online
    timeout: 5000
    retries: 0
  consumer:
    version: 1.0.0
    group: online
    retries: 0
    check: false
    timeout: 10000

server:
  port: 80
  servlet:
    context-path: /authentication
log-path:  ${APP_LOG_DIR:../log}

logging:
  level:
    root:  info
    org.springframework:  INFO
    org.mybatis:  INFO
    com.summerfarm: INFO
  pattern:
    console: "%d - %msg%n"

# 关闭es健康检查
management:
  health:
    elasticsearch:
      enabled: false

#pagehelper分页插件配置
pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true
  params: count=countSql
  
xm:
  log:
    enable: true
    resp: true
  mall-wechat:
    app:
      id: wxfc419e72b4747b33
      secret: ea4a5e2f096d2ce177bc7a3d1bbb07cd
    mp-app:
      id: wx0234b1d4eb212e12
      secret: 7779dbf6349ca85212b05435dfc38716
    pop-app:
      id: wxdc920b47380a0ae4
      secret: dcaa85b59ca267a3606f08a0b0fd95ce
    pop-mp-app:
      id: wxefc29048b84a19cb
      secret: 7f2a43f3a9975be1c6f66683c9ca2b03
  srm-wechat:
    app:
      id: wx96bc79fbea153923
      secret: 089d521244d2b743b8ab7a313af24c9b
    miniapp:
      id: wx847e41a8231b974c
      secret: c3aa15e91047ce49251bf48d582981fc
  sentinel:
    nacos:
      serverAddr: test-nacos.summerfarm.net:11000
      groupId: sentinel
      namespace: e5ca5c64-a551-4889-b5a1-7bf9c90b4752      
# 配置中心
nacos:
  config:
    server-addr: test-nacos.summerfarm.net:11000
    namespace: 800ed4d6-a4fd-4345-86dd-8a4cc70c9bda
