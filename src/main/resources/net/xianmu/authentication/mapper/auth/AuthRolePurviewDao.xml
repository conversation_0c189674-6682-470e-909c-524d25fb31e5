<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthRolePurviewDao">
    <resultMap id="BaseResultMap" type="net.xianmu.authentication.client.dto.AuthRolePurview">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="role_id" jdbcType="INTEGER" property="roleId"/>
        <result column="purview_id" jdbcType="INTEGER" property="purviewId"/>
        <result column="tenant_id" jdbcType="INTEGER" property="tenantId"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="weight" jdbcType="INTEGER" property="weight"/>
    </resultMap>
    <sql id="Base_Column_List">
    id, role_id, purview_id, tenant_id, create_time, update_time, weight
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_role_purview
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from auth_role_purview
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.xianmu.authentication.client.dto.AuthRolePurview" useGeneratedKeys="true">
    insert into auth_role_purview (role_id, purview_id, tenant_id, 
      create_time, update_time, weight
      )
    values (#{roleId,jdbcType=INTEGER}, #{purviewId,jdbcType=INTEGER}, #{tenantId,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{weight,jdbcType=INTEGER}
      )
  </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.xianmu.authentication.client.dto.AuthRolePurview" useGeneratedKeys="true">
        insert into auth_role_purview
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                role_id,
            </if>
            <if test="purviewId != null">
                purview_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="weight != null">
                weight,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleId != null">
                #{roleId,jdbcType=INTEGER},
            </if>
            <if test="purviewId != null">
                #{purviewId,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="weight != null">
                #{weight,jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.client.dto.AuthRolePurview">
        update auth_role_purview
        <set>
            <if test="roleId != null">
                role_id = #{roleId,jdbcType=INTEGER},
            </if>
            <if test="purviewId != null">
                purview_id = #{purviewId,jdbcType=INTEGER},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="weight != null">
                weight = #{weight,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.client.dto.AuthRolePurview">
    update auth_role_purview
    set role_id = #{roleId,jdbcType=INTEGER},
      purview_id = #{purviewId,jdbcType=INTEGER},
      tenant_id = #{tenantId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      weight = #{weight,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>

    <delete id="deleteByRoleId">
    delete  from auth_role_purview where role_id = #{roleId} and tenant_id =#{tenantId}
  </delete>

    <insert id="batchAdd">
        insert into auth_role_purview (role_id, purview_id, tenant_id,
        create_time, update_time, weight
        )
        values
        <foreach collection="list" separator="," item="item">
            (#{item.roleId,jdbcType=INTEGER}, #{item.purviewId,jdbcType=INTEGER}, #{item.tenantId,jdbcType=INTEGER},
            sysdate(), sysdate(), #{item.weight,jdbcType=INTEGER}
            )
        </foreach>
    </insert>

    <select id="selectRoleIdByPurviewId" resultType="long">
        select
        distinct  role_id
        from auth_role_purview
        where purview_id =#{purviewId}
    </select>
</mapper>