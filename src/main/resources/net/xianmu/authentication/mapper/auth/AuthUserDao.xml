<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthUserDao">
    <resultMap id="BaseResultMap" type="net.xianmu.authentication.client.dto.AuthUser">
        <id column="id" jdbcType="BIGINT" property="id"/>
        <result column="system_origin" jdbcType="TINYINT" property="systemOrigin"/>
        <result column="user_base_id" jdbcType="BIGINT" property="userBaseId"/>
        <result column="tenant_id" jdbcType="BIGINT" property="tenantId"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="audit_status" jdbcType="TINYINT" property="auditStatus"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="biz_user_id" jdbcType="BIGINT" property="bizUserId"/>
        <result column="password" jdbcType="VARCHAR" property="password"/>
        <result column="last_login_time" jdbcType="TIMESTAMP" property="lastLoginTime"/>

    </resultMap>
    <sql id="Base_Column_List">
    id, system_origin, user_base_id, tenant_id, create_time, update_time,`status`,biz_user_id,password,last_login_time,audit_status
  </sql>
    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user
        where id = #{id,jdbcType=BIGINT}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_user
    where id = #{id,jdbcType=BIGINT}
  </delete>
    <insert id="insert" keyColumn="id" keyProperty="id"
            parameterType="net.xianmu.authentication.client.dto.AuthUser" useGeneratedKeys="true">
    insert into auth_user (user_base_id, tenant_id, status,system_origin,
      create_time, update_time,biz_user_id,password,last_login_time,audit_status)
    values (#{userBaseId,jdbcType=BIGINT}, #{tenantId,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{systemOrigin,jdbcType=TINYINT},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},#{bizUserId,jdbcType=BIGINT}, #{password},#{lastLoginTime},#{auditStatus})
  </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id"
            parameterType="net.xianmu.authentication.client.dto.AuthUser" useGeneratedKeys="true">
        insert into auth_user
        <trim prefix="(" suffix=")" suffixOverrides=",">

            <if test="userBaseId != null">
                user_base_id,
            </if>
            <if test="tenantId != null">
                tenant_id,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="systemOrigin != null">
                system_origin,
            </if>
            <if test="bizUserId != null">
                biz_user_id,
            </if>
            <if test="password != null">
                password,
            </if>
            <if test="lastLoginTime != null">
                last_login_time,
            </if>
            <if test="auditStatus != null">
                audit_status,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="userBaseId != null">
                #{userBaseId,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="systemOrigin != null">
                #{systemOrigin,jdbcType=TINYINT},
            </if>
            <if test="bizUserId != null">
                #{bizUserId,jdbcType=BIGINT},
            </if>
            <if test="password != null">
                #{password},
            </if>
            <if test="lastLoginTime != null">
                #{lastLoginTime},
            </if>
            <if test="auditStatus != null">
                #{auditStatus},
            </if>
        </trim>
    </insert>
    <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.client.dto.AuthUser">
        update auth_user
        <set>

            <if test="userBaseId != null">
                user_base_id = #{userBaseId,jdbcType=BIGINT},
            </if>
            <if test="tenantId != null">
                tenant_id = #{tenantId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="status != null">
                status = #{status},
            </if>
            <if test="systemOrigin != null">
                system_origin = #{systemOrigin},
            </if>
            <if test="bizUserId != null">
                biz_user_id = #{bizUserId,jdbcType=BIGINT},
            </if>
            <if test="password != null">
                password =  #{password},
            </if>
            <if test="lastLoginTime != null">
                last_login_time = #{lastLoginTime},
            </if>
            <if test="auditStatus != null">
                audit_status =  #{auditStatus},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <select id="selectByUserBaseId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user
        where user_base_id = #{userBaseId,jdbcType=BIGINT}
    </select>


    <select id="selectByUserIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user
        where id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByBizUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from auth_user
        where system_origin =#{systemOriginEnum} and biz_user_id = #{bizUserId}
    </select>


    <update id="updateBaseUserId" parameterType="java.lang.Long">
    update  auth_user set user_base_id = #{newBaseUserId}
    where user_base_id = #{oldBaseUserId,jdbcType=BIGINT}
   </update>

    <update id="updateBaseUserIdById" parameterType="java.lang.Long">
    update  auth_user set user_base_id = #{baseUserId}
    where id = #{id,jdbcType=BIGINT}
   </update>

  <select id="selectByUserIdAndOrigin" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from auth_user
    where system_origin = #{systemOrigin} and tenant_id = #{tenantId}
    <if test="baseUserIdList != null and userBaseIdList.size!=0">
      and user_base_id in
      <foreach collection="userBaseIdList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    <if test="idList != null and idList.size!=0">
      and id in
      <foreach collection="idList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
  </select>

    <select id="selectByBizUserIdTenantId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from auth_user
        where system_origin =#{systemOriginEnum} and biz_user_id = #{bizUserId} and tenant_id=#{tenantId}
    </select>

    <update id="updateLastLoginTime" parameterType="java.lang.Long">
    update  auth_user set last_login_time = sysdate()
    where id = #{id,jdbcType=BIGINT}
  </update>

    <update id="updateLastLoginTimeByIdTime" >
    update  auth_user set last_login_time = #{lastLoginTime}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <update id="updateStatusById" >
    update  auth_user set status = #{status}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateAuditStatusById" >
    update  auth_user set audit_status = #{auditStatus}
    where id = #{id,jdbcType=BIGINT}
  </update>
    <update id="updatePwdBySystemOriginEnumBaseId">
        update  auth_user set password=#{password}
         where system_origin = #{systemOrigin} and
         user_base_id = #{userBaseId}
    </update>

    <select id="selectBySourceTenantIdBizIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user
        where system_origin = #{systemOrigin} and tenant_id = #{tenantId}
        <if test="bizIds != null and bizIds.size!=0">
            and biz_user_id in
            <foreach collection="bizIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="selectBySourceTenantIdPhone" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user
        where system_origin = #{systemOrigin} and tenant_id = #{tenantId}
        and user_base_id in (select id from auth_user_base where  phone = #{phone})
    </select>


    <select id="selectUserIdPhoneBySourcePhone" resultType="net.xianmu.authentication.model.BO.AuthUserBO">
        select
        au.id , aub.phone,au.create_time createTime
        from auth_user au
        join  auth_user_base aub
        on au.user_base_id = aub.id
        where system_origin = #{systemOrigin}
        and aub.phone in
         <foreach collection="phones" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="selectByPhone" resultMap="BaseResultMap">
        select
        au.id,  au.system_origin,  au.user_base_id,  au.tenant_id,  au.create_time,  au.update_time,  au.`status`,  au.biz_user_id,  au.last_login_time,
        au.audit_status
        from auth_user au
        join auth_user_base aub
        on au.user_base_id = aub.id
        where aub.phone in
        <foreach collection="phones" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>


    <select id="selectAuthUserBySystemTenantIdRoleIds"  resultType="net.xianmu.authentication.model.BO.AuthUserBO">
        select
        GROUP_CONCAT(a.role_id SEPARATOR ',') as roleIds ,
        b.id,b.system_origin as systemOrigin,b.user_base_id as userBaseId ,b.tenant_id as tenantId,b.create_time,b.update_time,b.`status`,b.biz_user_id as bizUserId,b.password,b.last_login_time as lastLoginTime,b.audit_status as auditStatus
        from auth_user_role a
        join auth_user b
        on a.user_id = b.id
        where b.tenant_id = #{tenantId} and b.system_origin = #{systemOrigin} and  a.role_id  in
        <foreach collection="roleIds" open="(" separator="," close=")" item="item">
            #{item}
        </foreach>
        and b.status  = 0
        GROUP BY a.user_id

    </select>


    <select id="selectByUserBaseIdAndOrigin" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from auth_user
        where user_base_id = #{userBaseId,jdbcType=BIGINT}
        and system_origin = #{systemOrigin}
    </select>


    <update id="updatePwdByIds">
        update  auth_user set password=#{password}
        where id in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
</mapper>