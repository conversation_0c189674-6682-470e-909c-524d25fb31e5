<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthUserDataPermissionDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.model.entity.AuthUserDataPermission">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="permission_value" jdbcType="VARCHAR" property="permissionValue" />
    <result column="permission_type" jdbcType="TINYINT" property="permissionType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, user_id, permission_value, permission_type,tenant_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auth_user_data_permission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_user_data_permission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.AuthUserDataPermission" useGeneratedKeys="true">
    insert into auth_user_data_permission (create_time, update_time, user_id, 
      permission_value, permission_type, tenant_id
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{userId,jdbcType=BIGINT}, 
      #{permissionValue,jdbcType=VARCHAR}, #{permissionType,jdbcType=TINYINT}, #{tenantId,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.AuthUserDataPermission" useGeneratedKeys="true">
    insert into auth_user_data_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="userId != null">
        user_id,
      </if>
      <if test="permissionValue != null">
        permission_value,
      </if>
      <if test="permissionType != null">
        permission_type,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="permissionValue != null">
        #{permissionValue,jdbcType=VARCHAR},
      </if>
      <if test="permissionType != null">
        #{permissionType,jdbcType=TINYINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.model.entity.AuthUserDataPermission">
    update auth_user_data_permission
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="permissionValue != null">
        permission_value = #{permissionValue,jdbcType=VARCHAR},
      </if>
      <if test="permissionType != null">
        permission_type = #{permissionType,jdbcType=TINYINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>


  <delete id="deleteByUserIdType">
    delete from auth_user_data_permission
    where   user_id = #{userId} and permission_type = #{permissionType}
  </delete>

  <insert id="batchAdd">
    insert into auth_user_data_permission (create_time, update_time, user_id,
    permission_value, permission_type,tenant_id)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP}, #{item.userId,jdbcType=BIGINT},
      #{item.permissionValue,jdbcType=VARCHAR}, #{item.permissionType,jdbcType=TINYINT}, #{item.tenantId,jdbcType=BIGINT}
      )
    </foreach>
  </insert>


  <select id="selectByUserIdType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_data_permission
    where   user_id = #{userId} and permission_type = #{permissionType}
  </select>


</mapper>