<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthUserPropertiesExtDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.client.dto.AuthUserPropertiesExt">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="prop_key" jdbcType="VARCHAR" property="propKey" />
    <result column="prop_value" jdbcType="VARCHAR" property="propValue" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, prop_key, prop_value, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auth_user_properties_ext
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_user_properties_ext
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthUserPropertiesExt" useGeneratedKeys="true">
    insert into auth_user_properties_ext (user_id, prop_key, prop_value, 
      create_time, update_time)
    values (#{userId,jdbcType=BIGINT}, #{propKey,jdbcType=VARCHAR}, #{propValue,jdbcType=VARCHAR}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthUserPropertiesExt" useGeneratedKeys="true">
    insert into auth_user_properties_ext
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="propKey != null">
        prop_key,
      </if>
      <if test="propValue != null">
        prop_value,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="propKey != null">
        #{propKey,jdbcType=VARCHAR},
      </if>
      <if test="propValue != null">
        #{propValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.client.dto.AuthUserPropertiesExt">
    update auth_user_properties_ext
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="propKey != null">
        prop_key = #{propKey,jdbcType=VARCHAR},
      </if>
      <if test="propValue != null">
        prop_value = #{propValue,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.client.dto.AuthUserPropertiesExt">
    update auth_user_properties_ext
    set user_id = #{userId,jdbcType=BIGINT},
      prop_key = #{propKey,jdbcType=VARCHAR},
      prop_value = #{propValue,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByLikeValue" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_properties_ext
    where prop_key = #{propKey} and prop_value like CONCAT('%',#{propValue},'%')
  </select>
  <select id="selectByValue" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_properties_ext
    where prop_key = #{propKey} and prop_value like CONCAT(#{propValue},'%')
  </select>
  <select id="selectALLByValue" parameterType="java.lang.String" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_properties_ext
    where prop_key = #{propKey} and prop_value like CONCAT(#{propValue},'%')
  </select>
  <select id="selectValue" resultType="net.xianmu.authentication.client.dto.AuthUserPropertiesExt">
    select id, user_id userId, prop_key propKey, prop_value propValue, create_time createTime, update_time updateTime
    from auth_user_properties_ext
    where user_id = #{userId} and prop_key = #{propKey}
  </select>

  <select id="selectUserIdKeyValue" resultType="net.xianmu.authentication.client.dto.AuthUserPropertiesExt">
    select id, user_id userId, prop_key propKey, prop_value propValue, create_time createTime, update_time updateTime
    from auth_user_properties_ext
    where user_id = #{userId} and prop_key = #{propKey} and prop_value =#{propValue}
  </select>
  <update id="updateValue">
    update auth_user_properties_ext
    set prop_value = #{propValue}
    where user_id = #{userId} and prop_key = #{propKey}
  </update>

  <delete id="deleteByUserIdKeys">
    delete from auth_user_properties_ext
    where user_id = #{userId} and prop_key in
    <foreach collection="propKeys" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </delete>

  <delete id="deleteByUserIdKeyValue">
    delete from auth_user_properties_ext
    where user_id = #{userId} and prop_key = #{propKey} and prop_value = #{propValue}
  </delete>

  <delete id="deleteByAllUserIdsKey">
    delete from auth_user_properties_ext
    where user_id in
    <foreach collection="userIds" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
    and prop_key = #{propKey}
  </delete>



  <insert id="batchAdd">
    insert into auth_user_properties_ext (user_id, prop_key, prop_value,
    create_time, update_time)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.userId,jdbcType=BIGINT}, #{item.propKey,jdbcType=VARCHAR}, #{item.propValue,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP}, #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <update id="updateLastTime">
    update auth_user_properties_ext
    set update_time = sysdate()
    where user_id = #{userId} and prop_key = #{propKey} and prop_value like CONCAT(#{propValue},'%')
  </update>


  <select id="selectByUserIdsAndKey" resultType="net.xianmu.authentication.client.dto.AuthUserPropertiesExt">
    select id, user_id userId, prop_key propKey, prop_value propValue, create_time createTime, update_time updateTime
    from auth_user_properties_ext
    where  prop_key = #{propKey} and  user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectALLByKeyValue" resultType="net.xianmu.authentication.client.dto.AuthUserPropertiesExt">
    select id, user_id userId, prop_key propKey, prop_value propValue, create_time createTime, update_time updateTime
    from auth_user_properties_ext
    where  prop_key = #{propKey} and prop_value = #{propValue}
  </select>
</mapper>