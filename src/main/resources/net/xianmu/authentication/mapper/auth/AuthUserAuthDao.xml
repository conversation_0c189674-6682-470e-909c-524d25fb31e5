<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthUserAuthDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.client.dto.AuthUserAuth">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="auth_type" jdbcType="TINYINT" property="authType" />
    <result column="auth_id" jdbcType="VARCHAR" property="authId" />
    <result column="third_party_id" jdbcType="VARCHAR" property="thirdPartyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, auth_type, auth_id, create_time, update_time,third_party_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_auth
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_user_auth
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthUserAuth" useGeneratedKeys="true">
    insert into auth_user_auth (user_id, auth_type, auth_id,third_party_id,
      create_time, update_time)
    values (#{userId,jdbcType=BIGINT}, #{authType,jdbcType=TINYINT}, #{authId,jdbcType=VARCHAR}, #{thirdPartyId},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthUserAuth" useGeneratedKeys="true">
    insert into auth_user_auth
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="authType != null">
        auth_type,
      </if>
      <if test="authId != null">
        auth_id,
      </if>
      <if test="thirdPartyId != null">
        third_party_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=BIGINT},
      </if>
      <if test="authType != null">
        #{authType,jdbcType=TINYINT},
      </if>
      <if test="authId != null">
        #{authId,jdbcType=VARCHAR},
      </if>
      <if test="thirdPartyId != null">
        #{thirdPartyId},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.client.dto.AuthUserAuth">
    update auth_user_auth
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=BIGINT},
      </if>
      <if test="authType != null">
        auth_type = #{authType,jdbcType=TINYINT},
      </if>
      <if test="authId != null">
        auth_id = #{authId,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="thirdPartyId != null">
        third_party_id = #{thirdPartyId},
      </if>
      <if test="thirdPartyId != null">
        third_party_id = #{thirdPartyId},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.client.dto.AuthUserAuth">
    update auth_user_auth
    set user_id = #{userId,jdbcType=BIGINT},
      auth_type = #{authType,jdbcType=TINYINT},
      auth_id = #{authId,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      third_party_id = #{thirdPartyId}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectUserAuthRecord" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_auth
    where  auth_type = #{authType} and user_id = #{userId}
    order  by id desc
  </select>

  <select id="selectByOpenIdType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_auth
    where  auth_type = #{authType} and auth_id = #{openid}
  </select>

  <select id="selectBOByOpenIdType" resultType="net.xianmu.authentication.model.BO.AuthUserAuthBO">
    select
    a.id, a.user_id as userId, a.auth_type authType, a.auth_id authId ,a.third_party_id thirdPartyId,
    au.biz_user_id as bizUserId
    from auth_user_auth a
    join auth_user au  on a.user_id =  au.id   and  au.system_origin =#{systemOriginEnum}
    where  a.auth_type = #{authType} and   a.auth_id = #{openid}
  </select>

  <select id="selectBOByThirdIdType" resultType="net.xianmu.authentication.model.BO.AuthUserAuthBO">
    select
    a.id, a.user_id as userId, a.auth_type authType, a.auth_id authId ,a.third_party_id thirdPartyId,
    au.biz_user_id as bizUserId
    from auth_user_auth a
    join auth_user au  on a.user_id =  au.id   and  au.system_origin =#{systemOriginEnum}
    where  a.auth_type = #{authType} and   a.third_party_id = #{unionId}
  </select>

  <select id="selectByThirdIdType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_auth
    where  auth_type = #{authType} and third_party_id = #{unionId}
  </select>

  <delete id="deleteByAuthIdType" >
    delete
    from auth_user_auth
    where  auth_type = #{authType} and user_id = #{userId}
  </delete>

  <delete id="deleteByAuthIdsType" >
    delete
    from auth_user_auth
    where  auth_type = #{authType} and
    user_id in
    <foreach collection="userId" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </delete>


  <select id="selectBySystemOriginEnumUserBaseIds" resultType="net.xianmu.authentication.model.BO.AuthUserAuthBO">
    select
    a.id, a.user_id as userId, a.auth_type authType, a.auth_id authId ,a.third_party_id thirdPartyId,
    b.user_base_id as baseUserId
    from auth_user_auth a
    join
    (
    select u.id,u.user_base_id,u.system_origin from auth_user u where u.user_base_id in
      <foreach collection="userBaseIds" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
     and u.system_origin =#{systemOriginEnum}
    ) b
    on a.user_id = b.id
    where  a.auth_type = #{authType}
  </select>


  <select id="selectByUserIdOpenIdType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_auth
    where  auth_type = #{authType} and auth_id = #{openid} and user_id = #{userId}
  </select>
  <select id="batchQueryByType" resultType="net.xianmu.authentication.model.DTO.AuthUserAuthDTO">
    SELECT
      aub.id baseUserId,
      aub.phone,
      aub.nickname,
      aua.third_party_id thirdPartyId,
      au.biz_user_id bizUserId,
      au.id authUserId
    from auth_user_base aub
      left join auth_user au on au.system_origin = #{originType} and au.tenant_id = #{tenantId} and aub.id = au.user_base_id
      left JOIN auth_user_auth aua on au.id = aua.user_id and aua.auth_type = #{authType}
    <where>
      <choose>
        <when test="accountType == 0">
          and aub.id in
        </when>
        <when test="accountType == 1">
          and au.biz_user_id in
        </when>
        <when test="accountType == 2">
          and aub.phone in
        </when>
      </choose>
      <foreach collection="accountList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </where>
  </select>
  <select id="selectByThirdPartyId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_auth
    where  auth_type = #{authType} and third_party_id in
    <foreach collection="thirdPartyList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>


  <select id="selectByThirdPartyPage" resultType="net.xianmu.authentication.model.DTO.UserBaseThirdPartyDTO">
     select
    aub.id baseUserId,
    au.biz_user_id bizUserId,
    aub.phone,
    aub.nickname nickName,
    aua.auth_type authType,
    aua.auth_id authId,
    aua.third_party_id thirdPartyId
    from auth_user_auth aua
    join  auth_user au on  aua.user_id = au.id
    join  auth_user_base aub on au.user_base_id = aub.id
    where  aua.auth_type = #{authType} and au.system_origin = #{systemOriginEnum} and au.tenant_id = #{tenantId}
   order by   aua.id desc
  </select>



  <select id="selectByUserIdsType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_auth
    where  auth_type = #{authType} and user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>


  <insert id="batchAdd">
    insert into auth_user_auth (user_id, auth_type, auth_id, third_party_id,
    create_time, update_time)
    values
    <foreach collection="list" separator="," item="item">
      (#{item.userId,jdbcType=BIGINT}, #{item.authType,jdbcType=TINYINT}, #{item.authId,jdbcType=VARCHAR}, #{item.thirdPartyId},
      sysdate(),sysdate()
      )
    </foreach>
  </insert>


  <update id="updateTime" >
    update auth_user_auth
    set update_time = sysdate()
    where user_id = #{userId} and auth_type=#{authType} and auth_id=#{openid}
  </update>

<!--  List<AuthUserAuth>  selectByBizIdSourceTenantIdType
(@Param("systemOriginEnum")SystemOriginEnum systemOriginEnum,
@Param("tenantId") Long tenantId,
  @Param("bizUserIds") List<Long> bizUserIds,
  @Param("authType") AuthTypeEnum authType);
  -->

  <select id="selectByBizIdSourceTenantIdType"  resultType="net.xianmu.authentication.model.BO.AuthUserAuthBO">
    select
    aua.user_id userId, aua.auth_type authType, aua.auth_id authId ,aua.third_party_id thirdPartyId ,au.biz_user_id bizUserId
    from auth_user_auth aua
    join auth_user au on aua.user_id = au.id
    where au.system_origin=#{systemOriginEnum}
    and au.tenant_id=#{tenantId} and au.biz_user_id in
     <foreach collection="bizUserIds" item="item" open="(" close=")" separator=",">
        #{item}
     </foreach>
    and aua.auth_type = #{authType}
  </select>

</mapper>