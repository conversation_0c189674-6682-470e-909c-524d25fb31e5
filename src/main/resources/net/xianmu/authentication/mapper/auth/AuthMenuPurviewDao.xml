<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthMenuPurviewDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.client.dto.AuthMenuPurview">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="menu_name" jdbcType="VARCHAR" property="menuName" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="son_purview" jdbcType="VARCHAR" property="sonPurview" />
    <result column="system_origin" jdbcType="TINYINT" property="systemOrigin" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="last_updater" jdbcType="VARCHAR" property="lastUpdater" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="purview_name" jdbcType="VARCHAR" property="purviewName" />
    <result column="weight" jdbcType="INTEGER" property="weight" />
    <result column="default_type" jdbcType="INTEGER" property="defaultType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, parent_id, menu_name, url, description, son_purview, system_origin, `type`, create_time, 
    last_updater, update_time, purview_name, weight,default_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auth_menu_purview
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_menu_purview
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthMenuPurview" useGeneratedKeys="true">
    insert into auth_menu_purview (parent_id, menu_name, url, 
      description, son_purview, system_origin, 
      `type`, create_time, last_updater, 
      update_time, purview_name, weight,default_type
      )
    values (#{parentId,jdbcType=INTEGER}, #{menuName,jdbcType=VARCHAR}, #{url,jdbcType=VARCHAR}, 
      #{description,jdbcType=VARCHAR}, #{sonPurview,jdbcType=VARCHAR}, #{systemOrigin,jdbcType=TINYINT}, 
      #{type,jdbcType=TINYINT}, #{createTime,jdbcType=TIMESTAMP}, #{lastUpdater,jdbcType=VARCHAR}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{purviewName,jdbcType=VARCHAR}, #{weight,jdbcType=INTEGER}, #{defaultType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthMenuPurview" useGeneratedKeys="true">
    insert into auth_menu_purview
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        parent_id,
      </if>
      <if test="menuName != null">
        menu_name,
      </if>
      <if test="url != null">
        url,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="sonPurview != null">
        son_purview,
      </if>
      <if test="systemOrigin != null">
        system_origin,
      </if>
      <if test="type != null">
        `type`,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="lastUpdater != null">
        last_updater,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="purviewName != null">
        purview_name,
      </if>
      <if test="weight != null">
        weight,
      </if>
      <if test="defaultType != null">
        default_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="parentId != null">
        #{parentId,jdbcType=INTEGER},
      </if>
      <if test="menuName != null">
        #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        #{url,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="sonPurview != null">
        #{sonPurview,jdbcType=VARCHAR},
      </if>
      <if test="systemOrigin != null">
        #{systemOrigin,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdater != null">
        #{lastUpdater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="purviewName != null">
        #{purviewName,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        #{weight,jdbcType=INTEGER},
      </if>
      <if test="defaultType != null">
        #{defaultType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.client.dto.AuthMenuPurview">
    update auth_menu_purview
    <set>
      <if test="parentId != null">
        parent_id = #{parentId,jdbcType=INTEGER},
      </if>
      <if test="menuName != null">
        menu_name = #{menuName,jdbcType=VARCHAR},
      </if>
      <if test="url != null">
        url = #{url,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="sonPurview != null">
        son_purview = #{sonPurview,jdbcType=VARCHAR},
      </if>
      <if test="systemOrigin != null">
        system_origin = #{systemOrigin,jdbcType=TINYINT},
      </if>
      <if test="type != null">
        `type` = #{type,jdbcType=TINYINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="lastUpdater != null">
        last_updater = #{lastUpdater,jdbcType=VARCHAR},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="purviewName != null">
        purview_name = #{purviewName,jdbcType=VARCHAR},
      </if>
      <if test="weight != null">
        weight = #{weight,jdbcType=INTEGER},
      </if>
      <if test="defaultType != null">
        default_type = #{defaultType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.client.dto.AuthMenuPurview">
    update auth_menu_purview
    set parent_id = #{parentId,jdbcType=INTEGER},
      menu_name = #{menuName,jdbcType=VARCHAR},
      url = #{url,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      son_purview = #{sonPurview,jdbcType=VARCHAR},
      system_origin = #{systemOrigin,jdbcType=TINYINT},
      `type` = #{type,jdbcType=TINYINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      last_updater = #{lastUpdater,jdbcType=VARCHAR},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      purview_name = #{purviewName,jdbcType=VARCHAR},
      default_type = #{defaultType,jdbcType=VARCHAR},
      weight = #{weight,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>


  <select id="selectByIds" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_menu_purview
    where id in
    <foreach collection="ids" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectRoleMenus"  resultMap="BaseResultMap">
    select
    a.id, a.parent_id, a.menu_name, a.url, a.description, a.son_purview, a.system_origin, a.`type`, a.create_time,
    a.last_updater, a.update_time, a.purview_name, a.weight
    from auth_menu_purview a
    join auth_role_purview b
    on a.id = b.purview_id
    where b.tenant_id = #{tenantId} and b.role_id = #{roleId}
    and a.system_origin=#{systemOrigin} and a.parent_id <![CDATA[ >= ]]> 0
    and a.default_type = 0
    order by a.weight asc,a.update_time desc
  </select>

  <select id="selectMenus"  resultMap="BaseResultMap">
    select
    a.id, a.parent_id, a.menu_name, a.url, a.description, a.son_purview, a.system_origin, a.`type`, a.create_time,
    a.last_updater, a.update_time, a.purview_name, a.weight
    from auth_menu_purview a
    where a.system_origin=#{systemOrigin}
    and a.default_type = 0
    order by a.weight asc,a.update_time desc
      </select>


  <select id="selectByRolIds"  resultMap="BaseResultMap">
    select distinct
    a.id, a.parent_id, a.menu_name, a.url, a.description, a.son_purview, a.system_origin, a.`type`, a.create_time,
    a.last_updater, a.update_time, a.purview_name, a.weight
    from auth_menu_purview a
    join auth_role_purview b
    on a.id = b.purview_id
    where a.system_origin=#{systemOrigin}
    and a.default_type = 0
    and b.role_id in
    <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    <if test="tenantId!=null">
      and b.tenant_id =#{tenantId}
    </if>
    order by a.weight asc,a.update_time desc

  </select>

  <select id="selectByName"  resultMap="BaseResultMap">
    select
    a.id, a.parent_id, a.menu_name, a.url, a.description, a.son_purview, a.system_origin, a.`type`, a.create_time,
    a.last_updater, a.update_time, a.purview_name, a.weight
    from auth_menu_purview a
    where a.system_origin=#{systemOrigin}
    and a.menu_name = #{menuName}
    limit 1
  </select>
  <select id="selectByUrl"  resultMap="BaseResultMap">
    select
      a.id, a.parent_id, a.menu_name, a.url, a.description, a.son_purview, a.system_origin, a.`type`, a.create_time,
      a.last_updater, a.update_time, a.purview_name, a.weight
    from auth_menu_purview a
    where a.system_origin=#{systemOrigin}
      and a.url = #{url}
      limit 1
  </select>

  <select id="countMenusByPid" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select count(1) from auth_menu_purview where parent_id  =#{purviewId}
  </select>

  <select id="selectMaxWeightBySystemOriginEnumParentId" resultType="java.lang.Integer">
    SELECT IFNULL(max(weight),0) from  auth_menu_purview where system_origin =#{systemOrigin} and parent_id = #{parentId}
  </select>


  <select id="selectBySourceParentId"  resultMap="BaseResultMap">
    select
    a.id, a.parent_id, a.menu_name, a.url, a.description, a.son_purview, a.system_origin, a.`type`, a.create_time,
    a.last_updater, a.update_time, a.purview_name, a.weight
    from auth_menu_purview a
    where a.system_origin=#{systemOrigin} and a.parent_id = #{parentId}
    <if test="weight==1">
      order by a.weight desc, a.id desc
    </if>
    <if test="weight ==-1">
      order by a.weight asc, a.id desc
    </if>
  </select>


  <select id="selectBySourceNameUrls"  resultMap="BaseResultMap">
    select
    a.id, a.parent_id, a.menu_name, a.url, a.description, a.son_purview, a.system_origin, a.`type`, a.create_time,
    a.last_updater, a.update_time, a.purview_name, a.weight
    from auth_menu_purview a
    where a.system_origin=#{systemOrigin}
    <if test="menus != null and menus!=''">
      and a.menu_name = #{menus}
    </if>
    <if test="urls != null and urls!=''">
      and a.url = #{urls}
    </if>
    limit 1
  </select>
</mapper>