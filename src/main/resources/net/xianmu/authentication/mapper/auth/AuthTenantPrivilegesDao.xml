<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthTenantPrivilegesDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.model.entity.AuthTenantPrivileges">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="menu_id" jdbcType="BIGINT" property="menuId" />
    <result column="expire_time" jdbcType="TIMESTAMP" property="expireTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, tenant_id, menu_id, expire_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auth_tenant_privileges
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_tenant_privileges
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.AuthTenantPrivileges" useGeneratedKeys="true">
    insert into auth_tenant_privileges (create_time, update_time, tenant_id, 
      menu_id, expire_time)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{tenantId,jdbcType=BIGINT}, 
      #{menuId,jdbcType=BIGINT}, #{expireTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.AuthTenantPrivileges" useGeneratedKeys="true">
    insert into auth_tenant_privileges
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="menuId != null">
        menu_id,
      </if>
      <if test="expireTime != null">
        expire_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="menuId != null">
        #{menuId,jdbcType=BIGINT},
      </if>
      <if test="expireTime != null">
        #{expireTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.model.entity.AuthTenantPrivileges">
    update auth_tenant_privileges
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="menuId != null">
        menu_id = #{menuId,jdbcType=BIGINT},
      </if>
      <if test="expireTime != null">
        expire_time = #{expireTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.model.entity.AuthTenantPrivileges">
    update auth_tenant_privileges
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      menu_id = #{menuId,jdbcType=BIGINT},
      expire_time = #{expireTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>



  <insert id="insertOrUpdateBatch" parameterType="list">
    INSERT INTO auth_tenant_privileges (
    create_time,
    update_time,
    tenant_id,
    menu_id,
    expire_time
    ) VALUES
    <foreach collection="list" item="record" separator=",">
      (
      sysdate(),
      sysdate(),
      #{record.tenantId},
      #{record.menuId},
      #{record.expireTime}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    expire_time = VALUES(expire_time)
  </insert>



  <delete id="deleteByTenantId" parameterType="java.lang.Long">
    delete from auth_tenant_privileges
    where  tenant_id = #{tenantId}
  </delete>


  <select id="selectByTenantId"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_tenant_privileges
    where tenant_id = #{tenantId}
  </select>
</mapper>