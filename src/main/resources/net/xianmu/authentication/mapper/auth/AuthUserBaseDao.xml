<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthUserBaseDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.client.dto.AuthUserBase">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="nickname" jdbcType="VARCHAR" property="nickname" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />

  </resultMap>
  <sql id="Base_Column_List">
    id, username, `password`, create_time, update_time, phone, email, nickname,
    logo
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_base
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_user_base
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthUserBase" useGeneratedKeys="true">
    insert into auth_user_base (username, `password`,
      create_time, update_time, phone, 
      email, nickname, logo
      )
    values (#{username,jdbcType=VARCHAR}, #{password,jdbcType=VARCHAR},
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{phone,jdbcType=VARCHAR}, 
      #{email,jdbcType=VARCHAR}, #{nickname,jdbcType=VARCHAR}, #{logo,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthUserBase" useGeneratedKeys="true">
    insert into auth_user_base
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="username != null">
        username,
      </if>
      <if test="password != null">
        `password`,
      </if>

      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="nickname != null">
        nickname,
      </if>
      <if test="logo != null">
        logo,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="logo != null">
        #{logo,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.client.dto.AuthUserBase">
    update auth_user_base
    <set>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        `password` = #{password,jdbcType=VARCHAR},
      </if>

      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="nickname != null">
        nickname = #{nickname,jdbcType=VARCHAR},
      </if>
      <if test="logo != null">
        logo = #{logo,jdbcType=VARCHAR},
      </if>

    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.client.dto.AuthUserBase">
    update auth_user_base
    set username = #{username,jdbcType=VARCHAR},
      `password` = #{password,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      phone = #{phone,jdbcType=VARCHAR},
      email = #{email,jdbcType=VARCHAR},
      nickname = #{nickname,jdbcType=VARCHAR},
      logo = #{logo,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByUserBase" parameterType="net.xianmu.authentication.client.dto.AuthUserBase" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_base
    <where>
      <if test="id != null">
        and id = #{id}
      </if>
      <if test="phone != null">
        and  phone = #{phone}
      </if>
      <if test="username != null">
       and   username = #{username}
      </if>
    </where>
  </select>


  <select id="selectByNameOrigin"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_base
    <where>
      username = #{username}
      limit 1
    </where>
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_base
      where id in
      <foreach collection="ids" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
  </select>

  <select id="selectUserBaseIdsBySourceRoleIds" resultType="java.lang.Long">
    select distinct a.id
    from auth_user_base  a
    join auth_user b     on a.id = b.user_base_id
    join auth_user_role c on b.id = c.user_id
    where c.role_id in
    <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
    and b.system_origin = #{systemOrigin}
  </select>

  <select id="selectAuthUserBySourceRoleIdsAndName" parameterType = "net.xianmu.authentication.model.input.AuthUserQueryInput" resultType="net.xianmu.authentication.model.VO.AuthUserVo">
    select a.nickname, a.username as userName, a.phone,
    b.id, b.system_origin as systemOrigin, b.user_base_id as userBaseId, b.tenant_id as tenantId, b.create_time as createTime, b.update_time as updateTime, b.`status`, b.biz_user_id as bizUserId
    from auth_user_base a
    join auth_user b on a.id = b.user_base_id
    <where>
      b.system_origin = #{systemOrigin}
      <if test="nickname != null and nickname != ''">
        and a.nickname like concat(#{nickname},'%')
      </if>
      <if test="nicknameLike != null and nicknameLike != ''">
        and a.nickname like concat('%',#{nicknameLike},'%')
      </if>
      <if test="userName != null and userName != ''">
        and a.username = #{userName}
      </if>
      <if test="status != null ">
        and b.status = #{status}
      </if>
      <if test="roleIds != null and roleIds.size > 0">
        and exists(select c.id from auth_user_role c where b.id = c.user_id and c.role_id in
        <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
        )
      </if>

      <if test="notExcludeRoleIds != null and notExcludeRoleIds.size > 0">
        and not exists(select c.id from auth_user_role c where b.id = c.user_id and c.role_id in
        <foreach collection="notExcludeRoleIds" item="item" open="(" close=")" separator=",">
          #{item}
        </foreach>
        )
      </if>
    </where>
    order by b.id desc
  </select>

  <select id="selectByPhoneList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_base
    where phone in
    <foreach collection="phoneList" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>
  <select id="selectWithOrigin" resultType="net.xianmu.authentication.model.DTO.AuthUserPhoneDTO">
    select
      au.id,
      au.user_base_id userBaseId,
      aub.phone,
      au.biz_user_id bizUserId
    from auth_user_base aub
      left join auth_user au on aub.id = au.user_base_id
    where au.system_origin = #{systemOrigin}
    order by aub.id asc
    limit #{offset}, #{pageSize}
  </select>

  <select id="selectWithOriginWithOutBigCustomer" resultType="net.xianmu.authentication.model.DTO.AuthUserPhoneDTO">
    select
      au.id,
      au.user_base_id userBaseId,
      aub.phone,
      au.biz_user_id bizUserId
    from auth_user_base aub
      left join auth_user au on aub.id = au.user_base_id
      left join auth_user_role aur on au.id  = on aur.user_id
    where au.system_origin = #{systemOrigin}
    and aur.role_id != 14
    and au.`status` =0
    and aub.`status`  = 0
    order by aub.id asc
    limit #{offset}, #{pageSize}
  </select>



  <select id="selectByPhone" parameterType="net.xianmu.authentication.client.dto.AuthUserBase" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_base
    <where>
      phone = #{phone}
    </where>
  </select>


  <select id="selectByEmail" parameterType="net.xianmu.authentication.client.dto.AuthUserBase" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_base
    <where>
      email = #{email}
      limit 1
    </where>
  </select>


  <select id="queryAuthUserBase" parameterType="net.xianmu.authentication.client.dto.AuthUserBase" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_user_base
    <where>
      <if test="phone != null">
        and  phone = #{phone}
      </if>
      <if test="username != null">
        and   username = #{username}
      </if>
      <if test="username != null">
        and   username = #{username}
      </if>
      <if test="phoneList != null">
      and phone in
      <foreach collection="phoneList" item="item" open="(" close=")" separator=",">
        #{item}
      </foreach>
    </if>
    </where>
  </select>
</mapper>