<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthWechatRelationMapper">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.model.entity.AuthWechatRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="system_origin" jdbcType="TINYINT" property="systemOrigin" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="auth_type" jdbcType="TINYINT" property="authType" />
    <result column="openid" jdbcType="VARCHAR" property="openid" />
    <result column="unionid" jdbcType="VARCHAR" property="unionid" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, system_origin, tenant_id, auth_type, openid, unionid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auth_wechat_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_wechat_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.AuthWechatRelation" useGeneratedKeys="true">
    insert into auth_wechat_relation (create_time, update_time, system_origin, 
      tenant_id, auth_type, openid, 
      unionid)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{systemOrigin,jdbcType=TINYINT}, 
      #{tenantId,jdbcType=BIGINT}, #{authType,jdbcType=TINYINT}, #{openid,jdbcType=VARCHAR}, 
      #{unionid,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.AuthWechatRelation" useGeneratedKeys="true">
    insert into auth_wechat_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="systemOrigin != null">
        system_origin,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="authType != null">
        auth_type,
      </if>
      <if test="openid != null">
        openid,
      </if>
      <if test="unionid != null">
        unionid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="systemOrigin != null">
        #{systemOrigin,jdbcType=TINYINT},
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="authType != null">
        #{authType,jdbcType=TINYINT},
      </if>
      <if test="openid != null">
        #{openid,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        #{unionid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.model.entity.AuthWechatRelation">
    update auth_wechat_relation
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="systemOrigin != null">
        system_origin = #{systemOrigin,jdbcType=TINYINT},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="authType != null">
        auth_type = #{authType,jdbcType=TINYINT},
      </if>
      <if test="openid != null">
        openid = #{openid,jdbcType=VARCHAR},
      </if>
      <if test="unionid != null">
        unionid = #{unionid,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.model.entity.AuthWechatRelation">
    update auth_wechat_relation
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      system_origin = #{systemOrigin,jdbcType=TINYINT},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      auth_type = #{authType,jdbcType=TINYINT},
      openid = #{openid,jdbcType=VARCHAR},
      unionid = #{unionid,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchAdd" >
    insert into auth_wechat_relation (create_time, update_time, system_origin,
      tenant_id, auth_type, openid, unionid)
    values
    <foreach collection="list" separator="," item="item">
      (sysdate(),sysdate(),#{item.systemOrigin,jdbcType=TINYINT},
      #{item.tenantId,jdbcType=BIGINT}, #{item.authType,jdbcType=TINYINT}, #{item.openid,jdbcType=VARCHAR},
      #{item.unionid,jdbcType=VARCHAR})
    </foreach>
  </insert>

  <delete id="deleteList" parameterType="java.lang.Long">
    delete from auth_wechat_relation
    where system_origin =#{systemOrigin} and tenant_id = #{tenantId} and auth_type =#{authType}
    and openid in
    <foreach collection="openIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </delete>

  <select id="selectBySourceTenantIdAuthTypeUnionid"  resultType="string">
    select
     openid
    from auth_wechat_relation
    where system_origin =#{systemOrigin} and tenant_id = #{tenantId} and auth_type =#{authType} and unionid =#{unionid} order by id desc limit 1
  </select>

  <select id="selectBySourceTenantIdAuthTypeOpenid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_wechat_relation
    where system_origin =#{systemOrigin} and tenant_id = #{tenantId} and auth_type =#{authType} and openid =#{openId} limit 1
  </select>



</mapper>