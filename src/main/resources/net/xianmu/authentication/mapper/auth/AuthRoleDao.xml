<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthRoleDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.client.dto.AuthRole">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rolename" jdbcType="VARCHAR" property="rolename" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="last_updater" jdbcType="VARCHAR" property="lastUpdater" />
    <result column="system_origin" jdbcType="TINYINT" property="systemOrigin" />
    <result column="super_admin" jdbcType="TINYINT" property="superAdmin" />
  </resultMap>

  <resultMap id="BaseResultMapDto" type="net.xianmu.authentication.client.dto.AuthRoleDTO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rolename" jdbcType="VARCHAR" property="rolename" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="last_updater" jdbcType="VARCHAR" property="lastUpdater" />
    <result column="system_origin" jdbcType="TINYINT" property="systemOrigin" />
    <result column="super_admin" jdbcType="TINYINT" property="superAdmin" />
  </resultMap>

  <resultMap id="UserRoleMap" type="net.xianmu.authentication.model.DTO.AuthUserRoleIdDto">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="rolename" jdbcType="VARCHAR" property="rolename" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="last_updater" jdbcType="VARCHAR" property="lastUpdater" />
    <result column="system_origin" jdbcType="TINYINT" property="systemOrigin" />
    <result column="super_admin" jdbcType="TINYINT" property="superAdmin" />
    <result column="user_id" jdbcType="BIGINT" property="userId" />
  </resultMap>
  <sql id="Base_Column_List">
    id, rolename, remarks, create_time, update_time, tenant_id, last_updater, system_origin, 
    super_admin
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auth_role
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from auth_role
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthRole" useGeneratedKeys="true">
    insert into auth_role (rolename, remarks, create_time, 
      update_time, tenant_id, last_updater, 
      system_origin, super_admin)
    values (#{rolename,jdbcType=VARCHAR}, #{remarks,jdbcType=VARCHAR},sysdate(), sysdate(), #{tenantId,jdbcType=BIGINT}, #{lastUpdater,jdbcType=VARCHAR},
      #{systemOrigin,jdbcType=TINYINT}, #{superAdmin,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthRole" useGeneratedKeys="true">
    insert into auth_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="rolename != null">
        rolename,
      </if>
      <if test="remarks != null">
        remarks,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="tenantId != null">
        tenant_id,
      </if>
      <if test="lastUpdater != null">
        last_updater,
      </if>
      <if test="systemOrigin != null">
        system_origin,
      </if>
      <if test="superAdmin != null">
        super_admin,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="rolename != null">
        #{rolename,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        sysdate(),
      </if>
      <if test="updateTime != null">
        sysdate(),
      </if>
      <if test="tenantId != null">
        #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="lastUpdater != null">
        #{lastUpdater,jdbcType=VARCHAR},
      </if>
      <if test="systemOrigin != null">
        #{systemOrigin,jdbcType=TINYINT},
      </if>
      <if test="superAdmin != null">
        #{superAdmin,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.client.dto.AuthRole">
    update auth_role
    <set>
      <if test="rolename != null">
        rolename = #{rolename,jdbcType=VARCHAR},
      </if>
      <if test="remarks != null">
        remarks = #{remarks,jdbcType=VARCHAR},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="tenantId != null">
        tenant_id = #{tenantId,jdbcType=BIGINT},
      </if>
      <if test="lastUpdater != null">
        last_updater = #{lastUpdater,jdbcType=VARCHAR},
      </if>
      <if test="systemOrigin != null">
        system_origin = #{systemOrigin,jdbcType=TINYINT},
      </if>
      <if test="superAdmin != null">
        super_admin = #{superAdmin,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.client.dto.AuthRole">
    update auth_role
    set rolename = #{rolename,jdbcType=VARCHAR},
      remarks = #{remarks,jdbcType=VARCHAR},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tenant_id = #{tenantId,jdbcType=BIGINT},
      last_updater = #{lastUpdater,jdbcType=VARCHAR},
      system_origin = #{systemOrigin,jdbcType=TINYINT},
      super_admin = #{superAdmin,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <select id="selectAuthRoleQueryVO" resultMap="BaseResultMapDto"
          parameterType="net.xianmu.authentication.client.input.AuthRoleQueryVO">
    select
    <include refid="Base_Column_List"/>
    from auth_role
    <where>
      <if test="roleName != null and roleName!=''">
        AND rolename like concat('%',#{roleName},'%')
      </if>
      <if test="systemOrigin != null">
        and system_origin = #{systemOrigin}
      </if>
      <if test="tenantId != null">
        and tenant_id = #{tenantId}
      </if>
      <if test="roleId != null">
        and id = #{roleId}
      </if>
    </where>

<!--    <if test="sortList!=null and sortList.size()>0">-->
<!--      order by-->
<!--      <foreach collection="sortList" item="item" open="(" close=")" separator=",">-->
<!--        ${item}.orderBy  ${item}.sortBy-->
<!--      </foreach>-->

<!--    </if>-->
  </select>

  <select id="selectByUserId" resultMap="BaseResultMap">
    select
    a.id,  a.rolename,  a.remarks,  a.create_time,  a.update_time,  a.tenant_id,  a.last_updater,  a.system_origin,
    super_admin
    from auth_role a
    join auth_user_role b
    on a.id = b.role_id
    where b.user_id = #{userId}
    order by a.id desc
  </select>

  <select id="selectByUserIds" resultMap="UserRoleMap">
    select distinct
    a.id,b.user_id,a.rolename,  a.remarks,  a.create_time,  a.update_time,  a.tenant_id,  a.last_updater,  a.system_origin,
    super_admin
    from auth_role a
    join auth_user_role b
    on a.id = b.role_id
    where b.user_id in
    <foreach collection="userIds" item="item" open="(" close=")" separator=",">
    #{item}
   </foreach>
    order by a.id desc
  </select>

  <select id="selectRoleIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_role where  system_origin = #{systemOrigin}
  </select>

  <select id="selectBySourceTenantSuperRole" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_role
    where super_admin = 1  and system_origin = #{systemOrigin}  and tenant_id = #{tenantId} limit 1
  </select>

  <select id="selectByOriginTenantIdRoleName" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from auth_role
    where  system_origin = #{systemOrigin}  and tenant_id = #{tenantId}  and rolename =#{roleName} limit 1
  </select>


  <select id="selectByIds" resultMap="UserRoleMap">
    select
    <include refid="Base_Column_List" />
    from auth_role
    where id in
    <foreach collection="roleIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectRoleIdBySourceAndTenantId" resultType="java.lang.Integer">
    select
    id
    from auth_role
    where system_origin = #{systemOrigin}  and tenant_id = #{tenantId}
  </select>

  <select id="selectAuthIdByRoleId" resultType="java.lang.Long">
    select
      aur.user_id
    from auth_user_role aur
    join  auth_role ar
    on aur.role_id = ar.id
    where ar.system_origin = #{systemOrigin}  and ar.id = #{id} limit 1
  </select>
</mapper>