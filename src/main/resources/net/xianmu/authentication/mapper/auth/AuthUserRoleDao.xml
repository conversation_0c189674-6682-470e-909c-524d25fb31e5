<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.AuthUserRoleDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.client.dto.AuthUserRole">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="role_id" jdbcType="INTEGER" property="roleId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    id, user_id, role_id, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from auth_user_role
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from auth_user_role
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthUserRole" useGeneratedKeys="true">
    insert into auth_user_role (user_id, role_id, create_time, 
      update_time)
    values (#{userId,jdbcType=INTEGER}, #{roleId,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.client.dto.AuthUserRole" useGeneratedKeys="true">
    insert into auth_user_role
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        user_id,
      </if>
      <if test="roleId != null">
        role_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=INTEGER},
      </if>
      <if test="roleId != null">
        #{roleId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.client.dto.AuthUserRole">
    update auth_user_role
    <set>
      <if test="userId != null">
        user_id = #{userId,jdbcType=INTEGER},
      </if>
      <if test="roleId != null">
        role_id = #{roleId,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.client.dto.AuthUserRole">
    update auth_user_role
    set user_id = #{userId,jdbcType=INTEGER},
      role_id = #{roleId,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <select id="selectRoleIdByUserId" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select
    distinct role_id
    from auth_user_role
    where user_id = #{userId}
  </select>


  <insert id="batchAdd">
    insert into auth_user_role (user_id, role_id, create_time,
    update_time)
    values
    <foreach collection="list" separator="," item="item">
    (#{item.userId,jdbcType=INTEGER}, #{item.roleId,jdbcType=INTEGER}, #{item.createTime,jdbcType=TIMESTAMP},
    #{item.updateTime,jdbcType=TIMESTAMP})
    </foreach>
  </insert>

  <select id="selectRoleIdByUserIds" parameterType="java.lang.Long" resultType="java.lang.Integer">
    select
    distinct role_id
    from auth_user_role
    where user_id  in
    <foreach collection="list" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </select>

  <delete id="deleteByRoleId" parameterType="java.lang.Long">
    delete from auth_user_role
    where role_id = #{roleId,jdbcType=INTEGER}
  </delete>

  <delete id="deleteByUserIdRoleId" parameterType="java.lang.Long">
    delete from auth_user_role
    where user_id = #{userId,jdbcType=INTEGER}
  </delete>


  <select id="countUserByUserId" parameterType="java.lang.Long" resultType="net.xianmu.authentication.model.DTO.RoleUserCountDTO">
    select
    a.role_id as roleId, count(b.id) as userCount
    from auth_user_role a
    join auth_role b
    on a.role_id = b.id
    JOIN auth_user c
    on a.user_id = c.id
    where a.role_id  in
    <foreach collection="roleIds" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
     and c.status  = 0
    group by a.role_id
  </select>


  <select id="selectUserIdListByRoleId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select
    distinct user_id
    from auth_user_role
    where role_id  in
    <foreach collection="roleIds" open="(" separator="," close=")" item="item">
      #{item}
    </foreach>
  </select>


  <select id="countUserIdListByRoleId" parameterType="java.lang.Long" resultType="java.lang.Long">
    select
    count(1)
    from auth_user_role a
    join auth_user b
    on a.user_id= b.id
    where role_id  =#{roleId} and b.status = 0
  </select>

</mapper>