<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.auth.BusinessDataPermissionDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.model.entity.BusinessDataPermission">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="permission_value" jdbcType="VARCHAR" property="permissionValue" />
    <result column="permission_name" jdbcType="VARCHAR" property="permissionName" />
    <result column="permission_type" jdbcType="TINYINT" property="permissionType" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, permission_value, permission_name, permission_type
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from business_data_permission
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from business_data_permission
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.BusinessDataPermission" useGeneratedKeys="true">
    insert into business_data_permission (create_time, update_time, permission_value, 
      permission_name, permission_type)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{permissionValue,jdbcType=VARCHAR}, 
      #{permissionName,jdbcType=VARCHAR}, #{permissionType,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.BusinessDataPermission" useGeneratedKeys="true">
    insert into business_data_permission
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="permissionValue != null">
        permission_value,
      </if>
      <if test="permissionName != null">
        permission_name,
      </if>
      <if test="permissionType != null">
        permission_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="permissionValue != null">
        #{permissionValue,jdbcType=VARCHAR},
      </if>
      <if test="permissionName != null">
        #{permissionName,jdbcType=VARCHAR},
      </if>
      <if test="permissionType != null">
        #{permissionType,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.model.entity.BusinessDataPermission">
    update business_data_permission
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="permissionValue != null">
        permission_value = #{permissionValue,jdbcType=VARCHAR},
      </if>
      <if test="permissionName != null">
        permission_name = #{permissionName,jdbcType=VARCHAR},
      </if>
      <if test="permissionType != null">
        permission_type = #{permissionType,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.model.entity.BusinessDataPermission">
    update business_data_permission
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      permission_value = #{permissionValue,jdbcType=VARCHAR},
      permission_name = #{permissionName,jdbcType=VARCHAR},
      permission_type = #{permissionType,jdbcType=TINYINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="getBusinessDataPermissionByType"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from business_data_permission
    where permission_type = #{permissionType}
  </select>

  <select id="selectByValueType"  resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from business_data_permission
    where permission_type = #{permissionType} and
     permission_value = #{permissionValue}

  </select>
</mapper>