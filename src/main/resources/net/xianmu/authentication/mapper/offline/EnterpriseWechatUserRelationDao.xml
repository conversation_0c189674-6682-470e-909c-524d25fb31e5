<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="net.xianmu.authentication.mapper.offline.EnterpriseWechatUserRelationDao">
  <resultMap id="BaseResultMap" type="net.xianmu.authentication.model.entity.EnterpriseWechatUserRelation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="auth_id" jdbcType="BIGINT" property="authId" />
    <result column="biz_user_id" jdbcType="BIGINT" property="bizUserId" />
    <result column="third_party_id" jdbcType="VARCHAR" property="thirdPartyId" />
    <result column="qr_code_url" jdbcType="VARCHAR" property="qrCodeUrl" />
  </resultMap>
  <sql id="Base_Column_List">
    id, create_time, update_time, auth_id, biz_user_id, third_party_id,qr_code_url
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from enterprise_wechat_user_relation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from enterprise_wechat_user_relation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.EnterpriseWechatUserRelation" useGeneratedKeys="true">
    insert into enterprise_wechat_user_relation (create_time, update_time, auth_id, 
      biz_user_id, third_party_id)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{authId,jdbcType=BIGINT}, 
      #{bizUserId,jdbcType=BIGINT}, #{thirdPartyId,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="net.xianmu.authentication.model.entity.EnterpriseWechatUserRelation" useGeneratedKeys="true">
    insert into enterprise_wechat_user_relation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="authId != null">
        auth_id,
      </if>
      <if test="bizUserId != null">
        biz_user_id,
      </if>
      <if test="thirdPartyId != null">
        third_party_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authId != null">
        #{authId,jdbcType=BIGINT},
      </if>
      <if test="bizUserId != null">
        #{bizUserId,jdbcType=BIGINT},
      </if>
      <if test="thirdPartyId != null">
        #{thirdPartyId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="net.xianmu.authentication.model.entity.EnterpriseWechatUserRelation">
    update enterprise_wechat_user_relation
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="authId != null">
        auth_id = #{authId,jdbcType=BIGINT},
      </if>
      <if test="bizUserId != null">
        biz_user_id = #{bizUserId,jdbcType=BIGINT},
      </if>
      <if test="thirdPartyId != null">
        third_party_id = #{thirdPartyId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="net.xianmu.authentication.model.entity.EnterpriseWechatUserRelation">
    update enterprise_wechat_user_relation
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      auth_id = #{authId,jdbcType=BIGINT},
      biz_user_id = #{bizUserId,jdbcType=BIGINT},
      third_party_id = #{thirdPartyId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByPage" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from enterprise_wechat_user_relation
    order by id desc
    limit #{offset}, #{pageSize}
  </select>
</mapper>