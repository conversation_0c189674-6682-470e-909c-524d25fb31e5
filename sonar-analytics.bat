@echo off
setlocal enabledelayedexpansion

:: Fetch the latest changes from the remote repository
git fetch origin

:: Get the list of changed files
for /f "delims=" %%i in ('git diff --name-only origin/master...HEAD') do (
    set "file=%%i"
    :: Convert the list to a comma-separated string excluding Test*.java files
    echo !file! | findstr /r /c:".*\.java" >nul && (
        echo !file! | findstr /r /c:"Test.*\.java" >nul || (
            set "inclusions=!inclusions!!file!,"
        )
    )
)

:: Remove the trailing comma
if defined inclusions set "inclusions=!inclusions:~0,-1!"

echo about to sonar scan for these files:
echo !inclusions!

:: Check if the user wants to recompile the source code
set "recompile="
if "%1" == "compile" (
    echo do recompile first...
    set "recompile=clean compile"
)

:: Run the SonarQube analysis with the specified plugin version, recompiling source code
mvn %recompile% org.sonarsource.scanner.maven:sonar-maven-plugin:3.9.1.2184:sonar -Dsonar.inclusions="%inclusions%" ^
  -Dsonar.exclusions="**/Test*.java,**/IT*.java,**/Test*.class,**/IT*.class"